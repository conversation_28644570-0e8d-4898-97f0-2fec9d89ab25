{"ast": null, "code": "export const CallableInstance =\n/**\n * @type {new <Parameters extends Array<unknown>, Result>(property: string | symbol) => (...parameters: Parameters) => Result}\n */\n\n/** @type {unknown} */\n\n/**\n * @this {Function}\n * @param {string | symbol} property\n * @returns {(...parameters: Array<unknown>) => unknown}\n */\nfunction (property) {\n  const self = this;\n  const constr = self.constructor;\n  const proto = /** @type {Record<string | symbol, Function>} */\n  // Prototypes do exist.\n  // type-coverage:ignore-next-line\n  constr.prototype;\n  const value = proto[property];\n  /** @type {(...parameters: Array<unknown>) => unknown} */\n  const apply = function () {\n    return value.apply(apply, arguments);\n  };\n  Object.setPrototypeOf(apply, proto);\n\n  // Not needed for us in `unified`: we only call this on the `copy`\n  // function,\n  // and we don't need to add its fields (`length`, `name`)\n  // over.\n  // See also: GH-246.\n  // const names = Object.getOwnPropertyNames(value)\n  //\n  // for (const p of names) {\n  //   const descriptor = Object.getOwnPropertyDescriptor(value, p)\n  //   if (descriptor) Object.defineProperty(apply, p, descriptor)\n  // }\n\n  return apply;\n};", "map": {"version": 3, "names": ["CallableInstance", "property", "self", "constr", "constructor", "proto", "prototype", "value", "apply", "arguments", "Object", "setPrototypeOf"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/unified/lib/callable-instance.js"], "sourcesContent": ["export const CallableInstance =\n  /**\n   * @type {new <Parameters extends Array<unknown>, Result>(property: string | symbol) => (...parameters: Parameters) => Result}\n   */\n  (\n    /** @type {unknown} */\n    (\n      /**\n       * @this {Function}\n       * @param {string | symbol} property\n       * @returns {(...parameters: Array<unknown>) => unknown}\n       */\n      function (property) {\n        const self = this\n        const constr = self.constructor\n        const proto = /** @type {Record<string | symbol, Function>} */ (\n          // Prototypes do exist.\n          // type-coverage:ignore-next-line\n          constr.prototype\n        )\n        const value = proto[property]\n        /** @type {(...parameters: Array<unknown>) => unknown} */\n        const apply = function () {\n          return value.apply(apply, arguments)\n        }\n\n        Object.setPrototypeOf(apply, proto)\n\n        // Not needed for us in `unified`: we only call this on the `copy`\n        // function,\n        // and we don't need to add its fields (`length`, `name`)\n        // over.\n        // See also: GH-246.\n        // const names = Object.getOwnPropertyNames(value)\n        //\n        // for (const p of names) {\n        //   const descriptor = Object.getOwnPropertyDescriptor(value, p)\n        //   if (descriptor) Object.defineProperty(apply, p, descriptor)\n        // }\n\n        return apply\n      }\n    )\n  )\n"], "mappings": "AAAA,OAAO,MAAMA,gBAAgB;AAC3B;AACF;AACA;;AAEI;;AAEE;AACN;AACA;AACA;AACA;AACM,SAAAA,CAAUC,QAAQ,EAAE;EAClB,MAAMC,IAAI,GAAG,IAAI;EACjB,MAAMC,MAAM,GAAGD,IAAI,CAACE,WAAW;EAC/B,MAAMC,KAAK,GAAG;EACZ;EACA;EACAF,MAAM,CAACG,SACR;EACD,MAAMC,KAAK,GAAGF,KAAK,CAACJ,QAAQ,CAAC;EAC7B;EACA,MAAMO,KAAK,GAAG,SAAAA,CAAA,EAAY;IACxB,OAAOD,KAAK,CAACC,KAAK,CAACA,KAAK,EAAEC,SAAS,CAAC;EACtC,CAAC;EAEDC,MAAM,CAACC,cAAc,CAACH,KAAK,EAAEH,KAAK,CAAC;;EAEnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,OAAOG,KAAK;AACd,CAEH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}