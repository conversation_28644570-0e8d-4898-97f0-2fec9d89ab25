[{"C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\components\\Navbar.js": "3", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\components\\ProtectedRoute.js": "4", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\contexts\\AuthContext.js": "5", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Home.js": "6", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Register.js": "7", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\CreateArticle.js": "8", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Articles.js": "9", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\EditArticle.js": "10", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\ArticleDetail.js": "11", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Categories.js": "12", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Login.js": "13", "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Profile.js": "14"}, {"size": 254, "mtime": 1757006782740, "results": "15", "hashOfConfig": "16"}, {"size": 2082, "mtime": 1757006791105, "results": "17", "hashOfConfig": "16"}, {"size": 7382, "mtime": 1757006824592, "results": "18", "hashOfConfig": "16"}, {"size": 961, "mtime": 1757006832772, "results": "19", "hashOfConfig": "16"}, {"size": 2453, "mtime": 1757006801191, "results": "20", "hashOfConfig": "16"}, {"size": 7860, "mtime": 1757006860608, "results": "21", "hashOfConfig": "16"}, {"size": 5243, "mtime": 1757006892412, "results": "22", "hashOfConfig": "16"}, {"size": 7799, "mtime": 1757006978250, "results": "23", "hashOfConfig": "16"}, {"size": 9475, "mtime": 1757006924556, "results": "24", "hashOfConfig": "16"}, {"size": 8585, "mtime": 1757007003467, "results": "25", "hashOfConfig": "16"}, {"size": 8605, "mtime": 1757006953684, "results": "26", "hashOfConfig": "16"}, {"size": 3035, "mtime": 1757007018248, "results": "27", "hashOfConfig": "16"}, {"size": 3161, "mtime": 1757006874982, "results": "28", "hashOfConfig": "16"}, {"size": 7761, "mtime": 1757007045028, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "puwa4o", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\contexts\\AuthContext.js", ["72"], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\CreateArticle.js", [], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Articles.js", ["73"], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\EditArticle.js", ["74"], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\ArticleDetail.js", ["75"], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Categories.js", [], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\x\\frontend\\src\\pages\\Profile.js", [], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 27, "column": 6, "nodeType": "78", "endLine": 27, "endColumn": 8, "suggestions": "79"}, {"ruleId": "76", "severity": 1, "message": "80", "line": 31, "column": 6, "nodeType": "78", "endLine": 31, "endColumn": 15, "suggestions": "81"}, {"ruleId": "76", "severity": 1, "message": "82", "line": 26, "column": 6, "nodeType": "78", "endLine": 26, "endColumn": 10, "suggestions": "83"}, {"ruleId": "76", "severity": 1, "message": "84", "line": 29, "column": 6, "nodeType": "78", "endLine": 29, "endColumn": 10, "suggestions": "85"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUser'. Either include it or remove the dependency array.", "ArrayExpression", ["86"], "React Hook useEffect has a missing dependency: 'fetchArticles'. Either include it or remove the dependency array.", ["87"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["88"], "React Hook useEffect has missing dependencies: 'fetchArticle' and 'fetchComments'. Either include them or remove the dependency array.", ["89"], {"desc": "90", "fix": "91"}, {"desc": "92", "fix": "93"}, {"desc": "94", "fix": "95"}, {"desc": "96", "fix": "97"}, "Update the dependencies array to be: [fetchUser]", {"range": "98", "text": "99"}, "Update the dependencies array to be: [fetchArticles, filters]", {"range": "100", "text": "101"}, "Update the dependencies array to be: [fetchData, id]", {"range": "102", "text": "103"}, "Update the dependencies array to be: [fetchArticle, fetchComments, id]", {"range": "104", "text": "105"}, [743, 745], "[fetchUser]", [846, 855], "[fetchArticles, filters]", [692, 696], "[fetchData, id]", [826, 830], "[fetchArticle, fetchComments, id]"]