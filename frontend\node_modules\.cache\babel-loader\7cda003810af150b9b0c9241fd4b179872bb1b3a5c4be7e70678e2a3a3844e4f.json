{"ast": null, "code": "/**\n * @import {Schema as SchemaType, Space} from 'property-information'\n */\n\n/** @type {SchemaType} */\nexport class Schema {\n  /**\n   * @param {SchemaType['property']} property\n   *   Property.\n   * @param {SchemaType['normal']} normal\n   *   Normal.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Schema.\n   */\n  constructor(property, normal, space) {\n    this.normal = normal;\n    this.property = property;\n    if (space) {\n      this.space = space;\n    }\n  }\n}\nSchema.prototype.normal = {};\nSchema.prototype.property = {};\nSchema.prototype.space = undefined;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "constructor", "property", "normal", "space", "prototype", "undefined"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/property-information/lib/util/schema.js"], "sourcesContent": ["/**\n * @import {Schema as SchemaType, Space} from 'property-information'\n */\n\n/** @type {SchemaType} */\nexport class Schema {\n  /**\n   * @param {SchemaType['property']} property\n   *   Property.\n   * @param {SchemaType['normal']} normal\n   *   Normal.\n   * @param {Space | undefined} [space]\n   *   Space.\n   * @returns\n   *   Schema.\n   */\n  constructor(property, normal, space) {\n    this.normal = normal\n    this.property = property\n\n    if (space) {\n      this.space = space\n    }\n  }\n}\n\nSchema.prototype.normal = {}\nSchema.prototype.property = {}\nSchema.prototype.space = undefined\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,MAAM,CAAC;EAClB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACnC,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IAExB,IAAIE,KAAK,EAAE;MACT,IAAI,CAACA,KAAK,GAAGA,KAAK;IACpB;EACF;AACF;AAEAJ,MAAM,CAACK,SAAS,CAACF,MAAM,GAAG,CAAC,CAAC;AAC5BH,MAAM,CAACK,SAAS,CAACH,QAAQ,GAAG,CAAC,CAAC;AAC9BF,MAAM,CAACK,SAAS,CAACD,KAAK,GAAGE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}