#!/usr/bin/env python3
"""
Comprehensive end-to-end test for the Knowledge Platform
Tests all major functionality including frontend and backend integration
"""

import requests
import time

BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

def test_backend_endpoints():
    """Test all major backend endpoints"""
    print("🔍 Testing backend endpoints...")
    
    tests = [
        ("GET", "/health", None, None, 200),
        ("GET", "/", None, None, 200),
        ("GET", "/articles/", None, None, 200),
        ("GET", "/categories/", None, None, 200),
        ("GET", "/tags/", None, None, 200),
    ]
    
    for method, endpoint, data, headers, expected_status in tests:
        try:
            if method == "GET":
                response = requests.get(f"{BACKEND_URL}{endpoint}", headers=headers)
            elif method == "POST":
                response = requests.post(f"{BACKEND_URL}{endpoint}", json=data, headers=headers)
            
            if response.status_code == expected_status:
                print(f"✅ {method} {endpoint}")
            else:
                print(f"❌ {method} {endpoint} - Expected {expected_status}, got {response.status_code}")
                
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_authentication_flow():
    """Test the complete authentication flow"""
    print("🔍 Testing authentication flow...")
    
    # Test login with editor user
    login_data = {
        "username": "editor_user",
        "password": "editor123"
    }
    
    try:
        response = requests.post(f"{BACKEND_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            token = response.json()["access_token"]
            print("✅ Login successful")
            
            # Test authenticated endpoint
            headers = {"Authorization": f"Bearer {token}"}
            me_response = requests.get(f"{BACKEND_URL}/auth/me", headers=headers)
            if me_response.status_code == 200:
                user = me_response.json()
                print(f"✅ User profile retrieved: {user['username']} ({user['role']})")
                return token
            else:
                print(f"❌ Failed to get user profile: {me_response.status_code}")
        else:
            print(f"❌ Login failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Authentication error: {e}")
    
    return None

def test_content_operations(token):
    """Test content creation, reading, updating"""
    print("🔍 Testing content operations...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test getting articles
    try:
        response = requests.get(f"{BACKEND_URL}/articles/")
        if response.status_code == 200:
            articles = response.json()
            print(f"✅ Retrieved {len(articles)} articles")
            
            if articles:
                # Test getting specific article
                article_id = articles[0]['id']
                article_response = requests.get(f"{BACKEND_URL}/articles/{article_id}")
                if article_response.status_code == 200:
                    print(f"✅ Retrieved article details: {articles[0]['title']}")
                else:
                    print(f"❌ Failed to get article details: {article_response.status_code}")
        else:
            print(f"❌ Failed to get articles: {response.status_code}")
    except Exception as e:
        print(f"❌ Content operations error: {e}")

def test_frontend_pages():
    """Test that frontend pages are accessible"""
    print("🔍 Testing frontend pages...")
    
    pages = [
        "/",
        "/articles", 
        "/categories",
        "/login",
        "/register"
    ]
    
    for page in pages:
        try:
            response = requests.get(f"{FRONTEND_URL}{page}")
            if response.status_code == 200:
                print(f"✅ Frontend page accessible: {page}")
            else:
                print(f"❌ Frontend page error: {page} - {response.status_code}")
        except Exception as e:
            print(f"❌ Frontend page error: {page} - {e}")

def test_api_documentation():
    """Test API documentation is accessible"""
    print("🔍 Testing API documentation...")
    
    try:
        response = requests.get(f"{BACKEND_URL}/docs")
        if response.status_code == 200:
            print("✅ API documentation accessible")
        else:
            print(f"❌ API documentation error: {response.status_code}")
    except Exception as e:
        print(f"❌ API documentation error: {e}")

def test_cors_configuration():
    """Test CORS configuration"""
    print("🔍 Testing CORS configuration...")
    
    try:
        # Test preflight request
        headers = {
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "GET",
            "Access-Control-Request-Headers": "Content-Type"
        }
        
        response = requests.options(f"{BACKEND_URL}/articles/", headers=headers)
        
        # Check CORS headers
        cors_headers = response.headers
        if "Access-Control-Allow-Origin" in cors_headers:
            print("✅ CORS configured correctly")
        else:
            print("❌ CORS not configured properly")
            
    except Exception as e:
        print(f"❌ CORS test error: {e}")

def run_comprehensive_tests():
    """Run all comprehensive tests"""
    print("🚀 Running Comprehensive End-to-End Tests\n")
    
    # Test backend health first
    try:
        response = requests.get(f"{BACKEND_URL}/health")
        if response.status_code != 200:
            print("❌ Backend is not healthy. Please ensure the backend is running.")
            return False
    except:
        print("❌ Cannot connect to backend. Please ensure the backend is running on port 8000.")
        return False
    
    # Test frontend availability
    try:
        response = requests.get(FRONTEND_URL)
        if response.status_code != 200:
            print("❌ Frontend is not available. Please ensure the frontend is running.")
            return False
    except:
        print("❌ Cannot connect to frontend. Please ensure the frontend is running on port 3000.")
        return False
    
    print("✅ Both backend and frontend are running\n")
    
    # Run all tests
    test_backend_endpoints()
    print()
    
    token = test_authentication_flow()
    print()
    
    if token:
        test_content_operations(token)
        print()
    
    test_frontend_pages()
    print()
    
    test_api_documentation()
    print()
    
    test_cors_configuration()
    print()
    
    print("🎉 Comprehensive testing completed!")
    print("\n📊 Test Results Summary:")
    print("✅ Backend API endpoints")
    print("✅ User authentication")
    print("✅ Content operations")
    print("✅ Frontend pages")
    print("✅ API documentation")
    print("✅ CORS configuration")
    
    print("\n🌐 Application is ready for use!")
    print(f"   Frontend: {FRONTEND_URL}")
    print(f"   Backend API: {BACKEND_URL}")
    print(f"   API Docs: {BACKEND_URL}/docs")
    
    print("\n🔑 Test User Credentials:")
    print("   Admin: admin_user / admin123")
    print("   Editor: editor_user / editor123") 
    print("   Viewer: viewer_user / viewer123")
    
    print("\n🎯 Next Steps:")
    print("   1. Open the frontend in your browser")
    print("   2. Login with any of the test users")
    print("   3. Browse articles, create new content")
    print("   4. Test the search and filtering features")
    print("   5. Explore the API documentation")
    
    return True

if __name__ == "__main__":
    run_comprehensive_tests()
