{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownSpace } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const blockQuote = {\n  continuation: {\n    tokenize: tokenizeBlockQuoteContinuation\n  },\n  exit,\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   * Start of block quote.\n   *\n   * ```markdown\n   * > | > a\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.greaterThan) {\n      const state = self.containerState;\n      assert(state, 'expected `containerState` to be defined in container');\n      if (!state.open) {\n        effects.enter(types.blockQuote, {\n          _container: true\n        });\n        state.open = true;\n      }\n      effects.enter(types.blockQuotePrefix);\n      effects.enter(types.blockQuoteMarker);\n      effects.consume(code);\n      effects.exit(types.blockQuoteMarker);\n      return after;\n    }\n    return nok(code);\n  }\n\n  /**\n   * After `>`, before optional whitespace.\n   *\n   * ```markdown\n   * > | > a\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.blockQuotePrefixWhitespace);\n      effects.consume(code);\n      effects.exit(types.blockQuotePrefixWhitespace);\n      effects.exit(types.blockQuotePrefix);\n      return ok;\n    }\n    effects.exit(types.blockQuotePrefix);\n    return ok(code);\n  }\n}\n\n/**\n * Start of block quote continuation.\n *\n * ```markdown\n *   | > a\n * > | > b\n *     ^\n * ```\n *\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  const self = this;\n  return contStart;\n\n  /**\n   * Start of block quote continuation.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contStart(code) {\n    if (markdownSpace(code)) {\n      // Always populated by defaults.\n      assert(self.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n      return factorySpace(effects, contBefore, types.linePrefix, self.parser.constructs.disable.null.includes('codeIndented') ? undefined : constants.tabSize)(code);\n    }\n    return contBefore(code);\n  }\n\n  /**\n   * At `>`, after optional whitespace.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contBefore(code) {\n    return effects.attempt(blockQuote, ok, nok)(code);\n  }\n}\n\n/** @type {Exiter} */\nfunction exit(effects) {\n  effects.exit(types.blockQuote);\n}", "map": {"version": 3, "names": ["ok", "assert", "factorySpace", "markdownSpace", "codes", "constants", "types", "blockQuote", "continuation", "tokenize", "tokenizeBlockQuoteContinuation", "exit", "name", "tokenizeBlockQuoteStart", "effects", "nok", "self", "start", "code", "greaterThan", "state", "containerState", "open", "enter", "_container", "blockQuotePrefix", "blockQuoteMarker", "consume", "after", "blockQuotePrefixWhitespace", "contStart", "parser", "constructs", "disable", "null", "contBefore", "linePrefix", "includes", "undefined", "tabSize", "attempt"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-core-commonmark/dev/lib/block-quote.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const blockQuote = {\n  continuation: {tokenize: tokenizeBlockQuoteContinuation},\n  exit,\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of block quote.\n   *\n   * ```markdown\n   * > | > a\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.greaterThan) {\n      const state = self.containerState\n\n      assert(state, 'expected `containerState` to be defined in container')\n\n      if (!state.open) {\n        effects.enter(types.blockQuote, {_container: true})\n        state.open = true\n      }\n\n      effects.enter(types.blockQuotePrefix)\n      effects.enter(types.blockQuoteMarker)\n      effects.consume(code)\n      effects.exit(types.blockQuoteMarker)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>`, before optional whitespace.\n   *\n   * ```markdown\n   * > | > a\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.blockQuotePrefixWhitespace)\n      effects.consume(code)\n      effects.exit(types.blockQuotePrefixWhitespace)\n      effects.exit(types.blockQuotePrefix)\n      return ok\n    }\n\n    effects.exit(types.blockQuotePrefix)\n    return ok(code)\n  }\n}\n\n/**\n * Start of block quote continuation.\n *\n * ```markdown\n *   | > a\n * > | > b\n *     ^\n * ```\n *\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  const self = this\n\n  return contStart\n\n  /**\n   * Start of block quote continuation.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contStart(code) {\n    if (markdownSpace(code)) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      return factorySpace(\n        effects,\n        contBefore,\n        types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : constants.tabSize\n      )(code)\n    }\n\n    return contBefore(code)\n  }\n\n  /**\n   * At `>`, after optional whitespace.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contBefore(code) {\n    return effects.attempt(blockQuote, ok, nok)(code)\n  }\n}\n\n/** @type {Exiter} */\nfunction exit(effects) {\n  effects.exit(types.blockQuote)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,aAAa,QAAO,0BAA0B;AACtD,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,YAAY,EAAE;IAACC,QAAQ,EAAEC;EAA8B,CAAC;EACxDC,IAAI;EACJC,IAAI,EAAE,YAAY;EAClBH,QAAQ,EAAEI;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASA,uBAAuBA,CAACC,OAAO,EAAEd,EAAE,EAAEe,GAAG,EAAE;EACjD,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAKd,KAAK,CAACe,WAAW,EAAE;MAC9B,MAAMC,KAAK,GAAGJ,IAAI,CAACK,cAAc;MAEjCpB,MAAM,CAACmB,KAAK,EAAE,sDAAsD,CAAC;MAErE,IAAI,CAACA,KAAK,CAACE,IAAI,EAAE;QACfR,OAAO,CAACS,KAAK,CAACjB,KAAK,CAACC,UAAU,EAAE;UAACiB,UAAU,EAAE;QAAI,CAAC,CAAC;QACnDJ,KAAK,CAACE,IAAI,GAAG,IAAI;MACnB;MAEAR,OAAO,CAACS,KAAK,CAACjB,KAAK,CAACmB,gBAAgB,CAAC;MACrCX,OAAO,CAACS,KAAK,CAACjB,KAAK,CAACoB,gBAAgB,CAAC;MACrCZ,OAAO,CAACa,OAAO,CAACT,IAAI,CAAC;MACrBJ,OAAO,CAACH,IAAI,CAACL,KAAK,CAACoB,gBAAgB,CAAC;MACpC,OAAOE,KAAK;IACd;IAEA,OAAOb,GAAG,CAACG,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASU,KAAKA,CAACV,IAAI,EAAE;IACnB,IAAIf,aAAa,CAACe,IAAI,CAAC,EAAE;MACvBJ,OAAO,CAACS,KAAK,CAACjB,KAAK,CAACuB,0BAA0B,CAAC;MAC/Cf,OAAO,CAACa,OAAO,CAACT,IAAI,CAAC;MACrBJ,OAAO,CAACH,IAAI,CAACL,KAAK,CAACuB,0BAA0B,CAAC;MAC9Cf,OAAO,CAACH,IAAI,CAACL,KAAK,CAACmB,gBAAgB,CAAC;MACpC,OAAOzB,EAAE;IACX;IAEAc,OAAO,CAACH,IAAI,CAACL,KAAK,CAACmB,gBAAgB,CAAC;IACpC,OAAOzB,EAAE,CAACkB,IAAI,CAAC;EACjB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASR,8BAA8BA,CAACI,OAAO,EAAEd,EAAE,EAAEe,GAAG,EAAE;EACxD,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOc,SAAS;;EAEhB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,SAASA,CAACZ,IAAI,EAAE;IACvB,IAAIf,aAAa,CAACe,IAAI,CAAC,EAAE;MACvB;MACAjB,MAAM,CACJe,IAAI,CAACe,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,EACnC,yCACF,CAAC;MAED,OAAOhC,YAAY,CACjBY,OAAO,EACPqB,UAAU,EACV7B,KAAK,CAAC8B,UAAU,EAChBpB,IAAI,CAACe,MAAM,CAACC,UAAU,CAACC,OAAO,CAACC,IAAI,CAACG,QAAQ,CAAC,cAAc,CAAC,GACxDC,SAAS,GACTjC,SAAS,CAACkC,OAChB,CAAC,CAACrB,IAAI,CAAC;IACT;IAEA,OAAOiB,UAAU,CAACjB,IAAI,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiB,UAAUA,CAACjB,IAAI,EAAE;IACxB,OAAOJ,OAAO,CAAC0B,OAAO,CAACjC,UAAU,EAAEP,EAAE,EAAEe,GAAG,CAAC,CAACG,IAAI,CAAC;EACnD;AACF;;AAEA;AACA,SAASP,IAAIA,CAACG,OAAO,EAAE;EACrBA,OAAO,CAACH,IAAI,CAACL,KAAK,CAACC,UAAU,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}