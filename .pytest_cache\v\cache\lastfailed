{"backend/tests/test_articles.py::test_create_article_unauthorized": true, "backend/tests/test_articles.py::test_get_articles": true, "backend/tests/test_articles.py::test_get_article_by_id": true, "backend/tests/test_articles.py::test_get_nonexistent_article": true, "backend/tests/test_articles.py::test_delete_article": true, "backend/tests/test_auth.py::test_register_user": true, "backend/tests/test_auth.py::test_get_current_user_unauthorized": true, "backend/tests/test_auth.py::test_update_user_profile": true}