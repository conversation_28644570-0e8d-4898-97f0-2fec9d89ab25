from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from .. import schemas, crud, auth, models
from ..database import get_db

router = APIRouter(prefix="/articles", tags=["articles"])

@router.post("/", response_model=schemas.Article)
def create_article(
    article: schemas.ArticleCreate,
    current_user: models.User = Depends(auth.require_editor),
    db: Session = Depends(get_db)
):
    """Create a new article."""
    return crud.create_article(db=db, article=article, author_id=current_user.id)

@router.get("/", response_model=List[schemas.Article])
async def read_articles(
    skip: int = 0,
    limit: int = 100,
    category_id: Optional[int] = Query(None),
    tag_ids: Optional[str] = Query(None),  # Comma-separated tag IDs
    search: Optional[str] = Query(None),
    published_only: bool = True,
    db: Session = Depends(get_db),
    current_user: Optional[models.User] = Depends(auth.get_current_user_optional)
):
    """Get articles with filtering and search."""
    # Parse tag_ids if provided
    tag_id_list = None
    if tag_ids:
        try:
            tag_id_list = [int(id.strip()) for id in tag_ids.split(",") if id.strip()]
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid tag IDs format")
    
    # Admins and editors can see unpublished articles
    if current_user and current_user.role in ["admin", "editor"]:
        published_only = False
    
    return crud.get_articles(
        db=db,
        skip=skip,
        limit=limit,
        category_id=category_id,
        tag_ids=tag_id_list,
        search=search,
        published_only=published_only
    )

@router.get("/featured", response_model=List[schemas.Article])
def read_featured_articles(
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """Get featured articles."""
    return db.query(models.Article).filter(
        models.Article.is_featured == True,
        models.Article.is_published == True
    ).order_by(models.Article.created_at.desc()).limit(limit).all()

@router.get("/trending", response_model=List[schemas.Article])
def read_trending_articles(
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """Get trending articles (most viewed)."""
    return db.query(models.Article).filter(
        models.Article.is_published == True
    ).order_by(models.Article.view_count.desc()).limit(limit).all()

@router.get("/{article_id}", response_model=schemas.Article)
async def read_article(
    article_id: int,
    db: Session = Depends(get_db),
    current_user: Optional[models.User] = Depends(auth.get_current_user_optional)
):
    """Get a specific article."""
    article = crud.get_article(db, article_id=article_id)
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    # Check if user can view unpublished articles
    if not article.is_published:
        if not current_user or current_user.role not in ["admin", "editor"]:
            if not current_user or current_user.id != article.author_id:
                raise HTTPException(status_code=404, detail="Article not found")
    
    # Increment view count
    crud.increment_article_views(db, article_id)
    
    return article

@router.put("/{article_id}", response_model=schemas.Article)
def update_article(
    article_id: int,
    article_update: schemas.ArticleUpdate,
    current_user: models.User = Depends(auth.require_editor),
    db: Session = Depends(get_db)
):
    """Update an article."""
    article = crud.get_article(db, article_id)
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    # Check permissions: author or admin can edit
    if current_user.role != "admin" and current_user.id != article.author_id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    updated_article = crud.update_article(db, article_id, article_update, current_user.id)
    if not updated_article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    return updated_article

@router.delete("/{article_id}")
def delete_article(
    article_id: int,
    current_user: models.User = Depends(auth.require_editor),
    db: Session = Depends(get_db)
):
    """Delete an article."""
    article = crud.get_article(db, article_id)
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    # Check permissions: author or admin can delete
    if current_user.role != "admin" and current_user.id != article.author_id:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    
    db.delete(article)
    db.commit()
    return {"message": "Article deleted successfully"}

@router.get("/{article_id}/versions", response_model=List[schemas.ArticleVersion])
def read_article_versions(
    article_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all versions of an article."""
    article = crud.get_article(db, article_id)
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    return crud.get_article_versions(db, article_id)

@router.get("/{article_id}/comments", response_model=List[schemas.Comment])
def read_article_comments(
    article_id: int,
    db: Session = Depends(get_db)
):
    """Get comments for an article."""
    article = crud.get_article(db, article_id)
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    return crud.get_article_comments(db, article_id)

@router.post("/{article_id}/comments", response_model=schemas.Comment)
def create_article_comment(
    article_id: int,
    comment: schemas.CommentBase,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a comment on an article."""
    article = crud.get_article(db, article_id)
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    comment_create = schemas.CommentCreate(**comment.dict(), article_id=article_id)
    return crud.create_comment(db, comment_create, current_user.id)
