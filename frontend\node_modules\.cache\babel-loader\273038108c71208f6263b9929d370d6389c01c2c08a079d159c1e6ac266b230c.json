{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\x\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport { BookOpenIcon, ArrowTrendingUpIcon, StarIcon, EyeIcon, ClockIcon } from '@heroicons/react/24/outline';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [featuredArticles, setFeaturedArticles] = useState([]);\n  const [trendingArticles, setTrendingArticles] = useState([]);\n  const [recentArticles, setRecentArticles] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchHomeData();\n  }, []);\n  const fetchHomeData = async () => {\n    try {\n      const [featured, trending, recent] = await Promise.all([axios.get('/articles/featured?limit=3'), axios.get('/articles/trending?limit=5'), axios.get('/articles?limit=5')]);\n      setFeaturedArticles(featured.data);\n      setTrendingArticles(trending.data);\n      setRecentArticles(recent.data);\n    } catch (error) {\n      console.error('Failed to fetch home data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center min-h-64\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"text-center py-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl md:text-6xl font-bold mb-4\",\n        children: \"Knowledge Platform\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xl md:text-2xl mb-8 opacity-90\",\n        children: \"Collaborate, Share, and Discover Knowledge\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/articles\",\n          className: \"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\",\n          children: \"Browse Articles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/articles/create\",\n          className: \"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors\",\n          children: \"Create Article\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), featuredArticles.length > 0 && /*#__PURE__*/_jsxDEV(\"section\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(StarIcon, {\n          className: \"h-6 w-6 text-yellow-500 mr-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Featured Articles\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-3 gap-6\",\n        children: featuredArticles.map(article => /*#__PURE__*/_jsxDEV(Link, {\n          to: `/articles/${article.id}`,\n          className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold mb-2 text-gray-900 hover:text-blue-600\",\n            children: article.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-4 line-clamp-3\",\n            children: article.summary || article.content.substring(0, 150) + '...'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"By \", article.author.full_name || article.author.username]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(EyeIcon, {\n                className: \"h-4 w-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: article.view_count\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 17\n          }, this)]\n        }, article.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid lg:grid-cols-2 gap-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n            className: \"h-6 w-6 text-red-500 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Trending\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: trendingArticles.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: trendingArticles.map((article, index) => /*#__PURE__*/_jsxDEV(Link, {\n              to: `/articles/${article.id}`,\n              className: \"flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex-shrink-0 w-6 h-6 bg-red-500 text-white text-sm font-bold rounded-full flex items-center justify-center\",\n                children: index + 1\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 min-w-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-medium text-gray-900 hover:text-blue-600 truncate\",\n                  children: article.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4 text-sm text-gray-500 mt-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-1\",\n                    children: [/*#__PURE__*/_jsxDEV(EyeIcon, {\n                      className: \"h-4 w-4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: article.view_count\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 141,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"By \", article.author.username]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 21\n              }, this)]\n            }, article.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-center py-8\",\n            children: \"No trending articles yet\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(ClockIcon, {\n            className: \"h-6 w-6 text-green-500 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900\",\n            children: \"Recently Updated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: recentArticles.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: recentArticles.map(article => /*#__PURE__*/_jsxDEV(Link, {\n              to: `/articles/${article.id}`,\n              className: \"block p-3 rounded-lg hover:bg-gray-50 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-medium text-gray-900 hover:text-blue-600 mb-1\",\n                children: article.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"By \", article.author.username]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: formatDate(article.updated_at || article.created_at)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 21\n              }, this)]\n            }, article.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-center py-8\",\n            children: \"No recent articles\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"text-center py-12 bg-gray-100 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(BookOpenIcon, {\n        className: \"h-16 w-16 text-blue-600 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-3xl font-bold text-gray-900 mb-4\",\n        children: \"Start Contributing Today\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-lg text-gray-600 mb-6 max-w-2xl mx-auto\",\n        children: \"Join our community of knowledge contributors. Share your expertise, learn from others, and help build a comprehensive knowledge base.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/register\",\n        className: \"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\",\n        children: \"Get Started\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"rYPjZ5x2FF+LIn4exWuol9G7qdE=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "axios", "BookOpenIcon", "ArrowTrendingUpIcon", "StarIcon", "EyeIcon", "ClockIcon", "jsxDEV", "_jsxDEV", "Home", "_s", "featuredArticles", "setFeaturedArticles", "trendingArticles", "setTrendingArticles", "recentArticles", "setRecentArticles", "loading", "setLoading", "fetchHomeData", "featured", "trending", "recent", "Promise", "all", "get", "data", "error", "console", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "length", "map", "article", "id", "title", "summary", "content", "substring", "author", "full_name", "username", "view_count", "TrendingUpIcon", "index", "updated_at", "created_at", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/src/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  BookOpenIcon,\n  ArrowTrendingUpIcon,\n  StarIcon,\n  EyeIcon,\n  ClockIcon\n} from '@heroicons/react/24/outline';\n\nconst Home = () => {\n  const [featuredArticles, setFeaturedArticles] = useState([]);\n  const [trendingArticles, setTrendingArticles] = useState([]);\n  const [recentArticles, setRecentArticles] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchHomeData();\n  }, []);\n\n  const fetchHomeData = async () => {\n    try {\n      const [featured, trending, recent] = await Promise.all([\n        axios.get('/articles/featured?limit=3'),\n        axios.get('/articles/trending?limit=5'),\n        axios.get('/articles?limit=5')\n      ]);\n\n      setFeaturedArticles(featured.data);\n      setTrendingArticles(trending.data);\n      setRecentArticles(recent.data);\n    } catch (error) {\n      console.error('Failed to fetch home data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-12\">\n      {/* Hero Section */}\n      <section className=\"text-center py-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg\">\n        <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">\n          Knowledge Platform\n        </h1>\n        <p className=\"text-xl md:text-2xl mb-8 opacity-90\">\n          Collaborate, Share, and Discover Knowledge\n        </p>\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Link \n            to=\"/articles\" \n            className=\"bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors\"\n          >\n            Browse Articles\n          </Link>\n          <Link \n            to=\"/articles/create\" \n            className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors\"\n          >\n            Create Article\n          </Link>\n        </div>\n      </section>\n\n      {/* Featured Articles */}\n      {featuredArticles.length > 0 && (\n        <section>\n          <div className=\"flex items-center mb-6\">\n            <StarIcon className=\"h-6 w-6 text-yellow-500 mr-2\" />\n            <h2 className=\"text-2xl font-bold text-gray-900\">Featured Articles</h2>\n          </div>\n          <div className=\"grid md:grid-cols-3 gap-6\">\n            {featuredArticles.map((article) => (\n              <Link \n                key={article.id} \n                to={`/articles/${article.id}`}\n                className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6\"\n              >\n                <h3 className=\"text-xl font-semibold mb-2 text-gray-900 hover:text-blue-600\">\n                  {article.title}\n                </h3>\n                <p className=\"text-gray-600 mb-4 line-clamp-3\">\n                  {article.summary || article.content.substring(0, 150) + '...'}\n                </p>\n                <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                  <span>By {article.author.full_name || article.author.username}</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <EyeIcon className=\"h-4 w-4\" />\n                    <span>{article.view_count}</span>\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n        </section>\n      )}\n\n      <div className=\"grid lg:grid-cols-2 gap-8\">\n        {/* Trending Articles */}\n        <section>\n          <div className=\"flex items-center mb-6\">\n            <TrendingUpIcon className=\"h-6 w-6 text-red-500 mr-2\" />\n            <h2 className=\"text-2xl font-bold text-gray-900\">Trending</h2>\n          </div>\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            {trendingArticles.length > 0 ? (\n              <div className=\"space-y-4\">\n                {trendingArticles.map((article, index) => (\n                  <Link \n                    key={article.id} \n                    to={`/articles/${article.id}`}\n                    className=\"flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\"\n                  >\n                    <span className=\"flex-shrink-0 w-6 h-6 bg-red-500 text-white text-sm font-bold rounded-full flex items-center justify-center\">\n                      {index + 1}\n                    </span>\n                    <div className=\"flex-1 min-w-0\">\n                      <h3 className=\"font-medium text-gray-900 hover:text-blue-600 truncate\">\n                        {article.title}\n                      </h3>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-500 mt-1\">\n                        <div className=\"flex items-center space-x-1\">\n                          <EyeIcon className=\"h-4 w-4\" />\n                          <span>{article.view_count}</span>\n                        </div>\n                        <span>By {article.author.username}</span>\n                      </div>\n                    </div>\n                  </Link>\n                ))}\n              </div>\n            ) : (\n              <p className=\"text-gray-500 text-center py-8\">No trending articles yet</p>\n            )}\n          </div>\n        </section>\n\n        {/* Recent Articles */}\n        <section>\n          <div className=\"flex items-center mb-6\">\n            <ClockIcon className=\"h-6 w-6 text-green-500 mr-2\" />\n            <h2 className=\"text-2xl font-bold text-gray-900\">Recently Updated</h2>\n          </div>\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            {recentArticles.length > 0 ? (\n              <div className=\"space-y-4\">\n                {recentArticles.map((article) => (\n                  <Link \n                    key={article.id} \n                    to={`/articles/${article.id}`}\n                    className=\"block p-3 rounded-lg hover:bg-gray-50 transition-colors\"\n                  >\n                    <h3 className=\"font-medium text-gray-900 hover:text-blue-600 mb-1\">\n                      {article.title}\n                    </h3>\n                    <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                      <span>By {article.author.username}</span>\n                      <span>{formatDate(article.updated_at || article.created_at)}</span>\n                    </div>\n                  </Link>\n                ))}\n              </div>\n            ) : (\n              <p className=\"text-gray-500 text-center py-8\">No recent articles</p>\n            )}\n          </div>\n        </section>\n      </div>\n\n      {/* Call to Action */}\n      <section className=\"text-center py-12 bg-gray-100 rounded-lg\">\n        <BookOpenIcon className=\"h-16 w-16 text-blue-600 mx-auto mb-4\" />\n        <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n          Start Contributing Today\n        </h2>\n        <p className=\"text-lg text-gray-600 mb-6 max-w-2xl mx-auto\">\n          Join our community of knowledge contributors. Share your expertise, \n          learn from others, and help build a comprehensive knowledge base.\n        </p>\n        <Link \n          to=\"/register\" \n          className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors\"\n        >\n          Get Started\n        </Link>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,YAAY,EACZC,mBAAmB,EACnBC,QAAQ,EACRC,OAAO,EACPC,SAAS,QACJ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACe,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACiB,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdoB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAM,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACrDvB,KAAK,CAACwB,GAAG,CAAC,4BAA4B,CAAC,EACvCxB,KAAK,CAACwB,GAAG,CAAC,4BAA4B,CAAC,EACvCxB,KAAK,CAACwB,GAAG,CAAC,mBAAmB,CAAC,CAC/B,CAAC;MAEFb,mBAAmB,CAACQ,QAAQ,CAACM,IAAI,CAAC;MAClCZ,mBAAmB,CAACO,QAAQ,CAACK,IAAI,CAAC;MAClCV,iBAAiB,CAACM,MAAM,CAACI,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,UAAU,GAAIC,UAAU,IAAK;IACjC,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,IAAIlB,OAAO,EAAE;IACX,oBACET,OAAA;MAAK4B,SAAS,EAAC,2CAA2C;MAAAC,QAAA,eACxD7B,OAAA;QAAK4B,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,oBACEjC,OAAA;IAAK4B,SAAS,EAAC,YAAY;IAAAC,QAAA,gBAEzB7B,OAAA;MAAS4B,SAAS,EAAC,sFAAsF;MAAAC,QAAA,gBACvG7B,OAAA;QAAI4B,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAEpD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLjC,OAAA;QAAG4B,SAAS,EAAC,qCAAqC;QAAAC,QAAA,EAAC;MAEnD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJjC,OAAA;QAAK4B,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAC7D7B,OAAA,CAACR,IAAI;UACH0C,EAAE,EAAC,WAAW;UACdN,SAAS,EAAC,+FAA+F;UAAAC,QAAA,EAC1G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPjC,OAAA,CAACR,IAAI;UACH0C,EAAE,EAAC,kBAAkB;UACrBN,SAAS,EAAC,0HAA0H;UAAAC,QAAA,EACrI;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGT9B,gBAAgB,CAACgC,MAAM,GAAG,CAAC,iBAC1BnC,OAAA;MAAA6B,QAAA,gBACE7B,OAAA;QAAK4B,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC7B,OAAA,CAACJ,QAAQ;UAACgC,SAAS,EAAC;QAA8B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDjC,OAAA;UAAI4B,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACNjC,OAAA;QAAK4B,SAAS,EAAC,2BAA2B;QAAAC,QAAA,EACvC1B,gBAAgB,CAACiC,GAAG,CAAEC,OAAO,iBAC5BrC,OAAA,CAACR,IAAI;UAEH0C,EAAE,EAAE,aAAaG,OAAO,CAACC,EAAE,EAAG;UAC9BV,SAAS,EAAC,qEAAqE;UAAAC,QAAA,gBAE/E7B,OAAA;YAAI4B,SAAS,EAAC,8DAA8D;YAAAC,QAAA,EACzEQ,OAAO,CAACE;UAAK;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACLjC,OAAA;YAAG4B,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAC3CQ,OAAO,CAACG,OAAO,IAAIH,OAAO,CAACI,OAAO,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG;UAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACJjC,OAAA;YAAK4B,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACtE7B,OAAA;cAAA6B,QAAA,GAAM,KAAG,EAACQ,OAAO,CAACM,MAAM,CAACC,SAAS,IAAIP,OAAO,CAACM,MAAM,CAACE,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrEjC,OAAA;cAAK4B,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C7B,OAAA,CAACH,OAAO;gBAAC+B,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/BjC,OAAA;gBAAA6B,QAAA,EAAOQ,OAAO,CAACS;cAAU;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAhBDI,OAAO,CAACC,EAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAiBX,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACV,eAEDjC,OAAA;MAAK4B,SAAS,EAAC,2BAA2B;MAAAC,QAAA,gBAExC7B,OAAA;QAAA6B,QAAA,gBACE7B,OAAA;UAAK4B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7B,OAAA,CAAC+C,cAAc;YAACnB,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDjC,OAAA;YAAI4B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC/CxB,gBAAgB,CAAC8B,MAAM,GAAG,CAAC,gBAC1BnC,OAAA;YAAK4B,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBxB,gBAAgB,CAAC+B,GAAG,CAAC,CAACC,OAAO,EAAEW,KAAK,kBACnChD,OAAA,CAACR,IAAI;cAEH0C,EAAE,EAAE,aAAaG,OAAO,CAACC,EAAE,EAAG;cAC9BV,SAAS,EAAC,8EAA8E;cAAAC,QAAA,gBAExF7B,OAAA;gBAAM4B,SAAS,EAAC,6GAA6G;gBAAAC,QAAA,EAC1HmB,KAAK,GAAG;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACPjC,OAAA;gBAAK4B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B7B,OAAA;kBAAI4B,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnEQ,OAAO,CAACE;gBAAK;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACLjC,OAAA;kBAAK4B,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBACrE7B,OAAA;oBAAK4B,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1C7B,OAAA,CAACH,OAAO;sBAAC+B,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC/BjC,OAAA;sBAAA6B,QAAA,EAAOQ,OAAO,CAACS;oBAAU;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACNjC,OAAA;oBAAA6B,QAAA,GAAM,KAAG,EAACQ,OAAO,CAACM,MAAM,CAACE,QAAQ;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA,GAlBDI,OAAO,CAACC,EAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmBX,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENjC,OAAA;YAAG4B,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAC1E;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVjC,OAAA;QAAA6B,QAAA,gBACE7B,OAAA;UAAK4B,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7B,OAAA,CAACF,SAAS;YAAC8B,SAAS,EAAC;UAA6B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDjC,OAAA;YAAI4B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACNjC,OAAA;UAAK4B,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC/CtB,cAAc,CAAC4B,MAAM,GAAG,CAAC,gBACxBnC,OAAA;YAAK4B,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBtB,cAAc,CAAC6B,GAAG,CAAEC,OAAO,iBAC1BrC,OAAA,CAACR,IAAI;cAEH0C,EAAE,EAAE,aAAaG,OAAO,CAACC,EAAE,EAAG;cAC9BV,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBAEnE7B,OAAA;gBAAI4B,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,EAC/DQ,OAAO,CAACE;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACLjC,OAAA;gBAAK4B,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,gBACtE7B,OAAA;kBAAA6B,QAAA,GAAM,KAAG,EAACQ,OAAO,CAACM,MAAM,CAACE,QAAQ;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzCjC,OAAA;kBAAA6B,QAAA,EAAOR,UAAU,CAACgB,OAAO,CAACY,UAAU,IAAIZ,OAAO,CAACa,UAAU;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC;YAAA,GAVDI,OAAO,CAACC,EAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWX,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENjC,OAAA;YAAG4B,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACpE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGNjC,OAAA;MAAS4B,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBAC3D7B,OAAA,CAACN,YAAY;QAACkC,SAAS,EAAC;MAAsC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACjEjC,OAAA;QAAI4B,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAEtD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLjC,OAAA;QAAG4B,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAAC;MAG5D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJjC,OAAA,CAACR,IAAI;QACH0C,EAAE,EAAC,WAAW;QACdN,SAAS,EAAC,+FAA+F;QAAAC,QAAA,EAC1G;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAlMID,IAAI;AAAAkD,EAAA,GAAJlD,IAAI;AAoMV,eAAeA,IAAI;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}