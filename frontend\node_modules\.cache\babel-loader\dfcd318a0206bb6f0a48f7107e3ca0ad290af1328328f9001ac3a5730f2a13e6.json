{"ast": null, "code": "import { create } from './util/create.js';\nexport const xml = create({\n  properties: {\n    xmlBase: null,\n    xmlLang: null,\n    xmlSpace: null\n  },\n  space: 'xml',\n  transform(_, property) {\n    return 'xml:' + property.slice(3).toLowerCase();\n  }\n});", "map": {"version": 3, "names": ["create", "xml", "properties", "xmlBase", "xmlLang", "xmlSpace", "space", "transform", "_", "property", "slice", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/property-information/lib/xml.js"], "sourcesContent": ["import {create} from './util/create.js'\n\nexport const xml = create({\n  properties: {xmlBase: null, xmlLang: null, xmlSpace: null},\n  space: 'xml',\n  transform(_, property) {\n    return 'xml:' + property.slice(3).toLowerCase()\n  }\n})\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,kBAAkB;AAEvC,OAAO,MAAMC,GAAG,GAAGD,MAAM,CAAC;EACxBE,UAAU,EAAE;IAACC,OAAO,EAAE,IAAI;IAAEC,OAAO,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAI,CAAC;EAC1DC,KAAK,EAAE,KAAK;EACZC,SAASA,CAACC,CAAC,EAAEC,QAAQ,EAAE;IACrB,OAAO,MAAM,GAAGA,QAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACjD;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}