{"ast": null, "code": "import { values } from 'micromark-util-symbol';\n\n/**\n * Normalize an identifier (as found in references, definitions).\n *\n * Collapses markdown whitespace, trim, and then lower- and uppercase.\n *\n * Some characters are considered “uppercase”, such as U+03F4 (`ϴ`), but if their\n * lowercase counterpart (U+03B8 (`θ`)) is uppercased will result in a different\n * uppercase character (U+0398 (`Θ`)).\n * So, to get a canonical form, we perform both lower- and uppercase.\n *\n * Using uppercase last makes sure keys will never interact with default\n * prototypal values (such as `constructor`): nothing in the prototype of\n * `Object` is uppercase.\n *\n * @param {string} value\n *   Identifier to normalize.\n * @returns {string}\n *   Normalized identifier.\n */\nexport function normalizeIdentifier(value) {\n  return value\n  // Collapse markdown whitespace.\n  .replace(/[\\t\\n\\r ]+/g, values.space)\n  // Trim.\n  .replace(/^ | $/g, '')\n  // Some characters are considered “uppercase”, but if their lowercase\n  // counterpart is uppercased will result in a different uppercase\n  // character.\n  // Hence, to get that form, we perform both lower- and uppercase.\n  // Upper case makes sure keys will not interact with default prototypal\n  // methods: no method is uppercase.\n  .toLowerCase().toUpperCase();\n}", "map": {"version": 3, "names": ["values", "normalizeIdentifier", "value", "replace", "space", "toLowerCase", "toUpperCase"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-util-normalize-identifier/dev/index.js"], "sourcesContent": ["import {values} from 'micromark-util-symbol'\n\n/**\n * Normalize an identifier (as found in references, definitions).\n *\n * Collapses markdown whitespace, trim, and then lower- and uppercase.\n *\n * Some characters are considered “uppercase”, such as U+03F4 (`ϴ`), but if their\n * lowercase counterpart (U+03B8 (`θ`)) is uppercased will result in a different\n * uppercase character (U+0398 (`Θ`)).\n * So, to get a canonical form, we perform both lower- and uppercase.\n *\n * Using uppercase last makes sure keys will never interact with default\n * prototypal values (such as `constructor`): nothing in the prototype of\n * `Object` is uppercase.\n *\n * @param {string} value\n *   Identifier to normalize.\n * @returns {string}\n *   Normalized identifier.\n */\nexport function normalizeIdentifier(value) {\n  return (\n    value\n      // Collapse markdown whitespace.\n      .replace(/[\\t\\n\\r ]+/g, values.space)\n      // Trim.\n      .replace(/^ | $/g, '')\n      // Some characters are considered “uppercase”, but if their lowercase\n      // counterpart is uppercased will result in a different uppercase\n      // character.\n      // Hence, to get that form, we perform both lower- and uppercase.\n      // Upper case makes sure keys will not interact with default prototypal\n      // methods: no method is uppercase.\n      .toLowerCase()\n      .toUpperCase()\n  )\n}\n"], "mappings": "AAAA,SAAQA,MAAM,QAAO,uBAAuB;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACzC,OACEA;EACE;EAAA,CACCC,OAAO,CAAC,aAAa,EAAEH,MAAM,CAACI,KAAK;EACpC;EAAA,CACCD,OAAO,CAAC,QAAQ,EAAE,EAAE;EACrB;EACA;EACA;EACA;EACA;EACA;EAAA,CACCE,WAAW,CAAC,CAAC,CACbC,WAAW,CAAC,CAAC;AAEpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}