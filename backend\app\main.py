from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .database import engine
from . import models
from .routers import auth, articles, categories, tags, users

# Create database tables
models.Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="Knowledge Platform API",
    description="A collaborative knowledge platform API",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router)
app.include_router(articles.router)
app.include_router(categories.router)
app.include_router(tags.router)
app.include_router(users.router)

@app.get("/")
def read_root():
    """Root endpoint."""
    return {"message": "Welcome to Knowledge Platform API"}

@app.get("/health")
def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}
