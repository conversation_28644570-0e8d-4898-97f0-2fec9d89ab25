{"ast": null, "code": "import { codes, values } from 'micromark-util-symbol';\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCodePoint(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nexport function decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base);\n  if (\n  // C0 except for HT, LF, FF, CR, space.\n  code < codes.ht || code === codes.vt || code > codes.cr && code < codes.space ||\n  // Control character (DEL) of C0, and C1 controls.\n  code > codes.tilde && code < 160 ||\n  // Lone high surrogates and low surrogates.\n  code > 55_295 && code < 57_344 ||\n  // Noncharacters.\n  code > 64_975 && code < 65_008 || /* eslint-disable no-bitwise */\n  (code & 65_535) === 65_535 || (code & 65_535) === 65_534 || /* eslint-enable no-bitwise */\n  // Out of range\n  code > 1_114_111) {\n    return values.replacementCharacter;\n  }\n  return String.fromCodePoint(code);\n}", "map": {"version": 3, "names": ["codes", "values", "decodeNumericCharacterReference", "value", "base", "code", "Number", "parseInt", "ht", "vt", "cr", "space", "tilde", "replacementCharacter", "String", "fromCodePoint"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-util-decode-numeric-character-reference/dev/index.js"], "sourcesContent": ["import {codes, values} from 'micromark-util-symbol'\n\n/**\n * Turn the number (in string form as either hexa- or plain decimal) coming from\n * a numeric character reference into a character.\n *\n * Sort of like `String.fromCodePoint(Number.parseInt(value, base))`, but makes\n * non-characters and control characters safe.\n *\n * @param {string} value\n *   Value to decode.\n * @param {number} base\n *   Numeric base.\n * @returns {string}\n *   Character.\n */\nexport function decodeNumericCharacterReference(value, base) {\n  const code = Number.parseInt(value, base)\n\n  if (\n    // C0 except for HT, LF, FF, CR, space.\n    code < codes.ht ||\n    code === codes.vt ||\n    (code > codes.cr && code < codes.space) ||\n    // Control character (DEL) of C0, and C1 controls.\n    (code > codes.tilde && code < 160) ||\n    // Lone high surrogates and low surrogates.\n    (code > 55_295 && code < 57_344) ||\n    // Noncharacters.\n    (code > 64_975 && code < 65_008) ||\n    /* eslint-disable no-bitwise */\n    (code & 65_535) === 65_535 ||\n    (code & 65_535) === 65_534 ||\n    /* eslint-enable no-bitwise */\n    // Out of range\n    code > 1_114_111\n  ) {\n    return values.replacementCharacter\n  }\n\n  return String.fromCodePoint(code)\n}\n"], "mappings": "AAAA,SAAQA,KAAK,EAAEC,MAAM,QAAO,uBAAuB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,+BAA+BA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC3D,MAAMC,IAAI,GAAGC,MAAM,CAACC,QAAQ,CAACJ,KAAK,EAAEC,IAAI,CAAC;EAEzC;EACE;EACAC,IAAI,GAAGL,KAAK,CAACQ,EAAE,IACfH,IAAI,KAAKL,KAAK,CAACS,EAAE,IAChBJ,IAAI,GAAGL,KAAK,CAACU,EAAE,IAAIL,IAAI,GAAGL,KAAK,CAACW,KAAM;EACvC;EACCN,IAAI,GAAGL,KAAK,CAACY,KAAK,IAAIP,IAAI,GAAG,GAAI;EAClC;EACCA,IAAI,GAAG,MAAM,IAAIA,IAAI,GAAG,MAAO;EAChC;EACCA,IAAI,GAAG,MAAM,IAAIA,IAAI,GAAG,MAAO,IAChC;EACA,CAACA,IAAI,GAAG,MAAM,MAAM,MAAM,IAC1B,CAACA,IAAI,GAAG,MAAM,MAAM,MAAM,IAC1B;EACA;EACAA,IAAI,GAAG,SAAS,EAChB;IACA,OAAOJ,MAAM,CAACY,oBAAoB;EACpC;EAEA,OAAOC,MAAM,CAACC,aAAa,CAACV,IAAI,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}