{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnoteReference(state, node) {\n  const clobberPrefix = typeof state.options.clobberPrefix === 'string' ? state.options.clobberPrefix : 'user-content-';\n  const id = String(node.identifier).toUpperCase();\n  const safeId = normalizeUri(id.toLowerCase());\n  const index = state.footnoteOrder.indexOf(id);\n  /** @type {number} */\n  let counter;\n  let reuseCounter = state.footnoteCounts.get(id);\n  if (reuseCounter === undefined) {\n    reuseCounter = 0;\n    state.footnoteOrder.push(id);\n    counter = state.footnoteOrder.length;\n  } else {\n    counter = index + 1;\n  }\n  reuseCounter += 1;\n  state.footnoteCounts.set(id, reuseCounter);\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + clobberPrefix + 'fn-' + safeId,\n      id: clobberPrefix + 'fnref-' + safeId + (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{\n      type: 'text',\n      value: String(counter)\n    }]\n  };\n  state.patch(node, link);\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  };\n  state.patch(node, sup);\n  return state.applyData(node, sup);\n}", "map": {"version": 3, "names": ["normalizeUri", "footnoteReference", "state", "node", "clobberPrefix", "options", "id", "String", "identifier", "toUpperCase", "safeId", "toLowerCase", "index", "footnoteOrder", "indexOf", "counter", "reuseCounter", "footnoteCounts", "get", "undefined", "push", "length", "set", "link", "type", "tagName", "properties", "href", "dataFootnoteRef", "ariaDescribedBy", "children", "value", "patch", "sup", "applyData"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/mdast-util-to-hast/lib/handlers/footnote-reference.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnoteReference(state, node) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const id = String(node.identifier).toUpperCase()\n  const safeId = normalizeUri(id.toLowerCase())\n  const index = state.footnoteOrder.indexOf(id)\n  /** @type {number} */\n  let counter\n\n  let reuseCounter = state.footnoteCounts.get(id)\n\n  if (reuseCounter === undefined) {\n    reuseCounter = 0\n    state.footnoteOrder.push(id)\n    counter = state.footnoteOrder.length\n  } else {\n    counter = index + 1\n  }\n\n  reuseCounter += 1\n  state.footnoteCounts.set(id, reuseCounter)\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + clobberPrefix + 'fn-' + safeId,\n      id:\n        clobberPrefix +\n        'fnref-' +\n        safeId +\n        (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{type: 'text', value: String(counter)}]\n  }\n  state.patch(node, link)\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  }\n  state.patch(node, sup)\n  return state.applyData(node, sup)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,6BAA6B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC7C,MAAMC,aAAa,GACjB,OAAOF,KAAK,CAACG,OAAO,CAACD,aAAa,KAAK,QAAQ,GAC3CF,KAAK,CAACG,OAAO,CAACD,aAAa,GAC3B,eAAe;EACrB,MAAME,EAAE,GAAGC,MAAM,CAACJ,IAAI,CAACK,UAAU,CAAC,CAACC,WAAW,CAAC,CAAC;EAChD,MAAMC,MAAM,GAAGV,YAAY,CAACM,EAAE,CAACK,WAAW,CAAC,CAAC,CAAC;EAC7C,MAAMC,KAAK,GAAGV,KAAK,CAACW,aAAa,CAACC,OAAO,CAACR,EAAE,CAAC;EAC7C;EACA,IAAIS,OAAO;EAEX,IAAIC,YAAY,GAAGd,KAAK,CAACe,cAAc,CAACC,GAAG,CAACZ,EAAE,CAAC;EAE/C,IAAIU,YAAY,KAAKG,SAAS,EAAE;IAC9BH,YAAY,GAAG,CAAC;IAChBd,KAAK,CAACW,aAAa,CAACO,IAAI,CAACd,EAAE,CAAC;IAC5BS,OAAO,GAAGb,KAAK,CAACW,aAAa,CAACQ,MAAM;EACtC,CAAC,MAAM;IACLN,OAAO,GAAGH,KAAK,GAAG,CAAC;EACrB;EAEAI,YAAY,IAAI,CAAC;EACjBd,KAAK,CAACe,cAAc,CAACK,GAAG,CAAChB,EAAE,EAAEU,YAAY,CAAC;;EAE1C;EACA,MAAMO,IAAI,GAAG;IACXC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,GAAG;IACZC,UAAU,EAAE;MACVC,IAAI,EAAE,GAAG,GAAGvB,aAAa,GAAG,KAAK,GAAGM,MAAM;MAC1CJ,EAAE,EACAF,aAAa,GACb,QAAQ,GACRM,MAAM,IACLM,YAAY,GAAG,CAAC,GAAG,GAAG,GAAGA,YAAY,GAAG,EAAE,CAAC;MAC9CY,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,CAAC,gBAAgB;IACpC,CAAC;IACDC,QAAQ,EAAE,CAAC;MAACN,IAAI,EAAE,MAAM;MAAEO,KAAK,EAAExB,MAAM,CAACQ,OAAO;IAAC,CAAC;EACnD,CAAC;EACDb,KAAK,CAAC8B,KAAK,CAAC7B,IAAI,EAAEoB,IAAI,CAAC;;EAEvB;EACA,MAAMU,GAAG,GAAG;IACVT,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,CAAC,CAAC;IACdI,QAAQ,EAAE,CAACP,IAAI;EACjB,CAAC;EACDrB,KAAK,CAAC8B,KAAK,CAAC7B,IAAI,EAAE8B,GAAG,CAAC;EACtB,OAAO/B,KAAK,CAACgC,SAAS,CAAC/B,IAAI,EAAE8B,GAAG,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}