{"ast": null, "code": "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { decodeNamedCharacterReference } from 'decode-named-character-reference';\nimport { asciiAlphanumeric, asciiDigit, asciiHexDigit } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  const self = this;\n  let size = 0;\n  /** @type {number} */\n  let max;\n  /** @type {(code: Code) => boolean} */\n  let test;\n  return start;\n\n  /**\n   * Start of character reference.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *      ^\n   * > | a&#123;b\n   *      ^\n   * > | a&#x9;b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.ampersand, 'expected `&`');\n    effects.enter(types.characterReference);\n    effects.enter(types.characterReferenceMarker);\n    effects.consume(code);\n    effects.exit(types.characterReferenceMarker);\n    return open;\n  }\n\n  /**\n   * After `&`, at `#` for numeric references or alphanumeric for named\n   * references.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^\n   * > | a&#123;b\n   *       ^\n   * > | a&#x9;b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.characterReferenceMarkerNumeric);\n      effects.consume(code);\n      effects.exit(types.characterReferenceMarkerNumeric);\n      return numeric;\n    }\n    effects.enter(types.characterReferenceValue);\n    max = constants.characterReferenceNamedSizeMax;\n    test = asciiAlphanumeric;\n    return value(code);\n  }\n\n  /**\n   * After `#`, at `x` for hexadecimals or digit for decimals.\n   *\n   * ```markdown\n   * > | a&#123;b\n   *        ^\n   * > | a&#x9;b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function numeric(code) {\n    if (code === codes.uppercaseX || code === codes.lowercaseX) {\n      effects.enter(types.characterReferenceMarkerHexadecimal);\n      effects.consume(code);\n      effects.exit(types.characterReferenceMarkerHexadecimal);\n      effects.enter(types.characterReferenceValue);\n      max = constants.characterReferenceHexadecimalSizeMax;\n      test = asciiHexDigit;\n      return value;\n    }\n    effects.enter(types.characterReferenceValue);\n    max = constants.characterReferenceDecimalSizeMax;\n    test = asciiDigit;\n    return value(code);\n  }\n\n  /**\n   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.\n   *\n   * The character reference kind defines what and how many characters are\n   * allowed.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^^^\n   * > | a&#123;b\n   *        ^^^\n   * > | a&#x9;b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function value(code) {\n    if (code === codes.semicolon && size) {\n      const token = effects.exit(types.characterReferenceValue);\n      if (test === asciiAlphanumeric && !decodeNamedCharacterReference(self.sliceSerialize(token))) {\n        return nok(code);\n      }\n\n      // To do: `markdown-rs` uses a different name:\n      // `CharacterReferenceMarkerSemi`.\n      effects.enter(types.characterReferenceMarker);\n      effects.consume(code);\n      effects.exit(types.characterReferenceMarker);\n      effects.exit(types.characterReference);\n      return ok;\n    }\n    if (test(code) && size++ < max) {\n      effects.consume(code);\n      return value;\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "decodeNamedCharacterReference", "asciiAlphanumeric", "asciiDigit", "asciiHexDigit", "codes", "constants", "types", "characterReference", "name", "tokenize", "tokenizeCharacterReference", "effects", "nok", "self", "size", "max", "test", "start", "code", "ampersand", "enter", "character<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "consume", "exit", "open", "numberSign", "characterReferenceMarkerNumeric", "numeric", "characterReferenceValue", "characterReferenceNamedSizeMax", "value", "uppercaseX", "lowercaseX", "characterReferenceMarkerHexadecimal", "characterReferenceHexadecimalSizeMax", "characterReferenceDecimalSizeMax", "semicolon", "token", "sliceSerialize"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-core-commonmark/dev/lib/character-reference.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {\n  asciiAlphanumeric,\n  asciiDigit,\n  asciiHexDigit\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  const self = this\n  let size = 0\n  /** @type {number} */\n  let max\n  /** @type {(code: Code) => boolean} */\n  let test\n\n  return start\n\n  /**\n   * Start of character reference.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *      ^\n   * > | a&#123;b\n   *      ^\n   * > | a&#x9;b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.ampersand, 'expected `&`')\n    effects.enter(types.characterReference)\n    effects.enter(types.characterReferenceMarker)\n    effects.consume(code)\n    effects.exit(types.characterReferenceMarker)\n    return open\n  }\n\n  /**\n   * After `&`, at `#` for numeric references or alphanumeric for named\n   * references.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^\n   * > | a&#123;b\n   *       ^\n   * > | a&#x9;b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.characterReferenceMarkerNumeric)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerNumeric)\n      return numeric\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceNamedSizeMax\n    test = asciiAlphanumeric\n    return value(code)\n  }\n\n  /**\n   * After `#`, at `x` for hexadecimals or digit for decimals.\n   *\n   * ```markdown\n   * > | a&#123;b\n   *        ^\n   * > | a&#x9;b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function numeric(code) {\n    if (code === codes.uppercaseX || code === codes.lowercaseX) {\n      effects.enter(types.characterReferenceMarkerHexadecimal)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerHexadecimal)\n      effects.enter(types.characterReferenceValue)\n      max = constants.characterReferenceHexadecimalSizeMax\n      test = asciiHexDigit\n      return value\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceDecimalSizeMax\n    test = asciiDigit\n    return value(code)\n  }\n\n  /**\n   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.\n   *\n   * The character reference kind defines what and how many characters are\n   * allowed.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^^^\n   * > | a&#123;b\n   *        ^^^\n   * > | a&#x9;b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function value(code) {\n    if (code === codes.semicolon && size) {\n      const token = effects.exit(types.characterReferenceValue)\n\n      if (\n        test === asciiAlphanumeric &&\n        !decodeNamedCharacterReference(self.sliceSerialize(token))\n      ) {\n        return nok(code)\n      }\n\n      // To do: `markdown-rs` uses a different name:\n      // `CharacterReferenceMarkerSemi`.\n      effects.enter(types.characterReferenceMarker)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarker)\n      effects.exit(types.characterReference)\n      return ok\n    }\n\n    if (test(code) && size++ < max) {\n      effects.consume(code)\n      return value\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,6BAA6B,QAAO,kCAAkC;AAC9E,SACEC,iBAAiB,EACjBC,UAAU,EACVC,aAAa,QACR,0BAA0B;AACjC,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,OAAO,MAAMC,kBAAkB,GAAG;EAChCC,IAAI,EAAE,oBAAoB;EAC1BC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASA,0BAA0BA,CAACC,OAAO,EAAEb,EAAE,EAAEc,GAAG,EAAE;EACpD,MAAMC,IAAI,GAAG,IAAI;EACjB,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA,IAAIC,GAAG;EACP;EACA,IAAIC,IAAI;EAER,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBnB,MAAM,CAACmB,IAAI,KAAKd,KAAK,CAACe,SAAS,EAAE,cAAc,CAAC;IAChDR,OAAO,CAACS,KAAK,CAACd,KAAK,CAACC,kBAAkB,CAAC;IACvCI,OAAO,CAACS,KAAK,CAACd,KAAK,CAACe,wBAAwB,CAAC;IAC7CV,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;IACrBP,OAAO,CAACY,IAAI,CAACjB,KAAK,CAACe,wBAAwB,CAAC;IAC5C,OAAOG,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,IAAIA,CAACN,IAAI,EAAE;IAClB,IAAIA,IAAI,KAAKd,KAAK,CAACqB,UAAU,EAAE;MAC7Bd,OAAO,CAACS,KAAK,CAACd,KAAK,CAACoB,+BAA+B,CAAC;MACpDf,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBP,OAAO,CAACY,IAAI,CAACjB,KAAK,CAACoB,+BAA+B,CAAC;MACnD,OAAOC,OAAO;IAChB;IAEAhB,OAAO,CAACS,KAAK,CAACd,KAAK,CAACsB,uBAAuB,CAAC;IAC5Cb,GAAG,GAAGV,SAAS,CAACwB,8BAA8B;IAC9Cb,IAAI,GAAGf,iBAAiB;IACxB,OAAO6B,KAAK,CAACZ,IAAI,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASS,OAAOA,CAACT,IAAI,EAAE;IACrB,IAAIA,IAAI,KAAKd,KAAK,CAAC2B,UAAU,IAAIb,IAAI,KAAKd,KAAK,CAAC4B,UAAU,EAAE;MAC1DrB,OAAO,CAACS,KAAK,CAACd,KAAK,CAAC2B,mCAAmC,CAAC;MACxDtB,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBP,OAAO,CAACY,IAAI,CAACjB,KAAK,CAAC2B,mCAAmC,CAAC;MACvDtB,OAAO,CAACS,KAAK,CAACd,KAAK,CAACsB,uBAAuB,CAAC;MAC5Cb,GAAG,GAAGV,SAAS,CAAC6B,oCAAoC;MACpDlB,IAAI,GAAGb,aAAa;MACpB,OAAO2B,KAAK;IACd;IAEAnB,OAAO,CAACS,KAAK,CAACd,KAAK,CAACsB,uBAAuB,CAAC;IAC5Cb,GAAG,GAAGV,SAAS,CAAC8B,gCAAgC;IAChDnB,IAAI,GAAGd,UAAU;IACjB,OAAO4B,KAAK,CAACZ,IAAI,CAAC;EACpB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASY,KAAKA,CAACZ,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAKd,KAAK,CAACgC,SAAS,IAAItB,IAAI,EAAE;MACpC,MAAMuB,KAAK,GAAG1B,OAAO,CAACY,IAAI,CAACjB,KAAK,CAACsB,uBAAuB,CAAC;MAEzD,IACEZ,IAAI,KAAKf,iBAAiB,IAC1B,CAACD,6BAA6B,CAACa,IAAI,CAACyB,cAAc,CAACD,KAAK,CAAC,CAAC,EAC1D;QACA,OAAOzB,GAAG,CAACM,IAAI,CAAC;MAClB;;MAEA;MACA;MACAP,OAAO,CAACS,KAAK,CAACd,KAAK,CAACe,wBAAwB,CAAC;MAC7CV,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrBP,OAAO,CAACY,IAAI,CAACjB,KAAK,CAACe,wBAAwB,CAAC;MAC5CV,OAAO,CAACY,IAAI,CAACjB,KAAK,CAACC,kBAAkB,CAAC;MACtC,OAAOT,EAAE;IACX;IAEA,IAAIkB,IAAI,CAACE,IAAI,CAAC,IAAIJ,IAAI,EAAE,GAAGC,GAAG,EAAE;MAC9BJ,OAAO,CAACW,OAAO,CAACJ,IAAI,CAAC;MACrB,OAAOY,KAAK;IACd;IAEA,OAAOlB,GAAG,CAACM,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}