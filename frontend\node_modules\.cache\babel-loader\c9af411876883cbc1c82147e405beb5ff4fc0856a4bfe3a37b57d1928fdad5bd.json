{"ast": null, "code": "/**\n * @import {Extension} from 'micromark-util-types'\n */\n\nimport { attention, autolink, blockQuote, characterEscape, characterReference, codeFenced, codeIndented, codeText, definition, hardBreakEscape, headingAtx, htmlFlow, htmlText, labelEnd, labelStartImage, labelStartLink, lineEnding, list, setextUnderline, thematicBreak } from 'micromark-core-commonmark';\nimport { codes } from 'micromark-util-symbol';\nimport { resolver as resolveText } from './initialize/text.js';\n\n/** @satisfies {Extension['document']} */\nexport const document = {\n  [codes.asterisk]: list,\n  [codes.plusSign]: list,\n  [codes.dash]: list,\n  [codes.digit0]: list,\n  [codes.digit1]: list,\n  [codes.digit2]: list,\n  [codes.digit3]: list,\n  [codes.digit4]: list,\n  [codes.digit5]: list,\n  [codes.digit6]: list,\n  [codes.digit7]: list,\n  [codes.digit8]: list,\n  [codes.digit9]: list,\n  [codes.greaterThan]: blockQuote\n};\n\n/** @satisfies {Extension['contentInitial']} */\nexport const contentInitial = {\n  [codes.leftSquareBracket]: definition\n};\n\n/** @satisfies {Extension['flowInitial']} */\nexport const flowInitial = {\n  [codes.horizontalTab]: codeIndented,\n  [codes.virtualSpace]: codeIndented,\n  [codes.space]: codeIndented\n};\n\n/** @satisfies {Extension['flow']} */\nexport const flow = {\n  [codes.numberSign]: headingAtx,\n  [codes.asterisk]: thematicBreak,\n  [codes.dash]: [setextUnderline, thematicBreak],\n  [codes.lessThan]: htmlFlow,\n  [codes.equalsTo]: setextUnderline,\n  [codes.underscore]: thematicBreak,\n  [codes.graveAccent]: codeFenced,\n  [codes.tilde]: codeFenced\n};\n\n/** @satisfies {Extension['string']} */\nexport const string = {\n  [codes.ampersand]: characterReference,\n  [codes.backslash]: characterEscape\n};\n\n/** @satisfies {Extension['text']} */\nexport const text = {\n  [codes.carriageReturn]: lineEnding,\n  [codes.lineFeed]: lineEnding,\n  [codes.carriageReturnLineFeed]: lineEnding,\n  [codes.exclamationMark]: labelStartImage,\n  [codes.ampersand]: characterReference,\n  [codes.asterisk]: attention,\n  [codes.lessThan]: [autolink, htmlText],\n  [codes.leftSquareBracket]: labelStartLink,\n  [codes.backslash]: [hardBreakEscape, characterEscape],\n  [codes.rightSquareBracket]: labelEnd,\n  [codes.underscore]: attention,\n  [codes.graveAccent]: codeText\n};\n\n/** @satisfies {Extension['insideSpan']} */\nexport const insideSpan = {\n  null: [attention, resolveText]\n};\n\n/** @satisfies {Extension['attentionMarkers']} */\nexport const attentionMarkers = {\n  null: [codes.asterisk, codes.underscore]\n};\n\n/** @satisfies {Extension['disable']} */\nexport const disable = {\n  null: []\n};", "map": {"version": 3, "names": ["attention", "autolink", "blockQuote", "characterEscape", "characterReference", "codeFenced", "codeIndented", "codeText", "definition", "hardBreakEscape", "headingAtx", "htmlFlow", "htmlText", "labelEnd", "labelStartImage", "labelStartLink", "lineEnding", "list", "setextUnderline", "thematicBreak", "codes", "resolver", "resolveText", "document", "asterisk", "plusSign", "dash", "digit0", "digit1", "digit2", "digit3", "digit4", "digit5", "digit6", "digit7", "digit8", "digit9", "greaterThan", "contentInitial", "leftSquareBracket", "flowInitial", "horizontalTab", "virtualSpace", "space", "flow", "numberSign", "lessThan", "equalsTo", "underscore", "graveAccent", "tilde", "string", "ampersand", "backslash", "text", "carriageReturn", "lineFeed", "carriageReturnLineFeed", "exclamationMark", "rightSquareBracket", "insideSpan", "null", "attentionMarkers", "disable"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark/dev/lib/constructs.js"], "sourcesContent": ["/**\n * @import {Extension} from 'micromark-util-types'\n */\n\nimport {\n  attention,\n  autolink,\n  blockQuote,\n  characterEscape,\n  characterReference,\n  codeFenced,\n  codeIndented,\n  codeText,\n  definition,\n  hardBreakEscape,\n  headingAtx,\n  htmlFlow,\n  htmlText,\n  labelEnd,\n  labelStartImage,\n  labelStartLink,\n  lineEnding,\n  list,\n  setextUnderline,\n  thematicBreak\n} from 'micromark-core-commonmark'\nimport {codes} from 'micromark-util-symbol'\nimport {resolver as resolveText} from './initialize/text.js'\n\n/** @satisfies {Extension['document']} */\nexport const document = {\n  [codes.asterisk]: list,\n  [codes.plusSign]: list,\n  [codes.dash]: list,\n  [codes.digit0]: list,\n  [codes.digit1]: list,\n  [codes.digit2]: list,\n  [codes.digit3]: list,\n  [codes.digit4]: list,\n  [codes.digit5]: list,\n  [codes.digit6]: list,\n  [codes.digit7]: list,\n  [codes.digit8]: list,\n  [codes.digit9]: list,\n  [codes.greaterThan]: blockQuote\n}\n\n/** @satisfies {Extension['contentInitial']} */\nexport const contentInitial = {\n  [codes.leftSquareBracket]: definition\n}\n\n/** @satisfies {Extension['flowInitial']} */\nexport const flowInitial = {\n  [codes.horizontalTab]: codeIndented,\n  [codes.virtualSpace]: codeIndented,\n  [codes.space]: codeIndented\n}\n\n/** @satisfies {Extension['flow']} */\nexport const flow = {\n  [codes.numberSign]: headingAtx,\n  [codes.asterisk]: thematicBreak,\n  [codes.dash]: [setextUnderline, thematicBreak],\n  [codes.lessThan]: htmlFlow,\n  [codes.equalsTo]: setextUnderline,\n  [codes.underscore]: thematicBreak,\n  [codes.graveAccent]: codeFenced,\n  [codes.tilde]: codeFenced\n}\n\n/** @satisfies {Extension['string']} */\nexport const string = {\n  [codes.ampersand]: characterReference,\n  [codes.backslash]: characterEscape\n}\n\n/** @satisfies {Extension['text']} */\nexport const text = {\n  [codes.carriageReturn]: lineEnding,\n  [codes.lineFeed]: lineEnding,\n  [codes.carriageReturnLineFeed]: lineEnding,\n  [codes.exclamationMark]: labelStartImage,\n  [codes.ampersand]: characterReference,\n  [codes.asterisk]: attention,\n  [codes.lessThan]: [autolink, htmlText],\n  [codes.leftSquareBracket]: labelStartLink,\n  [codes.backslash]: [hardBreakEscape, characterEscape],\n  [codes.rightSquareBracket]: labelEnd,\n  [codes.underscore]: attention,\n  [codes.graveAccent]: codeText\n}\n\n/** @satisfies {Extension['insideSpan']} */\nexport const insideSpan = {null: [attention, resolveText]}\n\n/** @satisfies {Extension['attentionMarkers']} */\nexport const attentionMarkers = {null: [codes.asterisk, codes.underscore]}\n\n/** @satisfies {Extension['disable']} */\nexport const disable = {null: []}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SACEA,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,eAAe,EACfC,kBAAkB,EAClBC,UAAU,EACVC,YAAY,EACZC,QAAQ,EACRC,UAAU,EACVC,eAAe,EACfC,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRC,QAAQ,EACRC,eAAe,EACfC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,eAAe,EACfC,aAAa,QACR,2BAA2B;AAClC,SAAQC,KAAK,QAAO,uBAAuB;AAC3C,SAAQC,QAAQ,IAAIC,WAAW,QAAO,sBAAsB;;AAE5D;AACA,OAAO,MAAMC,QAAQ,GAAG;EACtB,CAACH,KAAK,CAACI,QAAQ,GAAGP,IAAI;EACtB,CAACG,KAAK,CAACK,QAAQ,GAAGR,IAAI;EACtB,CAACG,KAAK,CAACM,IAAI,GAAGT,IAAI;EAClB,CAACG,KAAK,CAACO,MAAM,GAAGV,IAAI;EACpB,CAACG,KAAK,CAACQ,MAAM,GAAGX,IAAI;EACpB,CAACG,KAAK,CAACS,MAAM,GAAGZ,IAAI;EACpB,CAACG,KAAK,CAACU,MAAM,GAAGb,IAAI;EACpB,CAACG,KAAK,CAACW,MAAM,GAAGd,IAAI;EACpB,CAACG,KAAK,CAACY,MAAM,GAAGf,IAAI;EACpB,CAACG,KAAK,CAACa,MAAM,GAAGhB,IAAI;EACpB,CAACG,KAAK,CAACc,MAAM,GAAGjB,IAAI;EACpB,CAACG,KAAK,CAACe,MAAM,GAAGlB,IAAI;EACpB,CAACG,KAAK,CAACgB,MAAM,GAAGnB,IAAI;EACpB,CAACG,KAAK,CAACiB,WAAW,GAAGnC;AACvB,CAAC;;AAED;AACA,OAAO,MAAMoC,cAAc,GAAG;EAC5B,CAAClB,KAAK,CAACmB,iBAAiB,GAAG/B;AAC7B,CAAC;;AAED;AACA,OAAO,MAAMgC,WAAW,GAAG;EACzB,CAACpB,KAAK,CAACqB,aAAa,GAAGnC,YAAY;EACnC,CAACc,KAAK,CAACsB,YAAY,GAAGpC,YAAY;EAClC,CAACc,KAAK,CAACuB,KAAK,GAAGrC;AACjB,CAAC;;AAED;AACA,OAAO,MAAMsC,IAAI,GAAG;EAClB,CAACxB,KAAK,CAACyB,UAAU,GAAGnC,UAAU;EAC9B,CAACU,KAAK,CAACI,QAAQ,GAAGL,aAAa;EAC/B,CAACC,KAAK,CAACM,IAAI,GAAG,CAACR,eAAe,EAAEC,aAAa,CAAC;EAC9C,CAACC,KAAK,CAAC0B,QAAQ,GAAGnC,QAAQ;EAC1B,CAACS,KAAK,CAAC2B,QAAQ,GAAG7B,eAAe;EACjC,CAACE,KAAK,CAAC4B,UAAU,GAAG7B,aAAa;EACjC,CAACC,KAAK,CAAC6B,WAAW,GAAG5C,UAAU;EAC/B,CAACe,KAAK,CAAC8B,KAAK,GAAG7C;AACjB,CAAC;;AAED;AACA,OAAO,MAAM8C,MAAM,GAAG;EACpB,CAAC/B,KAAK,CAACgC,SAAS,GAAGhD,kBAAkB;EACrC,CAACgB,KAAK,CAACiC,SAAS,GAAGlD;AACrB,CAAC;;AAED;AACA,OAAO,MAAMmD,IAAI,GAAG;EAClB,CAAClC,KAAK,CAACmC,cAAc,GAAGvC,UAAU;EAClC,CAACI,KAAK,CAACoC,QAAQ,GAAGxC,UAAU;EAC5B,CAACI,KAAK,CAACqC,sBAAsB,GAAGzC,UAAU;EAC1C,CAACI,KAAK,CAACsC,eAAe,GAAG5C,eAAe;EACxC,CAACM,KAAK,CAACgC,SAAS,GAAGhD,kBAAkB;EACrC,CAACgB,KAAK,CAACI,QAAQ,GAAGxB,SAAS;EAC3B,CAACoB,KAAK,CAAC0B,QAAQ,GAAG,CAAC7C,QAAQ,EAAEW,QAAQ,CAAC;EACtC,CAACQ,KAAK,CAACmB,iBAAiB,GAAGxB,cAAc;EACzC,CAACK,KAAK,CAACiC,SAAS,GAAG,CAAC5C,eAAe,EAAEN,eAAe,CAAC;EACrD,CAACiB,KAAK,CAACuC,kBAAkB,GAAG9C,QAAQ;EACpC,CAACO,KAAK,CAAC4B,UAAU,GAAGhD,SAAS;EAC7B,CAACoB,KAAK,CAAC6B,WAAW,GAAG1C;AACvB,CAAC;;AAED;AACA,OAAO,MAAMqD,UAAU,GAAG;EAACC,IAAI,EAAE,CAAC7D,SAAS,EAAEsB,WAAW;AAAC,CAAC;;AAE1D;AACA,OAAO,MAAMwC,gBAAgB,GAAG;EAACD,IAAI,EAAE,CAACzC,KAAK,CAACI,QAAQ,EAAEJ,KAAK,CAAC4B,UAAU;AAAC,CAAC;;AAE1E;AACA,OAAO,MAAMe,OAAO,GAAG;EAACF,IAAI,EAAE;AAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}