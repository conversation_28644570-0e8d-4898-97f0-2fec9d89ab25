{"ast": null, "code": "import { constants } from 'micromark-util-symbol';\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */\nexport class SpliceBuffer {\n  /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */\n  constructor(initial) {\n    /** @type {Array<T>} */\n    this.left = initial ? [...initial] : [];\n    /** @type {Array<T>} */\n    this.right = [];\n  }\n\n  /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */\n  get(index) {\n    if (index < 0 || index >= this.left.length + this.right.length) {\n      throw new RangeError('Cannot access index `' + index + '` in a splice buffer of size `' + (this.left.length + this.right.length) + '`');\n    }\n    if (index < this.left.length) return this.left[index];\n    return this.right[this.right.length - index + this.left.length - 1];\n  }\n\n  /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */\n  get length() {\n    return this.left.length + this.right.length;\n  }\n\n  /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  shift() {\n    this.setCursor(0);\n    return this.right.pop();\n  }\n\n  /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */\n  slice(start, end) {\n    /** @type {number} */\n    const stop = end === null || end === undefined ? Number.POSITIVE_INFINITY : end;\n    if (stop < this.left.length) {\n      return this.left.slice(start, stop);\n    }\n    if (start > this.left.length) {\n      return this.right.slice(this.right.length - stop + this.left.length, this.right.length - start + this.left.length).reverse();\n    }\n    return this.left.slice(start).concat(this.right.slice(this.right.length - stop + this.left.length).reverse());\n  }\n\n  /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */\n  splice(start, deleteCount, items) {\n    /** @type {number} */\n    const count = deleteCount || 0;\n    this.setCursor(Math.trunc(start));\n    const removed = this.right.splice(this.right.length - count, Number.POSITIVE_INFINITY);\n    if (items) chunkedPush(this.left, items);\n    return removed.reverse();\n  }\n\n  /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  pop() {\n    this.setCursor(Number.POSITIVE_INFINITY);\n    return this.left.pop();\n  }\n\n  /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  push(item) {\n    this.setCursor(Number.POSITIVE_INFINITY);\n    this.left.push(item);\n  }\n\n  /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  pushMany(items) {\n    this.setCursor(Number.POSITIVE_INFINITY);\n    chunkedPush(this.left, items);\n  }\n\n  /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshift(item) {\n    this.setCursor(0);\n    this.right.push(item);\n  }\n\n  /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshiftMany(items) {\n    this.setCursor(0);\n    chunkedPush(this.right, items.reverse());\n  }\n\n  /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */\n  setCursor(n) {\n    if (n === this.left.length || n > this.left.length && this.right.length === 0 || n < 0 && this.left.length === 0) return;\n    if (n < this.left.length) {\n      // Move cursor to the this.left\n      const removed = this.left.splice(n, Number.POSITIVE_INFINITY);\n      chunkedPush(this.right, removed.reverse());\n    } else {\n      // Move cursor to the this.right\n      const removed = this.right.splice(this.left.length + this.right.length - n, Number.POSITIVE_INFINITY);\n      chunkedPush(this.left, removed.reverse());\n    }\n  }\n}\n\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */\nfunction chunkedPush(list, right) {\n  /** @type {number} */\n  let chunkStart = 0;\n  if (right.length < constants.v8MaxSafeChunkSize) {\n    list.push(...right);\n  } else {\n    while (chunkStart < right.length) {\n      list.push(...right.slice(chunkStart, chunkStart + constants.v8MaxSafeChunkSize));\n      chunkStart += constants.v8MaxSafeChunkSize;\n    }\n  }\n}", "map": {"version": 3, "names": ["constants", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "initial", "left", "right", "get", "index", "length", "RangeError", "shift", "setCursor", "pop", "slice", "start", "end", "stop", "undefined", "Number", "POSITIVE_INFINITY", "reverse", "concat", "splice", "deleteCount", "items", "count", "Math", "trunc", "removed", "chunkedPush", "push", "item", "pushMany", "unshift", "unshiftMany", "n", "list", "chunkStart", "v8MaxSafeChunkSize"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js"], "sourcesContent": ["import {constants} from 'micromark-util-symbol'\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */\nexport class SpliceBuffer {\n  /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */\n  constructor(initial) {\n    /** @type {Array<T>} */\n    this.left = initial ? [...initial] : []\n    /** @type {Array<T>} */\n    this.right = []\n  }\n\n  /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */\n  get(index) {\n    if (index < 0 || index >= this.left.length + this.right.length) {\n      throw new RangeError(\n        'Cannot access index `' +\n          index +\n          '` in a splice buffer of size `' +\n          (this.left.length + this.right.length) +\n          '`'\n      )\n    }\n\n    if (index < this.left.length) return this.left[index]\n    return this.right[this.right.length - index + this.left.length - 1]\n  }\n\n  /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */\n  get length() {\n    return this.left.length + this.right.length\n  }\n\n  /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  shift() {\n    this.setCursor(0)\n    return this.right.pop()\n  }\n\n  /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */\n  slice(start, end) {\n    /** @type {number} */\n    const stop =\n      end === null || end === undefined ? Number.POSITIVE_INFINITY : end\n\n    if (stop < this.left.length) {\n      return this.left.slice(start, stop)\n    }\n\n    if (start > this.left.length) {\n      return this.right\n        .slice(\n          this.right.length - stop + this.left.length,\n          this.right.length - start + this.left.length\n        )\n        .reverse()\n    }\n\n    return this.left\n      .slice(start)\n      .concat(\n        this.right.slice(this.right.length - stop + this.left.length).reverse()\n      )\n  }\n\n  /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */\n  splice(start, deleteCount, items) {\n    /** @type {number} */\n    const count = deleteCount || 0\n\n    this.setCursor(Math.trunc(start))\n    const removed = this.right.splice(\n      this.right.length - count,\n      Number.POSITIVE_INFINITY\n    )\n    if (items) chunkedPush(this.left, items)\n    return removed.reverse()\n  }\n\n  /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  pop() {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    return this.left.pop()\n  }\n\n  /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  push(item) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    this.left.push(item)\n  }\n\n  /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  pushMany(items) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    chunkedPush(this.left, items)\n  }\n\n  /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshift(item) {\n    this.setCursor(0)\n    this.right.push(item)\n  }\n\n  /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshiftMany(items) {\n    this.setCursor(0)\n    chunkedPush(this.right, items.reverse())\n  }\n\n  /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */\n  setCursor(n) {\n    if (\n      n === this.left.length ||\n      (n > this.left.length && this.right.length === 0) ||\n      (n < 0 && this.left.length === 0)\n    )\n      return\n    if (n < this.left.length) {\n      // Move cursor to the this.left\n      const removed = this.left.splice(n, Number.POSITIVE_INFINITY)\n      chunkedPush(this.right, removed.reverse())\n    } else {\n      // Move cursor to the this.right\n      const removed = this.right.splice(\n        this.left.length + this.right.length - n,\n        Number.POSITIVE_INFINITY\n      )\n      chunkedPush(this.left, removed.reverse())\n    }\n  }\n}\n\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */\nfunction chunkedPush(list, right) {\n  /** @type {number} */\n  let chunkStart = 0\n\n  if (right.length < constants.v8MaxSafeChunkSize) {\n    list.push(...right)\n  } else {\n    while (chunkStart < right.length) {\n      list.push(\n        ...right.slice(chunkStart, chunkStart + constants.v8MaxSafeChunkSize)\n      )\n      chunkStart += constants.v8MaxSafeChunkSize\n    }\n  }\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,uBAAuB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,CAAC;EACxB;AACF;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAO,EAAE;IACnB;IACA,IAAI,CAACC,IAAI,GAAGD,OAAO,GAAG,CAAC,GAAGA,OAAO,CAAC,GAAG,EAAE;IACvC;IACA,IAAI,CAACE,KAAK,GAAG,EAAE;EACjB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,GAAGA,CAACC,KAAK,EAAE;IACT,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACH,IAAI,CAACI,MAAM,GAAG,IAAI,CAACH,KAAK,CAACG,MAAM,EAAE;MAC9D,MAAM,IAAIC,UAAU,CAClB,uBAAuB,GACrBF,KAAK,GACL,gCAAgC,IAC/B,IAAI,CAACH,IAAI,CAACI,MAAM,GAAG,IAAI,CAACH,KAAK,CAACG,MAAM,CAAC,GACtC,GACJ,CAAC;IACH;IAEA,IAAID,KAAK,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,EAAE,OAAO,IAAI,CAACJ,IAAI,CAACG,KAAK,CAAC;IACrD,OAAO,IAAI,CAACF,KAAK,CAAC,IAAI,CAACA,KAAK,CAACG,MAAM,GAAGD,KAAK,GAAG,IAAI,CAACH,IAAI,CAACI,MAAM,GAAG,CAAC,CAAC;EACrE;;EAEA;AACF;AACA;AACA;EACE,IAAIA,MAAMA,CAAA,EAAG;IACX,OAAO,IAAI,CAACJ,IAAI,CAACI,MAAM,GAAG,IAAI,CAACH,KAAK,CAACG,MAAM;EAC7C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEE,KAAKA,CAAA,EAAG;IACN,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;IACjB,OAAO,IAAI,CAACN,KAAK,CAACO,GAAG,CAAC,CAAC;EACzB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAChB;IACA,MAAMC,IAAI,GACRD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,GAAGC,MAAM,CAACC,iBAAiB,GAAGJ,GAAG;IAEpE,IAAIC,IAAI,GAAG,IAAI,CAACZ,IAAI,CAACI,MAAM,EAAE;MAC3B,OAAO,IAAI,CAACJ,IAAI,CAACS,KAAK,CAACC,KAAK,EAAEE,IAAI,CAAC;IACrC;IAEA,IAAIF,KAAK,GAAG,IAAI,CAACV,IAAI,CAACI,MAAM,EAAE;MAC5B,OAAO,IAAI,CAACH,KAAK,CACdQ,KAAK,CACJ,IAAI,CAACR,KAAK,CAACG,MAAM,GAAGQ,IAAI,GAAG,IAAI,CAACZ,IAAI,CAACI,MAAM,EAC3C,IAAI,CAACH,KAAK,CAACG,MAAM,GAAGM,KAAK,GAAG,IAAI,CAACV,IAAI,CAACI,MACxC,CAAC,CACAY,OAAO,CAAC,CAAC;IACd;IAEA,OAAO,IAAI,CAAChB,IAAI,CACbS,KAAK,CAACC,KAAK,CAAC,CACZO,MAAM,CACL,IAAI,CAAChB,KAAK,CAACQ,KAAK,CAAC,IAAI,CAACR,KAAK,CAACG,MAAM,GAAGQ,IAAI,GAAG,IAAI,CAACZ,IAAI,CAACI,MAAM,CAAC,CAACY,OAAO,CAAC,CACxE,CAAC;EACL;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,MAAMA,CAACR,KAAK,EAAES,WAAW,EAAEC,KAAK,EAAE;IAChC;IACA,MAAMC,KAAK,GAAGF,WAAW,IAAI,CAAC;IAE9B,IAAI,CAACZ,SAAS,CAACe,IAAI,CAACC,KAAK,CAACb,KAAK,CAAC,CAAC;IACjC,MAAMc,OAAO,GAAG,IAAI,CAACvB,KAAK,CAACiB,MAAM,CAC/B,IAAI,CAACjB,KAAK,CAACG,MAAM,GAAGiB,KAAK,EACzBP,MAAM,CAACC,iBACT,CAAC;IACD,IAAIK,KAAK,EAAEK,WAAW,CAAC,IAAI,CAACzB,IAAI,EAAEoB,KAAK,CAAC;IACxC,OAAOI,OAAO,CAACR,OAAO,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACER,GAAGA,CAAA,EAAG;IACJ,IAAI,CAACD,SAAS,CAACO,MAAM,CAACC,iBAAiB,CAAC;IACxC,OAAO,IAAI,CAACf,IAAI,CAACQ,GAAG,CAAC,CAAC;EACxB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEkB,IAAIA,CAACC,IAAI,EAAE;IACT,IAAI,CAACpB,SAAS,CAACO,MAAM,CAACC,iBAAiB,CAAC;IACxC,IAAI,CAACf,IAAI,CAAC0B,IAAI,CAACC,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,QAAQA,CAACR,KAAK,EAAE;IACd,IAAI,CAACb,SAAS,CAACO,MAAM,CAACC,iBAAiB,CAAC;IACxCU,WAAW,CAAC,IAAI,CAACzB,IAAI,EAAEoB,KAAK,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACES,OAAOA,CAACF,IAAI,EAAE;IACZ,IAAI,CAACpB,SAAS,CAAC,CAAC,CAAC;IACjB,IAAI,CAACN,KAAK,CAACyB,IAAI,CAACC,IAAI,CAAC;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,WAAWA,CAACV,KAAK,EAAE;IACjB,IAAI,CAACb,SAAS,CAAC,CAAC,CAAC;IACjBkB,WAAW,CAAC,IAAI,CAACxB,KAAK,EAAEmB,KAAK,CAACJ,OAAO,CAAC,CAAC,CAAC;EAC1C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACET,SAASA,CAACwB,CAAC,EAAE;IACX,IACEA,CAAC,KAAK,IAAI,CAAC/B,IAAI,CAACI,MAAM,IACrB2B,CAAC,GAAG,IAAI,CAAC/B,IAAI,CAACI,MAAM,IAAI,IAAI,CAACH,KAAK,CAACG,MAAM,KAAK,CAAE,IAChD2B,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC/B,IAAI,CAACI,MAAM,KAAK,CAAE,EAEjC;IACF,IAAI2B,CAAC,GAAG,IAAI,CAAC/B,IAAI,CAACI,MAAM,EAAE;MACxB;MACA,MAAMoB,OAAO,GAAG,IAAI,CAACxB,IAAI,CAACkB,MAAM,CAACa,CAAC,EAAEjB,MAAM,CAACC,iBAAiB,CAAC;MAC7DU,WAAW,CAAC,IAAI,CAACxB,KAAK,EAAEuB,OAAO,CAACR,OAAO,CAAC,CAAC,CAAC;IAC5C,CAAC,MAAM;MACL;MACA,MAAMQ,OAAO,GAAG,IAAI,CAACvB,KAAK,CAACiB,MAAM,CAC/B,IAAI,CAAClB,IAAI,CAACI,MAAM,GAAG,IAAI,CAACH,KAAK,CAACG,MAAM,GAAG2B,CAAC,EACxCjB,MAAM,CAACC,iBACT,CAAC;MACDU,WAAW,CAAC,IAAI,CAACzB,IAAI,EAAEwB,OAAO,CAACR,OAAO,CAAC,CAAC,CAAC;IAC3C;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,WAAWA,CAACO,IAAI,EAAE/B,KAAK,EAAE;EAChC;EACA,IAAIgC,UAAU,GAAG,CAAC;EAElB,IAAIhC,KAAK,CAACG,MAAM,GAAGR,SAAS,CAACsC,kBAAkB,EAAE;IAC/CF,IAAI,CAACN,IAAI,CAAC,GAAGzB,KAAK,CAAC;EACrB,CAAC,MAAM;IACL,OAAOgC,UAAU,GAAGhC,KAAK,CAACG,MAAM,EAAE;MAChC4B,IAAI,CAACN,IAAI,CACP,GAAGzB,KAAK,CAACQ,KAAK,CAACwB,UAAU,EAAEA,UAAU,GAAGrC,SAAS,CAACsC,kBAAkB,CACtE,CAAC;MACDD,UAAU,IAAIrC,SAAS,CAACsC,kBAAkB;IAC5C;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}