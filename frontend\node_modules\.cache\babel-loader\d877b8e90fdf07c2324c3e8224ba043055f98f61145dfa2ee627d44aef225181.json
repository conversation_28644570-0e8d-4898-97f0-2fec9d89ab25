{"ast": null, "code": "/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { blankLine, content } from 'micromark-core-commonmark';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { codes, types } from 'micromark-util-symbol';\n\n/** @type {InitialConstruct} */\nexport const flow = {\n  tokenize: initializeFlow\n};\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeFlow(effects) {\n  const self = this;\n  const initial = effects.attempt(\n  // Try to parse a blank line.\n  blankLine, atBlankEnding,\n  // Try to parse initial flow (essentially, only code).\n  effects.attempt(this.parser.constructs.flowInitial, afterConstruct, factorySpace(effects, effects.attempt(this.parser.constructs.flow, afterConstruct, effects.attempt(content, afterConstruct)), types.linePrefix)));\n  return initial;\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    assert(code === codes.eof || markdownLineEnding(code), 'expected eol or eof');\n    if (code === codes.eof) {\n      effects.consume(code);\n      return;\n    }\n    effects.enter(types.lineEndingBlank);\n    effects.consume(code);\n    effects.exit(types.lineEndingBlank);\n    self.currentConstruct = undefined;\n    return initial;\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    assert(code === codes.eof || markdownLineEnding(code), 'expected eol or eof');\n    if (code === codes.eof) {\n      effects.consume(code);\n      return;\n    }\n    effects.enter(types.lineEnding);\n    effects.consume(code);\n    effects.exit(types.lineEnding);\n    self.currentConstruct = undefined;\n    return initial;\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "blankLine", "content", "factorySpace", "markdownLineEnding", "codes", "types", "flow", "tokenize", "initializeFlow", "effects", "self", "initial", "attempt", "atBlankEnding", "parser", "constructs", "flowInitial", "afterConstruct", "linePrefix", "code", "eof", "consume", "enter", "lineEndingBlank", "exit", "currentConstruct", "undefined", "lineEnding"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark/dev/lib/initialize/flow.js"], "sourcesContent": ["/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {blankLine, content} from 'micromark-core-commonmark'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const flow = {tokenize: initializeFlow}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeFlow(effects) {\n  const self = this\n  const initial = effects.attempt(\n    // Try to parse a blank line.\n    blankLine,\n    atBlankEnding,\n    // Try to parse initial flow (essentially, only code).\n    effects.attempt(\n      this.parser.constructs.flowInitial,\n      afterConstruct,\n      factorySpace(\n        effects,\n        effects.attempt(\n          this.parser.constructs.flow,\n          afterConstruct,\n          effects.attempt(content, afterConstruct)\n        ),\n        types.linePrefix\n      )\n    )\n  )\n\n  return initial\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEndingBlank)\n    effects.consume(code)\n    effects.exit(types.lineEndingBlank)\n    self.currentConstruct = undefined\n    return initial\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    self.currentConstruct = undefined\n    return initial\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,SAAS,EAAEC,OAAO,QAAO,2BAA2B;AAC5D,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,KAAK,EAAEC,KAAK,QAAO,uBAAuB;;AAElD;AACA,OAAO,MAAMC,IAAI,GAAG;EAACC,QAAQ,EAAEC;AAAc,CAAC;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,cAAcA,CAACC,OAAO,EAAE;EAC/B,MAAMC,IAAI,GAAG,IAAI;EACjB,MAAMC,OAAO,GAAGF,OAAO,CAACG,OAAO;EAC7B;EACAZ,SAAS,EACTa,aAAa;EACb;EACAJ,OAAO,CAACG,OAAO,CACb,IAAI,CAACE,MAAM,CAACC,UAAU,CAACC,WAAW,EAClCC,cAAc,EACdf,YAAY,CACVO,OAAO,EACPA,OAAO,CAACG,OAAO,CACb,IAAI,CAACE,MAAM,CAACC,UAAU,CAACT,IAAI,EAC3BW,cAAc,EACdR,OAAO,CAACG,OAAO,CAACX,OAAO,EAAEgB,cAAc,CACzC,CAAC,EACDZ,KAAK,CAACa,UACR,CACF,CACF,CAAC;EAED,OAAOP,OAAO;;EAEd;EACA,SAASE,aAAaA,CAACM,IAAI,EAAE;IAC3BpB,MAAM,CACJoB,IAAI,KAAKf,KAAK,CAACgB,GAAG,IAAIjB,kBAAkB,CAACgB,IAAI,CAAC,EAC9C,qBACF,CAAC;IAED,IAAIA,IAAI,KAAKf,KAAK,CAACgB,GAAG,EAAE;MACtBX,OAAO,CAACY,OAAO,CAACF,IAAI,CAAC;MACrB;IACF;IAEAV,OAAO,CAACa,KAAK,CAACjB,KAAK,CAACkB,eAAe,CAAC;IACpCd,OAAO,CAACY,OAAO,CAACF,IAAI,CAAC;IACrBV,OAAO,CAACe,IAAI,CAACnB,KAAK,CAACkB,eAAe,CAAC;IACnCb,IAAI,CAACe,gBAAgB,GAAGC,SAAS;IACjC,OAAOf,OAAO;EAChB;;EAEA;EACA,SAASM,cAAcA,CAACE,IAAI,EAAE;IAC5BpB,MAAM,CACJoB,IAAI,KAAKf,KAAK,CAACgB,GAAG,IAAIjB,kBAAkB,CAACgB,IAAI,CAAC,EAC9C,qBACF,CAAC;IAED,IAAIA,IAAI,KAAKf,KAAK,CAACgB,GAAG,EAAE;MACtBX,OAAO,CAACY,OAAO,CAACF,IAAI,CAAC;MACrB;IACF;IAEAV,OAAO,CAACa,KAAK,CAACjB,KAAK,CAACsB,UAAU,CAAC;IAC/BlB,OAAO,CAACY,OAAO,CAACF,IAAI,CAAC;IACrBV,OAAO,CAACe,IAAI,CAACnB,KAAK,CAACsB,UAAU,CAAC;IAC9BjB,IAAI,CAACe,gBAAgB,GAAGC,SAAS;IACjC,OAAOf,OAAO;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}