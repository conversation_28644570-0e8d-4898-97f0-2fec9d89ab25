{"ast": null, "code": "/**\n * @import {\n *   Break,\n *   Blockquote,\n *   Code,\n *   Definition,\n *   Emphasis,\n *   Heading,\n *   Html,\n *   Image,\n *   InlineCode,\n *   Link,\n *   ListItem,\n *   List,\n *   Nodes,\n *   Paragraph,\n *   PhrasingContent,\n *   ReferenceType,\n *   Root,\n *   Strong,\n *   Text,\n *   ThematicBreak\n * } from 'mdast'\n * @import {\n *   Encoding,\n *   Event,\n *   Token,\n *   Value\n * } from 'micromark-util-types'\n * @import {Point} from 'unist'\n * @import {\n *   CompileContext,\n *   CompileData,\n *   Config,\n *   Extension,\n *   Handle,\n *   OnEnterError,\n *   Options\n * } from './types.js'\n */\n\nimport { ok as assert } from 'devlop';\nimport { toString } from 'mdast-util-to-string';\nimport { parse, postprocess, preprocess } from 'micromark';\nimport { decodeNumericCharacterReference } from 'micromark-util-decode-numeric-character-reference';\nimport { decodeString } from 'micromark-util-decode-string';\nimport { normalizeIdentifier } from 'micromark-util-normalize-identifier';\nimport { codes, constants, types } from 'micromark-util-symbol';\nimport { decodeNamedCharacterReference } from 'decode-named-character-reference';\nimport { stringifyPosition } from 'unist-util-stringify-position';\nconst own = {}.hasOwnProperty;\n\n/**\n * Turn markdown into a syntax tree.\n *\n * @overload\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @overload\n * @param {Value} value\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @param {Value} value\n *   Markdown to parse.\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding for when `value` is `Buffer`.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {Root}\n *   mdast tree.\n */\nexport function fromMarkdown(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding;\n    encoding = undefined;\n  }\n  return compiler(options)(postprocess(parse(options).document().write(preprocess()(value, encoding, true))));\n}\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      characterReference: onexitcharacterreference,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  };\n  configure(config, (options || {}).mdastExtensions || []);\n\n  /** @type {CompileData} */\n  const data = {};\n  return compile;\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {\n      type: 'root',\n      children: []\n    };\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      data\n    };\n    /** @type {Array<number>} */\n    const listStack = [];\n    let index = -1;\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (events[index][1].type === types.listOrdered || events[index][1].type === types.listUnordered) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index);\n        } else {\n          const tail = listStack.pop();\n          assert(typeof tail === 'number', 'expected list ot be open');\n          index = prepareList(events, tail, index);\n        }\n      }\n    }\n    index = -1;\n    while (++index < events.length) {\n      const handler = config[events[index][0]];\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(Object.assign({\n          sliceSerialize: events[index][2].sliceSerialize\n        }, context), events[index][1]);\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1];\n      const handler = tail[1] || defaultOnError;\n      handler.call(context, undefined, tail[0]);\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(events.length > 0 ? events[0][1].start : {\n        line: 1,\n        column: 1,\n        offset: 0\n      }),\n      end: point(events.length > 0 ? events[events.length - 2][1].end : {\n        line: 1,\n        column: 1,\n        offset: 0\n      })\n    };\n\n    // Call transforms.\n    index = -1;\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree;\n    }\n    return tree;\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1;\n    let containerBalance = -1;\n    let listSpread = false;\n    /** @type {Token | undefined} */\n    let listItem;\n    /** @type {number | undefined} */\n    let lineIndex;\n    /** @type {number | undefined} */\n    let firstBlankLineIndex;\n    /** @type {boolean | undefined} */\n    let atMarker;\n    while (++index <= length) {\n      const event = events[index];\n      switch (event[1].type) {\n        case types.listUnordered:\n        case types.listOrdered:\n        case types.blockQuote:\n          {\n            if (event[0] === 'enter') {\n              containerBalance++;\n            } else {\n              containerBalance--;\n            }\n            atMarker = undefined;\n            break;\n          }\n        case types.lineEndingBlank:\n          {\n            if (event[0] === 'enter') {\n              if (listItem && !atMarker && !containerBalance && !firstBlankLineIndex) {\n                firstBlankLineIndex = index;\n              }\n              atMarker = undefined;\n            }\n            break;\n          }\n        case types.linePrefix:\n        case types.listItemValue:\n        case types.listItemMarker:\n        case types.listItemPrefix:\n        case types.listItemPrefixWhitespace:\n          {\n            // Empty.\n\n            break;\n          }\n        default:\n          {\n            atMarker = undefined;\n          }\n      }\n      if (!containerBalance && event[0] === 'enter' && event[1].type === types.listItemPrefix || containerBalance === -1 && event[0] === 'exit' && (event[1].type === types.listUnordered || event[1].type === types.listOrdered)) {\n        if (listItem) {\n          let tailIndex = index;\n          lineIndex = undefined;\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex];\n            if (tailEvent[1].type === types.lineEnding || tailEvent[1].type === types.lineEndingBlank) {\n              if (tailEvent[0] === 'exit') continue;\n              if (lineIndex) {\n                events[lineIndex][1].type = types.lineEndingBlank;\n                listSpread = true;\n              }\n              tailEvent[1].type = types.lineEnding;\n              lineIndex = tailIndex;\n            } else if (tailEvent[1].type === types.linePrefix || tailEvent[1].type === types.blockQuotePrefix || tailEvent[1].type === types.blockQuotePrefixWhitespace || tailEvent[1].type === types.blockQuoteMarker || tailEvent[1].type === types.listItemIndent) {\n              // Empty\n            } else {\n              break;\n            }\n          }\n          if (firstBlankLineIndex && (!lineIndex || firstBlankLineIndex < lineIndex)) {\n            listItem._spread = true;\n          }\n\n          // Fix position.\n          listItem.end = Object.assign({}, lineIndex ? events[lineIndex][1].start : event[1].end);\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]]);\n          index++;\n          length++;\n        }\n\n        // Create a new list item.\n        if (event[1].type === types.listItemPrefix) {\n          /** @type {Token} */\n          const item = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          };\n          listItem = item;\n          events.splice(index, 0, ['enter', item, event[2]]);\n          index++;\n          length++;\n          firstBlankLineIndex = undefined;\n          atMarker = true;\n        }\n      }\n    }\n    events[start][1]._spread = listSpread;\n    return length;\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Nodes} create\n   *   Create a node.\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open;\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function open(token) {\n      enter.call(this, create(token), token);\n      if (and) and.call(this, token);\n    }\n  }\n\n  /**\n   * @type {CompileContext['buffer']}\n   */\n  function buffer() {\n    this.stack.push({\n      type: 'fragment',\n      children: []\n    });\n  }\n\n  /**\n   * @type {CompileContext['enter']}\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1];\n    assert(parent, 'expected `parent`');\n    assert('children' in parent, 'expected `parent`');\n    /** @type {Array<Nodes>} */\n    const siblings = parent.children;\n    siblings.push(node);\n    this.stack.push(node);\n    this.tokenStack.push([token, errorHandler || undefined]);\n    node.position = {\n      start: point(token.start),\n      // @ts-expect-error: `end` will be patched later.\n      end: undefined\n    };\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close;\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function close(token) {\n      if (and) and.call(this, token);\n      exit.call(this, token);\n    }\n  }\n\n  /**\n   * @type {CompileContext['exit']}\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop();\n    assert(node, 'expected `node`');\n    const open = this.tokenStack.pop();\n    if (!open) {\n      throw new Error('Cannot close `' + token.type + '` (' + stringifyPosition({\n        start: token.start,\n        end: token.end\n      }) + '): it’s not open');\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0]);\n      } else {\n        const handler = open[1] || defaultOnError;\n        handler.call(this, token, open[0]);\n      }\n    }\n    assert(node.type !== 'fragment', 'unexpected fragment `exit`ed');\n    assert(node.position, 'expected `position` to be defined');\n    node.position.end = point(token.end);\n  }\n\n  /**\n   * @type {CompileContext['resume']}\n   */\n  function resume() {\n    return toString(this.stack.pop());\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    this.data.expectingFirstListItemValue = true;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (this.data.expectingFirstListItemValue) {\n      const ancestor = this.stack[this.stack.length - 2];\n      assert(ancestor, 'expected nodes on stack');\n      assert(ancestor.type === 'list', 'expected list on stack');\n      ancestor.start = Number.parseInt(this.sliceSerialize(token), constants.numericBaseDecimal);\n      this.data.expectingFirstListItemValue = undefined;\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'code', 'expected code on stack');\n    node.lang = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'code', 'expected code on stack');\n    node.meta = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (this.data.flowCodeInside) return;\n    this.buffer();\n    this.data.flowCodeInside = true;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'code', 'expected code on stack');\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '');\n    this.data.flowCodeInside = undefined;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'code', 'expected code on stack');\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'definition', 'expected definition on stack');\n    node.label = label;\n    node.identifier = normalizeIdentifier(this.sliceSerialize(token)).toLowerCase();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'definition', 'expected definition on stack');\n    node.title = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'definition', 'expected definition on stack');\n    node.url = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'heading', 'expected heading on stack');\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length;\n      assert(depth === 1 || depth === 2 || depth === 3 || depth === 4 || depth === 5 || depth === 6, 'expected `depth` between `1` and `6`');\n      node.depth = depth;\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    this.data.setextHeadingSlurpLineEnding = true;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'heading', 'expected heading on stack');\n    node.depth = this.sliceSerialize(token).codePointAt(0) === codes.equalsTo ? 1 : 2;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    this.data.setextHeadingSlurpLineEnding = undefined;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert('children' in node, 'expected parent on stack');\n    /** @type {Array<Nodes>} */\n    const siblings = node.children;\n    let tail = siblings[siblings.length - 1];\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text();\n      tail.position = {\n        start: point(token.start),\n        // @ts-expect-error: we’ll add `end` later.\n        end: undefined\n      };\n      siblings.push(tail);\n    }\n    this.stack.push(tail);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop();\n    assert(tail, 'expected a `node` to be on the stack');\n    assert('value' in tail, 'expected a `literal` to be on the stack');\n    assert(tail.position, 'expected `node` to have an open position');\n    tail.value += this.sliceSerialize(token);\n    tail.position.end = point(token.end);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1];\n    assert(context, 'expected `node`');\n\n    // If we’re at a hard break, include the line ending in there.\n    if (this.data.atHardBreak) {\n      assert('children' in context, 'expected `parent`');\n      const tail = context.children[context.children.length - 1];\n      assert(tail.position, 'expected tail to have a starting position');\n      tail.position.end = point(token.end);\n      this.data.atHardBreak = undefined;\n      return;\n    }\n    if (!this.data.setextHeadingSlurpLineEnding && config.canContainEols.includes(context.type)) {\n      onenterdata.call(this, token);\n      onexitdata.call(this, token);\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    this.data.atHardBreak = true;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'html', 'expected html on stack');\n    node.value = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'html', 'expected html on stack');\n    node.value = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'inlineCode', 'expected inline code on stack');\n    node.value = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'link', 'expected link on stack');\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut';\n      node.type += 'Reference';\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType;\n      // @ts-expect-error: mutate.\n      delete node.url;\n      delete node.title;\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier;\n      // @ts-expect-error: mutate.\n      delete node.label;\n    }\n    this.data.referenceType = undefined;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image', 'expected image on stack');\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut';\n      node.type += 'Reference';\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType;\n      // @ts-expect-error: mutate.\n      delete node.url;\n      delete node.title;\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier;\n      // @ts-expect-error: mutate.\n      delete node.label;\n    }\n    this.data.referenceType = undefined;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token);\n    const ancestor = this.stack[this.stack.length - 2];\n    assert(ancestor, 'expected ancestor on stack');\n    assert(ancestor.type === 'image' || ancestor.type === 'link', 'expected image or link on stack');\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = decodeString(string);\n    // @ts-expect-error: same as above.\n    ancestor.identifier = normalizeIdentifier(string).toLowerCase();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1];\n    assert(fragment, 'expected node on stack');\n    assert(fragment.type === 'fragment', 'expected fragment on stack');\n    const value = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image' || node.type === 'link', 'expected image or link on stack');\n\n    // Assume a reference.\n    this.data.inReference = true;\n    if (node.type === 'link') {\n      /** @type {Array<PhrasingContent>} */\n      const children = fragment.children;\n      node.children = children;\n    } else {\n      node.alt = value;\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image' || node.type === 'link', 'expected image or link on stack');\n    node.url = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image' || node.type === 'link', 'expected image or link on stack');\n    node.title = data;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    this.data.inReference = undefined;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    this.data.referenceType = 'collapsed';\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume();\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'image' || node.type === 'link', 'expected image reference or link reference on stack');\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label;\n    // @ts-expect-error: same as above.\n    node.identifier = normalizeIdentifier(this.sliceSerialize(token)).toLowerCase();\n    this.data.referenceType = 'full';\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    assert(token.type === 'characterReferenceMarkerNumeric' || token.type === 'characterReferenceMarkerHexadecimal');\n    this.data.characterReferenceType = token.type;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token);\n    const type = this.data.characterReferenceType;\n    /** @type {string} */\n    let value;\n    if (type) {\n      value = decodeNumericCharacterReference(data, type === types.characterReferenceMarkerNumeric ? constants.numericBaseDecimal : constants.numericBaseHexadecimal);\n      this.data.characterReferenceType = undefined;\n    } else {\n      const result = decodeNamedCharacterReference(data);\n      assert(result !== false, 'expected reference to decode');\n      value = result;\n    }\n    const tail = this.stack[this.stack.length - 1];\n    assert(tail, 'expected `node`');\n    assert('value' in tail, 'expected `node.value`');\n    tail.value += value;\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreference(token) {\n    const tail = this.stack.pop();\n    assert(tail, 'expected `node`');\n    assert(tail.position, 'expected `node.position`');\n    tail.position.end = point(token.end);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token);\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'link', 'expected link on stack');\n    node.url = this.sliceSerialize(token);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token);\n    const node = this.stack[this.stack.length - 1];\n    assert(node, 'expected node on stack');\n    assert(node.type === 'link', 'expected link on stack');\n    node.url = 'mailto:' + this.sliceSerialize(token);\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {\n      type: 'blockquote',\n      children: []\n    };\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {\n      type: 'code',\n      lang: null,\n      meta: null,\n      value: ''\n    };\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {\n      type: 'inlineCode',\n      value: ''\n    };\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    };\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {\n      type: 'emphasis',\n      children: []\n    };\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    return {\n      type: 'heading',\n      // @ts-expect-error `depth` will be set later.\n      depth: 0,\n      children: []\n    };\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {\n      type: 'break'\n    };\n  }\n\n  /** @returns {Html} */\n  function html() {\n    return {\n      type: 'html',\n      value: ''\n    };\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {\n      type: 'image',\n      title: null,\n      url: '',\n      alt: null\n    };\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {\n      type: 'link',\n      title: null,\n      url: '',\n      children: []\n    };\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    };\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    };\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {\n      type: 'paragraph',\n      children: []\n    };\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {\n      type: 'strong',\n      children: []\n    };\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {\n      type: 'text',\n      value: ''\n    };\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {\n      type: 'thematicBreak'\n    };\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {\n    line: d.line,\n    column: d.column,\n    offset: d.offset\n  };\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Array<Extension> | Extension>} extensions\n * @returns {undefined}\n */\nfunction configure(combined, extensions) {\n  let index = -1;\n  while (++index < extensions.length) {\n    const value = extensions[index];\n    if (Array.isArray(value)) {\n      configure(combined, value);\n    } else {\n      extension(combined, value);\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {undefined}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key;\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'canContainEols':\n          {\n            const right = extension[key];\n            if (right) {\n              combined[key].push(...right);\n            }\n            break;\n          }\n        case 'transforms':\n          {\n            const right = extension[key];\n            if (right) {\n              combined[key].push(...right);\n            }\n            break;\n          }\n        case 'enter':\n        case 'exit':\n          {\n            const right = extension[key];\n            if (right) {\n              Object.assign(combined[key], right);\n            }\n            break;\n          }\n        // No default\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error('Cannot close `' + left.type + '` (' + stringifyPosition({\n      start: left.start,\n      end: left.end\n    }) + '): a different token (`' + right.type + '`, ' + stringifyPosition({\n      start: right.start,\n      end: right.end\n    }) + ') is open');\n  } else {\n    throw new Error('Cannot close document, a token (`' + right.type + '`, ' + stringifyPosition({\n      start: right.start,\n      end: right.end\n    }) + ') is still open');\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "toString", "parse", "postprocess", "preprocess", "decodeNumericCharacterReference", "decodeString", "normalizeIdentifier", "codes", "constants", "types", "decodeNamedCharacterReference", "stringifyPosition", "own", "hasOwnProperty", "fromMarkdown", "value", "encoding", "options", "undefined", "compiler", "document", "write", "config", "transforms", "canContainEols", "enter", "autolink", "opener", "link", "autolinkProtocol", "onenterdata", "autolinkEmail", "atxHeading", "heading", "blockQuote", "characterEscape", "characterReference", "codeFenced", "codeFlow", "codeFencedFenceInfo", "buffer", "codeFencedFenceMeta", "codeIndented", "codeText", "codeTextData", "data", "codeFlowValue", "definition", "definitionDestinationString", "definitionLabelString", "definitionTitleString", "emphasis", "hardBreakEscape", "hardBreak", "hardBreakTrailing", "htmlFlow", "html", "htmlFlowData", "htmlText", "htmlTextData", "image", "label", "listItem", "listItemValue", "onenterlistitemvalue", "listOrdered", "list", "onenterlistordered", "listUnordered", "paragraph", "reference", "onenterreference", "referenceString", "resourceDestinationString", "resourceTitleString", "setextHeading", "strong", "thematicBreak", "exit", "closer", "atxHeadingSequence", "onexitatxheadingsequence", "onexitautolinkemail", "onexitautolinkprotocol", "characterEscapeValue", "onexitdata", "characterReferenceMarkerHexadecimal", "onexitcharacterreferencemarker", "characterReferenceMarkerNumeric", "characterReferenceValue", "onexitcharacterreferencevalue", "onexitcharacterreference", "onexitcodefenced", "codeFencedFence", "onexitcodefencedfence", "onexitcodefencedfenceinfo", "onexitcodefencedfencemeta", "onexitcodeindented", "onexitcodetext", "onexitdefinitiondestinationstring", "onexitdefinitionlabelstring", "onexitdefinitiontitlestring", "onexithardbreak", "onexithtmlflow", "onexithtmltext", "onexitimage", "onexitlabel", "labelText", "onexitlabeltext", "lineEnding", "onexitlineending", "onexitlink", "onexitreferencestring", "onexitresourcedestinationstring", "onexitresourcetitlestring", "resource", "onexitresource", "onexitsetextheading", "setextHeadingLineSequence", "onexitsetextheadinglinesequence", "setextHeadingText", "onexitsetextheadingtext", "configure", "mdastExtensions", "compile", "events", "tree", "type", "children", "context", "stack", "tokenStack", "resume", "listStack", "index", "length", "push", "tail", "pop", "prepareList", "handler", "call", "Object", "assign", "sliceSerialize", "defaultOnError", "position", "start", "point", "line", "column", "offset", "end", "containerBalance", "listSpread", "lineIndex", "firstBlankLineIndex", "atMarker", "event", "lineEndingBlank", "linePrefix", "listItemMarker", "listItemPrefix", "listItemPrefixWhitespace", "tailIndex", "tailEvent", "blockQuotePrefix", "blockQuotePrefixWhitespace", "blockQuoteMarker", "listItemIndent", "_spread", "splice", "item", "create", "and", "open", "token", "node", "<PERSON><PERSON><PERSON><PERSON>", "parent", "siblings", "close", "onExitError", "Error", "expectingFirstListItemValue", "ancestor", "Number", "parseInt", "numericBaseDecimal", "lang", "meta", "flowCodeInside", "replace", "identifier", "toLowerCase", "title", "url", "depth", "setextHeadingSlurpLineEnding", "codePointAt", "equalsTo", "text", "atHardBreak", "includes", "inReference", "referenceType", "string", "fragment", "alt", "characterReferenceType", "numericBaseHexadecimal", "result", "ordered", "spread", "checked", "d", "combined", "extensions", "Array", "isArray", "extension", "key", "right", "left"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/mdast-util-from-markdown/dev/lib/index.js"], "sourcesContent": ["/**\n * @import {\n *   Break,\n *   Blockquote,\n *   Code,\n *   Definition,\n *   Emphasis,\n *   Heading,\n *   Html,\n *   Image,\n *   InlineCode,\n *   Link,\n *   ListItem,\n *   List,\n *   Nodes,\n *   Paragraph,\n *   PhrasingContent,\n *   ReferenceType,\n *   Root,\n *   Strong,\n *   Text,\n *   ThematicBreak\n * } from 'mdast'\n * @import {\n *   Encoding,\n *   Event,\n *   Token,\n *   Value\n * } from 'micromark-util-types'\n * @import {Point} from 'unist'\n * @import {\n *   CompileContext,\n *   CompileData,\n *   Config,\n *   Extension,\n *   Handle,\n *   OnEnterError,\n *   Options\n * } from './types.js'\n */\n\nimport {ok as assert} from 'devlop'\nimport {toString} from 'mdast-util-to-string'\nimport {parse, postprocess, preprocess} from 'micromark'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {decodeString} from 'micromark-util-decode-string'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Turn markdown into a syntax tree.\n *\n * @overload\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @overload\n * @param {Value} value\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @param {Value} value\n *   Markdown to parse.\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding for when `value` is `Buffer`.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {Root}\n *   mdast tree.\n */\nexport function fromMarkdown(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding\n    encoding = undefined\n  }\n\n  return compiler(options)(\n    postprocess(\n      parse(options)\n        .document()\n        .write(preprocess()(value, encoding, true))\n    )\n  )\n}\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      characterReference: onexitcharacterreference,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  }\n\n  configure(config, (options || {}).mdastExtensions || [])\n\n  /** @type {CompileData} */\n  const data = {}\n\n  return compile\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {type: 'root', children: []}\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      data\n    }\n    /** @type {Array<number>} */\n    const listStack = []\n    let index = -1\n\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (\n        events[index][1].type === types.listOrdered ||\n        events[index][1].type === types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          const tail = listStack.pop()\n          assert(typeof tail === 'number', 'expected list ot be open')\n          index = prepareList(events, tail, index)\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      const handler = config[events[index][0]]\n\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(\n          Object.assign(\n            {sliceSerialize: events[index][2].sliceSerialize},\n            context\n          ),\n          events[index][1]\n        )\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1]\n      const handler = tail[1] || defaultOnError\n      handler.call(context, undefined, tail[0])\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(\n        events.length > 0 ? events[0][1].start : {line: 1, column: 1, offset: 0}\n      ),\n      end: point(\n        events.length > 0\n          ? events[events.length - 2][1].end\n          : {line: 1, column: 1, offset: 0}\n      )\n    }\n\n    // Call transforms.\n    index = -1\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree\n    }\n\n    return tree\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1\n    let containerBalance = -1\n    let listSpread = false\n    /** @type {Token | undefined} */\n    let listItem\n    /** @type {number | undefined} */\n    let lineIndex\n    /** @type {number | undefined} */\n    let firstBlankLineIndex\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index <= length) {\n      const event = events[index]\n\n      switch (event[1].type) {\n        case types.listUnordered:\n        case types.listOrdered:\n        case types.blockQuote: {\n          if (event[0] === 'enter') {\n            containerBalance++\n          } else {\n            containerBalance--\n          }\n\n          atMarker = undefined\n\n          break\n        }\n\n        case types.lineEndingBlank: {\n          if (event[0] === 'enter') {\n            if (\n              listItem &&\n              !atMarker &&\n              !containerBalance &&\n              !firstBlankLineIndex\n            ) {\n              firstBlankLineIndex = index\n            }\n\n            atMarker = undefined\n          }\n\n          break\n        }\n\n        case types.linePrefix:\n        case types.listItemValue:\n        case types.listItemMarker:\n        case types.listItemPrefix:\n        case types.listItemPrefixWhitespace: {\n          // Empty.\n\n          break\n        }\n\n        default: {\n          atMarker = undefined\n        }\n      }\n\n      if (\n        (!containerBalance &&\n          event[0] === 'enter' &&\n          event[1].type === types.listItemPrefix) ||\n        (containerBalance === -1 &&\n          event[0] === 'exit' &&\n          (event[1].type === types.listUnordered ||\n            event[1].type === types.listOrdered))\n      ) {\n        if (listItem) {\n          let tailIndex = index\n          lineIndex = undefined\n\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex]\n\n            if (\n              tailEvent[1].type === types.lineEnding ||\n              tailEvent[1].type === types.lineEndingBlank\n            ) {\n              if (tailEvent[0] === 'exit') continue\n\n              if (lineIndex) {\n                events[lineIndex][1].type = types.lineEndingBlank\n                listSpread = true\n              }\n\n              tailEvent[1].type = types.lineEnding\n              lineIndex = tailIndex\n            } else if (\n              tailEvent[1].type === types.linePrefix ||\n              tailEvent[1].type === types.blockQuotePrefix ||\n              tailEvent[1].type === types.blockQuotePrefixWhitespace ||\n              tailEvent[1].type === types.blockQuoteMarker ||\n              tailEvent[1].type === types.listItemIndent\n            ) {\n              // Empty\n            } else {\n              break\n            }\n          }\n\n          if (\n            firstBlankLineIndex &&\n            (!lineIndex || firstBlankLineIndex < lineIndex)\n          ) {\n            listItem._spread = true\n          }\n\n          // Fix position.\n          listItem.end = Object.assign(\n            {},\n            lineIndex ? events[lineIndex][1].start : event[1].end\n          )\n\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]])\n          index++\n          length++\n        }\n\n        // Create a new list item.\n        if (event[1].type === types.listItemPrefix) {\n          /** @type {Token} */\n          const item = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          }\n          listItem = item\n          events.splice(index, 0, ['enter', item, event[2]])\n          index++\n          length++\n          firstBlankLineIndex = undefined\n          atMarker = true\n        }\n      }\n    }\n\n    events[start][1]._spread = listSpread\n    return length\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Nodes} create\n   *   Create a node.\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function open(token) {\n      enter.call(this, create(token), token)\n      if (and) and.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['buffer']}\n   */\n  function buffer() {\n    this.stack.push({type: 'fragment', children: []})\n  }\n\n  /**\n   * @type {CompileContext['enter']}\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1]\n    assert(parent, 'expected `parent`')\n    assert('children' in parent, 'expected `parent`')\n    /** @type {Array<Nodes>} */\n    const siblings = parent.children\n    siblings.push(node)\n    this.stack.push(node)\n    this.tokenStack.push([token, errorHandler || undefined])\n    node.position = {\n      start: point(token.start),\n      // @ts-expect-error: `end` will be patched later.\n      end: undefined\n    }\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function close(token) {\n      if (and) and.call(this, token)\n      exit.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['exit']}\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop()\n    assert(node, 'expected `node`')\n    const open = this.tokenStack.pop()\n\n    if (!open) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          stringifyPosition({start: token.start, end: token.end}) +\n          '): it’s not open'\n      )\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0])\n      } else {\n        const handler = open[1] || defaultOnError\n        handler.call(this, token, open[0])\n      }\n    }\n\n    assert(node.type !== 'fragment', 'unexpected fragment `exit`ed')\n    assert(node.position, 'expected `position` to be defined')\n    node.position.end = point(token.end)\n  }\n\n  /**\n   * @type {CompileContext['resume']}\n   */\n  function resume() {\n    return toString(this.stack.pop())\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    this.data.expectingFirstListItemValue = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (this.data.expectingFirstListItemValue) {\n      const ancestor = this.stack[this.stack.length - 2]\n      assert(ancestor, 'expected nodes on stack')\n      assert(ancestor.type === 'list', 'expected list on stack')\n      ancestor.start = Number.parseInt(\n        this.sliceSerialize(token),\n        constants.numericBaseDecimal\n      )\n      this.data.expectingFirstListItemValue = undefined\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.lang = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.meta = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (this.data.flowCodeInside) return\n    this.buffer()\n    this.data.flowCodeInside = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n    this.data.flowCodeInside = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.label = label\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length\n\n      assert(\n        depth === 1 ||\n          depth === 2 ||\n          depth === 3 ||\n          depth === 4 ||\n          depth === 5 ||\n          depth === 6,\n        'expected `depth` between `1` and `6`'\n      )\n\n      node.depth = depth\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    this.data.setextHeadingSlurpLineEnding = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    node.depth =\n      this.sliceSerialize(token).codePointAt(0) === codes.equalsTo ? 1 : 2\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    this.data.setextHeadingSlurpLineEnding = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert('children' in node, 'expected parent on stack')\n    /** @type {Array<Nodes>} */\n    const siblings = node.children\n\n    let tail = siblings[siblings.length - 1]\n\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text()\n      tail.position = {\n        start: point(token.start),\n        // @ts-expect-error: we’ll add `end` later.\n        end: undefined\n      }\n      siblings.push(tail)\n    }\n\n    this.stack.push(tail)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop()\n    assert(tail, 'expected a `node` to be on the stack')\n    assert('value' in tail, 'expected a `literal` to be on the stack')\n    assert(tail.position, 'expected `node` to have an open position')\n    tail.value += this.sliceSerialize(token)\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1]\n    assert(context, 'expected `node`')\n\n    // If we’re at a hard break, include the line ending in there.\n    if (this.data.atHardBreak) {\n      assert('children' in context, 'expected `parent`')\n      const tail = context.children[context.children.length - 1]\n      assert(tail.position, 'expected tail to have a starting position')\n      tail.position.end = point(token.end)\n      this.data.atHardBreak = undefined\n      return\n    }\n\n    if (\n      !this.data.setextHeadingSlurpLineEnding &&\n      config.canContainEols.includes(context.type)\n    ) {\n      onenterdata.call(this, token)\n      onexitdata.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    this.data.atHardBreak = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'inlineCode', 'expected inline code on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'image', 'expected image on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token)\n    const ancestor = this.stack[this.stack.length - 2]\n    assert(ancestor, 'expected ancestor on stack')\n    assert(\n      ancestor.type === 'image' || ancestor.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = decodeString(string)\n    // @ts-expect-error: same as above.\n    ancestor.identifier = normalizeIdentifier(string).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1]\n    assert(fragment, 'expected node on stack')\n    assert(fragment.type === 'fragment', 'expected fragment on stack')\n    const value = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // Assume a reference.\n    this.data.inReference = true\n\n    if (node.type === 'link') {\n      /** @type {Array<PhrasingContent>} */\n      const children = fragment.children\n\n      node.children = children\n    } else {\n      node.alt = value\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    this.data.inReference = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    this.data.referenceType = 'collapsed'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image reference or link reference on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label\n    // @ts-expect-error: same as above.\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n    this.data.referenceType = 'full'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    assert(\n      token.type === 'characterReferenceMarkerNumeric' ||\n        token.type === 'characterReferenceMarkerHexadecimal'\n    )\n    this.data.characterReferenceType = token.type\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token)\n    const type = this.data.characterReferenceType\n    /** @type {string} */\n    let value\n\n    if (type) {\n      value = decodeNumericCharacterReference(\n        data,\n        type === types.characterReferenceMarkerNumeric\n          ? constants.numericBaseDecimal\n          : constants.numericBaseHexadecimal\n      )\n      this.data.characterReferenceType = undefined\n    } else {\n      const result = decodeNamedCharacterReference(data)\n      assert(result !== false, 'expected reference to decode')\n      value = result\n    }\n\n    const tail = this.stack[this.stack.length - 1]\n    assert(tail, 'expected `node`')\n    assert('value' in tail, 'expected `node.value`')\n    tail.value += value\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreference(token) {\n    const tail = this.stack.pop()\n    assert(tail, 'expected `node`')\n    assert(tail.position, 'expected `node.position`')\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = 'mailto:' + this.sliceSerialize(token)\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {type: 'blockquote', children: []}\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {type: 'code', lang: null, meta: null, value: ''}\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {type: 'inlineCode', value: ''}\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    }\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {type: 'emphasis', children: []}\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    return {\n      type: 'heading',\n      // @ts-expect-error `depth` will be set later.\n      depth: 0,\n      children: []\n    }\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {type: 'break'}\n  }\n\n  /** @returns {Html} */\n  function html() {\n    return {type: 'html', value: ''}\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {type: 'image', title: null, url: '', alt: null}\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {type: 'link', title: null, url: '', children: []}\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    }\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    }\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {type: 'paragraph', children: []}\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {type: 'strong', children: []}\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {type: 'text', value: ''}\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {type: 'thematicBreak'}\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {line: d.line, column: d.column, offset: d.offset}\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Array<Extension> | Extension>} extensions\n * @returns {undefined}\n */\nfunction configure(combined, extensions) {\n  let index = -1\n\n  while (++index < extensions.length) {\n    const value = extensions[index]\n\n    if (Array.isArray(value)) {\n      configure(combined, value)\n    } else {\n      extension(combined, value)\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {undefined}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'canContainEols': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'transforms': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'enter':\n        case 'exit': {\n          const right = extension[key]\n          if (right) {\n            Object.assign(combined[key], right)\n          }\n\n          break\n        }\n        // No default\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error(\n      'Cannot close `' +\n        left.type +\n        '` (' +\n        stringifyPosition({start: left.start, end: left.end}) +\n        '): a different token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is open'\n    )\n  } else {\n    throw new Error(\n      'Cannot close document, a token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is still open'\n    )\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,QAAQ,QAAO,sBAAsB;AAC7C,SAAQC,KAAK,EAAEC,WAAW,EAAEC,UAAU,QAAO,WAAW;AACxD,SAAQC,+BAA+B,QAAO,mDAAmD;AACjG,SAAQC,YAAY,QAAO,8BAA8B;AACzD,SAAQC,mBAAmB,QAAO,qCAAqC;AACvE,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;AAC7D,SAAQC,6BAA6B,QAAO,kCAAkC;AAC9E,SAAQC,iBAAiB,QAAO,+BAA+B;AAE/D,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACrD,IAAI,OAAOD,QAAQ,KAAK,QAAQ,EAAE;IAChCC,OAAO,GAAGD,QAAQ;IAClBA,QAAQ,GAAGE,SAAS;EACtB;EAEA,OAAOC,QAAQ,CAACF,OAAO,CAAC,CACtBf,WAAW,CACTD,KAAK,CAACgB,OAAO,CAAC,CACXG,QAAQ,CAAC,CAAC,CACVC,KAAK,CAAClB,UAAU,CAAC,CAAC,CAACY,KAAK,EAAEC,QAAQ,EAAE,IAAI,CAAC,CAC9C,CACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACF,OAAO,EAAE;EACzB;EACA,MAAMK,MAAM,GAAG;IACbC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,CAAC;IAC1EC,KAAK,EAAE;MACLC,QAAQ,EAAEC,MAAM,CAACC,IAAI,CAAC;MACtBC,gBAAgB,EAAEC,WAAW;MAC7BC,aAAa,EAAED,WAAW;MAC1BE,UAAU,EAAEL,MAAM,CAACM,OAAO,CAAC;MAC3BC,UAAU,EAAEP,MAAM,CAACO,UAAU,CAAC;MAC9BC,eAAe,EAAEL,WAAW;MAC5BM,kBAAkB,EAAEN,WAAW;MAC/BO,UAAU,EAAEV,MAAM,CAACW,QAAQ,CAAC;MAC5BC,mBAAmB,EAAEC,MAAM;MAC3BC,mBAAmB,EAAED,MAAM;MAC3BE,YAAY,EAAEf,MAAM,CAACW,QAAQ,EAAEE,MAAM,CAAC;MACtCG,QAAQ,EAAEhB,MAAM,CAACgB,QAAQ,EAAEH,MAAM,CAAC;MAClCI,YAAY,EAAEd,WAAW;MACzBe,IAAI,EAAEf,WAAW;MACjBgB,aAAa,EAAEhB,WAAW;MAC1BiB,UAAU,EAAEpB,MAAM,CAACoB,UAAU,CAAC;MAC9BC,2BAA2B,EAAER,MAAM;MACnCS,qBAAqB,EAAET,MAAM;MAC7BU,qBAAqB,EAAEV,MAAM;MAC7BW,QAAQ,EAAExB,MAAM,CAACwB,QAAQ,CAAC;MAC1BC,eAAe,EAAEzB,MAAM,CAAC0B,SAAS,CAAC;MAClCC,iBAAiB,EAAE3B,MAAM,CAAC0B,SAAS,CAAC;MACpCE,QAAQ,EAAE5B,MAAM,CAAC6B,IAAI,EAAEhB,MAAM,CAAC;MAC9BiB,YAAY,EAAE3B,WAAW;MACzB4B,QAAQ,EAAE/B,MAAM,CAAC6B,IAAI,EAAEhB,MAAM,CAAC;MAC9BmB,YAAY,EAAE7B,WAAW;MACzB8B,KAAK,EAAEjC,MAAM,CAACiC,KAAK,CAAC;MACpBC,KAAK,EAAErB,MAAM;MACbZ,IAAI,EAAED,MAAM,CAACC,IAAI,CAAC;MAClBkC,QAAQ,EAAEnC,MAAM,CAACmC,QAAQ,CAAC;MAC1BC,aAAa,EAAEC,oBAAoB;MACnCC,WAAW,EAAEtC,MAAM,CAACuC,IAAI,EAAEC,kBAAkB,CAAC;MAC7CC,aAAa,EAAEzC,MAAM,CAACuC,IAAI,CAAC;MAC3BG,SAAS,EAAE1C,MAAM,CAAC0C,SAAS,CAAC;MAC5BC,SAAS,EAAEC,gBAAgB;MAC3BC,eAAe,EAAEhC,MAAM;MACvBiC,yBAAyB,EAAEjC,MAAM;MACjCkC,mBAAmB,EAAElC,MAAM;MAC3BmC,aAAa,EAAEhD,MAAM,CAACM,OAAO,CAAC;MAC9B2C,MAAM,EAAEjD,MAAM,CAACiD,MAAM,CAAC;MACtBC,aAAa,EAAElD,MAAM,CAACkD,aAAa;IACrC,CAAC;IACDC,IAAI,EAAE;MACJ9C,UAAU,EAAE+C,MAAM,CAAC,CAAC;MACpBC,kBAAkB,EAAEC,wBAAwB;MAC5CvD,QAAQ,EAAEqD,MAAM,CAAC,CAAC;MAClBhD,aAAa,EAAEmD,mBAAmB;MAClCrD,gBAAgB,EAAEsD,sBAAsB;MACxCjD,UAAU,EAAE6C,MAAM,CAAC,CAAC;MACpBK,oBAAoB,EAAEC,UAAU;MAChCC,mCAAmC,EAAEC,8BAA8B;MACnEC,+BAA+B,EAAED,8BAA8B;MAC/DE,uBAAuB,EAAEC,6BAA6B;MACtDtD,kBAAkB,EAAEuD,wBAAwB;MAC5CtD,UAAU,EAAE0C,MAAM,CAACa,gBAAgB,CAAC;MACpCC,eAAe,EAAEC,qBAAqB;MACtCvD,mBAAmB,EAAEwD,yBAAyB;MAC9CtD,mBAAmB,EAAEuD,yBAAyB;MAC9ClD,aAAa,EAAEuC,UAAU;MACzB3C,YAAY,EAAEqC,MAAM,CAACkB,kBAAkB,CAAC;MACxCtD,QAAQ,EAAEoC,MAAM,CAACmB,cAAc,CAAC;MAChCtD,YAAY,EAAEyC,UAAU;MACxBxC,IAAI,EAAEwC,UAAU;MAChBtC,UAAU,EAAEgC,MAAM,CAAC,CAAC;MACpB/B,2BAA2B,EAAEmD,iCAAiC;MAC9DlD,qBAAqB,EAAEmD,2BAA2B;MAClDlD,qBAAqB,EAAEmD,2BAA2B;MAClDlD,QAAQ,EAAE4B,MAAM,CAAC,CAAC;MAClB3B,eAAe,EAAE2B,MAAM,CAACuB,eAAe,CAAC;MACxChD,iBAAiB,EAAEyB,MAAM,CAACuB,eAAe,CAAC;MAC1C/C,QAAQ,EAAEwB,MAAM,CAACwB,cAAc,CAAC;MAChC9C,YAAY,EAAE4B,UAAU;MACxB3B,QAAQ,EAAEqB,MAAM,CAACyB,cAAc,CAAC;MAChC7C,YAAY,EAAE0B,UAAU;MACxBzB,KAAK,EAAEmB,MAAM,CAAC0B,WAAW,CAAC;MAC1B5C,KAAK,EAAE6C,WAAW;MAClBC,SAAS,EAAEC,eAAe;MAC1BC,UAAU,EAAEC,gBAAgB;MAC5BlF,IAAI,EAAEmD,MAAM,CAACgC,UAAU,CAAC;MACxBjD,QAAQ,EAAEiB,MAAM,CAAC,CAAC;MAClBd,WAAW,EAAEc,MAAM,CAAC,CAAC;MACrBX,aAAa,EAAEW,MAAM,CAAC,CAAC;MACvBV,SAAS,EAAEU,MAAM,CAAC,CAAC;MACnBP,eAAe,EAAEwC,qBAAqB;MACtCvC,yBAAyB,EAAEwC,+BAA+B;MAC1DvC,mBAAmB,EAAEwC,yBAAyB;MAC9CC,QAAQ,EAAEC,cAAc;MACxBzC,aAAa,EAAEI,MAAM,CAACsC,mBAAmB,CAAC;MAC1CC,yBAAyB,EAAEC,+BAA+B;MAC1DC,iBAAiB,EAAEC,uBAAuB;MAC1C7C,MAAM,EAAEG,MAAM,CAAC,CAAC;MAChBF,aAAa,EAAEE,MAAM,CAAC;IACxB;EACF,CAAC;EAED2C,SAAS,CAACpG,MAAM,EAAE,CAACL,OAAO,IAAI,CAAC,CAAC,EAAE0G,eAAe,IAAI,EAAE,CAAC;;EAExD;EACA,MAAM9E,IAAI,GAAG,CAAC,CAAC;EAEf,OAAO+E,OAAO;;EAEd;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,OAAOA,CAACC,MAAM,EAAE;IACvB;IACA,IAAIC,IAAI,GAAG;MAACC,IAAI,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAE,CAAC;IACvC;IACA,MAAMC,OAAO,GAAG;MACdC,KAAK,EAAE,CAACJ,IAAI,CAAC;MACbK,UAAU,EAAE,EAAE;MACd7G,MAAM;MACNG,KAAK;MACLqD,IAAI;MACJtC,MAAM;MACN4F,MAAM;MACNvF;IACF,CAAC;IACD;IACA,MAAMwF,SAAS,GAAG,EAAE;IACpB,IAAIC,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,EAAEA,KAAK,GAAGT,MAAM,CAACU,MAAM,EAAE;MAC9B;MACA;MACA,IACEV,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,IAAI,KAAKtH,KAAK,CAACwD,WAAW,IAC3C4D,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,IAAI,KAAKtH,KAAK,CAAC2D,aAAa,EAC7C;QACA,IAAIyD,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UAChCD,SAAS,CAACG,IAAI,CAACF,KAAK,CAAC;QACvB,CAAC,MAAM;UACL,MAAMG,IAAI,GAAGJ,SAAS,CAACK,GAAG,CAAC,CAAC;UAC5B3I,MAAM,CAAC,OAAO0I,IAAI,KAAK,QAAQ,EAAE,0BAA0B,CAAC;UAC5DH,KAAK,GAAGK,WAAW,CAACd,MAAM,EAAEY,IAAI,EAAEH,KAAK,CAAC;QAC1C;MACF;IACF;IAEAA,KAAK,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,KAAK,GAAGT,MAAM,CAACU,MAAM,EAAE;MAC9B,MAAMK,OAAO,GAAGtH,MAAM,CAACuG,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAExC,IAAI1H,GAAG,CAACiI,IAAI,CAACD,OAAO,EAAEf,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,IAAI,CAAC,EAAE;QAC5Ca,OAAO,CAACf,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAACP,IAAI,CAAC,CAACc,IAAI,CACjCC,MAAM,CAACC,MAAM,CACX;UAACC,cAAc,EAAEnB,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CAACU;QAAc,CAAC,EACjDf,OACF,CAAC,EACDJ,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC,CACjB,CAAC;MACH;IACF;;IAEA;IACA,IAAIL,OAAO,CAACE,UAAU,CAACI,MAAM,GAAG,CAAC,EAAE;MACjC,MAAME,IAAI,GAAGR,OAAO,CAACE,UAAU,CAACF,OAAO,CAACE,UAAU,CAACI,MAAM,GAAG,CAAC,CAAC;MAC9D,MAAMK,OAAO,GAAGH,IAAI,CAAC,CAAC,CAAC,IAAIQ,cAAc;MACzCL,OAAO,CAACC,IAAI,CAACZ,OAAO,EAAE/G,SAAS,EAAEuH,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3C;;IAEA;IACAX,IAAI,CAACoB,QAAQ,GAAG;MACdC,KAAK,EAAEC,KAAK,CACVvB,MAAM,CAACU,MAAM,GAAG,CAAC,GAAGV,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACsB,KAAK,GAAG;QAACE,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAC,CACzE,CAAC;MACDC,GAAG,EAAEJ,KAAK,CACRvB,MAAM,CAACU,MAAM,GAAG,CAAC,GACbV,MAAM,CAACA,MAAM,CAACU,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACiB,GAAG,GAChC;QAACH,IAAI,EAAE,CAAC;QAAEC,MAAM,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAC,CACpC;IACF,CAAC;;IAED;IACAjB,KAAK,GAAG,CAAC,CAAC;IACV,OAAO,EAAEA,KAAK,GAAGhH,MAAM,CAACC,UAAU,CAACgH,MAAM,EAAE;MACzCT,IAAI,GAAGxG,MAAM,CAACC,UAAU,CAAC+G,KAAK,CAAC,CAACR,IAAI,CAAC,IAAIA,IAAI;IAC/C;IAEA,OAAOA,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASa,WAAWA,CAACd,MAAM,EAAEsB,KAAK,EAAEZ,MAAM,EAAE;IAC1C,IAAID,KAAK,GAAGa,KAAK,GAAG,CAAC;IACrB,IAAIM,gBAAgB,GAAG,CAAC,CAAC;IACzB,IAAIC,UAAU,GAAG,KAAK;IACtB;IACA,IAAI5F,QAAQ;IACZ;IACA,IAAI6F,SAAS;IACb;IACA,IAAIC,mBAAmB;IACvB;IACA,IAAIC,QAAQ;IAEZ,OAAO,EAAEvB,KAAK,IAAIC,MAAM,EAAE;MACxB,MAAMuB,KAAK,GAAGjC,MAAM,CAACS,KAAK,CAAC;MAE3B,QAAQwB,KAAK,CAAC,CAAC,CAAC,CAAC/B,IAAI;QACnB,KAAKtH,KAAK,CAAC2D,aAAa;QACxB,KAAK3D,KAAK,CAACwD,WAAW;QACtB,KAAKxD,KAAK,CAACyB,UAAU;UAAE;YACrB,IAAI4H,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;cACxBL,gBAAgB,EAAE;YACpB,CAAC,MAAM;cACLA,gBAAgB,EAAE;YACpB;YAEAI,QAAQ,GAAG3I,SAAS;YAEpB;UACF;QAEA,KAAKT,KAAK,CAACsJ,eAAe;UAAE;YAC1B,IAAID,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;cACxB,IACEhG,QAAQ,IACR,CAAC+F,QAAQ,IACT,CAACJ,gBAAgB,IACjB,CAACG,mBAAmB,EACpB;gBACAA,mBAAmB,GAAGtB,KAAK;cAC7B;cAEAuB,QAAQ,GAAG3I,SAAS;YACtB;YAEA;UACF;QAEA,KAAKT,KAAK,CAACuJ,UAAU;QACrB,KAAKvJ,KAAK,CAACsD,aAAa;QACxB,KAAKtD,KAAK,CAACwJ,cAAc;QACzB,KAAKxJ,KAAK,CAACyJ,cAAc;QACzB,KAAKzJ,KAAK,CAAC0J,wBAAwB;UAAE;YACnC;;YAEA;UACF;QAEA;UAAS;YACPN,QAAQ,GAAG3I,SAAS;UACtB;MACF;MAEA,IACG,CAACuI,gBAAgB,IAChBK,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,IACpBA,KAAK,CAAC,CAAC,CAAC,CAAC/B,IAAI,KAAKtH,KAAK,CAACyJ,cAAc,IACvCT,gBAAgB,KAAK,CAAC,CAAC,IACtBK,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,KAClBA,KAAK,CAAC,CAAC,CAAC,CAAC/B,IAAI,KAAKtH,KAAK,CAAC2D,aAAa,IACpC0F,KAAK,CAAC,CAAC,CAAC,CAAC/B,IAAI,KAAKtH,KAAK,CAACwD,WAAW,CAAE,EACzC;QACA,IAAIH,QAAQ,EAAE;UACZ,IAAIsG,SAAS,GAAG9B,KAAK;UACrBqB,SAAS,GAAGzI,SAAS;UAErB,OAAOkJ,SAAS,EAAE,EAAE;YAClB,MAAMC,SAAS,GAAGxC,MAAM,CAACuC,SAAS,CAAC;YAEnC,IACEC,SAAS,CAAC,CAAC,CAAC,CAACtC,IAAI,KAAKtH,KAAK,CAACoG,UAAU,IACtCwD,SAAS,CAAC,CAAC,CAAC,CAACtC,IAAI,KAAKtH,KAAK,CAACsJ,eAAe,EAC3C;cACA,IAAIM,SAAS,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;cAE7B,IAAIV,SAAS,EAAE;gBACb9B,MAAM,CAAC8B,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC5B,IAAI,GAAGtH,KAAK,CAACsJ,eAAe;gBACjDL,UAAU,GAAG,IAAI;cACnB;cAEAW,SAAS,CAAC,CAAC,CAAC,CAACtC,IAAI,GAAGtH,KAAK,CAACoG,UAAU;cACpC8C,SAAS,GAAGS,SAAS;YACvB,CAAC,MAAM,IACLC,SAAS,CAAC,CAAC,CAAC,CAACtC,IAAI,KAAKtH,KAAK,CAACuJ,UAAU,IACtCK,SAAS,CAAC,CAAC,CAAC,CAACtC,IAAI,KAAKtH,KAAK,CAAC6J,gBAAgB,IAC5CD,SAAS,CAAC,CAAC,CAAC,CAACtC,IAAI,KAAKtH,KAAK,CAAC8J,0BAA0B,IACtDF,SAAS,CAAC,CAAC,CAAC,CAACtC,IAAI,KAAKtH,KAAK,CAAC+J,gBAAgB,IAC5CH,SAAS,CAAC,CAAC,CAAC,CAACtC,IAAI,KAAKtH,KAAK,CAACgK,cAAc,EAC1C;cACA;YAAA,CACD,MAAM;cACL;YACF;UACF;UAEA,IACEb,mBAAmB,KAClB,CAACD,SAAS,IAAIC,mBAAmB,GAAGD,SAAS,CAAC,EAC/C;YACA7F,QAAQ,CAAC4G,OAAO,GAAG,IAAI;UACzB;;UAEA;UACA5G,QAAQ,CAAC0F,GAAG,GAAGV,MAAM,CAACC,MAAM,CAC1B,CAAC,CAAC,EACFY,SAAS,GAAG9B,MAAM,CAAC8B,SAAS,CAAC,CAAC,CAAC,CAAC,CAACR,KAAK,GAAGW,KAAK,CAAC,CAAC,CAAC,CAACN,GACpD,CAAC;UAED3B,MAAM,CAAC8C,MAAM,CAAChB,SAAS,IAAIrB,KAAK,EAAE,CAAC,EAAE,CAAC,MAAM,EAAExE,QAAQ,EAAEgG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAClExB,KAAK,EAAE;UACPC,MAAM,EAAE;QACV;;QAEA;QACA,IAAIuB,KAAK,CAAC,CAAC,CAAC,CAAC/B,IAAI,KAAKtH,KAAK,CAACyJ,cAAc,EAAE;UAC1C;UACA,MAAMU,IAAI,GAAG;YACX7C,IAAI,EAAE,UAAU;YAChB2C,OAAO,EAAE,KAAK;YACdvB,KAAK,EAAEL,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEe,KAAK,CAAC,CAAC,CAAC,CAACX,KAAK,CAAC;YACxC;YACAK,GAAG,EAAEtI;UACP,CAAC;UACD4C,QAAQ,GAAG8G,IAAI;UACf/C,MAAM,CAAC8C,MAAM,CAACrC,KAAK,EAAE,CAAC,EAAE,CAAC,OAAO,EAAEsC,IAAI,EAAEd,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;UAClDxB,KAAK,EAAE;UACPC,MAAM,EAAE;UACRqB,mBAAmB,GAAG1I,SAAS;UAC/B2I,QAAQ,GAAG,IAAI;QACjB;MACF;IACF;IAEAhC,MAAM,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACuB,OAAO,GAAGhB,UAAU;IACrC,OAAOnB,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS5G,MAAMA,CAACkJ,MAAM,EAAEC,GAAG,EAAE;IAC3B,OAAOC,IAAI;;IAEX;AACJ;AACA;AACA;AACA;IACI,SAASA,IAAIA,CAACC,KAAK,EAAE;MACnBvJ,KAAK,CAACoH,IAAI,CAAC,IAAI,EAAEgC,MAAM,CAACG,KAAK,CAAC,EAAEA,KAAK,CAAC;MACtC,IAAIF,GAAG,EAAEA,GAAG,CAACjC,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,SAASxI,MAAMA,CAAA,EAAG;IAChB,IAAI,CAAC0F,KAAK,CAACM,IAAI,CAAC;MAACT,IAAI,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAE,CAAC,CAAC;EACnD;;EAEA;AACF;AACA;EACE,SAASvG,KAAKA,CAACwJ,IAAI,EAAED,KAAK,EAAEE,YAAY,EAAE;IACxC,MAAMC,MAAM,GAAG,IAAI,CAACjD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAChDxI,MAAM,CAACoL,MAAM,EAAE,mBAAmB,CAAC;IACnCpL,MAAM,CAAC,UAAU,IAAIoL,MAAM,EAAE,mBAAmB,CAAC;IACjD;IACA,MAAMC,QAAQ,GAAGD,MAAM,CAACnD,QAAQ;IAChCoD,QAAQ,CAAC5C,IAAI,CAACyC,IAAI,CAAC;IACnB,IAAI,CAAC/C,KAAK,CAACM,IAAI,CAACyC,IAAI,CAAC;IACrB,IAAI,CAAC9C,UAAU,CAACK,IAAI,CAAC,CAACwC,KAAK,EAAEE,YAAY,IAAIhK,SAAS,CAAC,CAAC;IACxD+J,IAAI,CAAC/B,QAAQ,GAAG;MACdC,KAAK,EAAEC,KAAK,CAAC4B,KAAK,CAAC7B,KAAK,CAAC;MACzB;MACAK,GAAG,EAAEtI;IACP,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS6D,MAAMA,CAAC+F,GAAG,EAAE;IACnB,OAAOO,KAAK;;IAEZ;AACJ;AACA;AACA;AACA;IACI,SAASA,KAAKA,CAACL,KAAK,EAAE;MACpB,IAAIF,GAAG,EAAEA,GAAG,CAACjC,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;MAC9BlG,IAAI,CAAC+D,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IACxB;EACF;;EAEA;AACF;AACA;EACE,SAASlG,IAAIA,CAACkG,KAAK,EAAEM,WAAW,EAAE;IAChC,MAAML,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAACQ,GAAG,CAAC,CAAC;IAC7B3I,MAAM,CAACkL,IAAI,EAAE,iBAAiB,CAAC;IAC/B,MAAMF,IAAI,GAAG,IAAI,CAAC5C,UAAU,CAACO,GAAG,CAAC,CAAC;IAElC,IAAI,CAACqC,IAAI,EAAE;MACT,MAAM,IAAIQ,KAAK,CACb,gBAAgB,GACdP,KAAK,CAACjD,IAAI,GACV,KAAK,GACLpH,iBAAiB,CAAC;QAACwI,KAAK,EAAE6B,KAAK,CAAC7B,KAAK;QAAEK,GAAG,EAAEwB,KAAK,CAACxB;MAAG,CAAC,CAAC,GACvD,kBACJ,CAAC;IACH,CAAC,MAAM,IAAIuB,IAAI,CAAC,CAAC,CAAC,CAAChD,IAAI,KAAKiD,KAAK,CAACjD,IAAI,EAAE;MACtC,IAAIuD,WAAW,EAAE;QACfA,WAAW,CAACzC,IAAI,CAAC,IAAI,EAAEmC,KAAK,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,MAAMnC,OAAO,GAAGmC,IAAI,CAAC,CAAC,CAAC,IAAI9B,cAAc;QACzCL,OAAO,CAACC,IAAI,CAAC,IAAI,EAAEmC,KAAK,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;MACpC;IACF;IAEAhL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,UAAU,EAAE,8BAA8B,CAAC;IAChEhI,MAAM,CAACkL,IAAI,CAAC/B,QAAQ,EAAE,mCAAmC,CAAC;IAC1D+B,IAAI,CAAC/B,QAAQ,CAACM,GAAG,GAAGJ,KAAK,CAAC4B,KAAK,CAACxB,GAAG,CAAC;EACtC;;EAEA;AACF;AACA;EACE,SAASpB,MAAMA,CAAA,EAAG;IAChB,OAAOpI,QAAQ,CAAC,IAAI,CAACkI,KAAK,CAACQ,GAAG,CAAC,CAAC,CAAC;EACnC;;EAEA;EACA;EACA;;EAEA;AACF;AACA;AACA;EACE,SAASvE,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAACtB,IAAI,CAAC2I,2BAA2B,GAAG,IAAI;EAC9C;;EAEA;AACF;AACA;AACA;EACE,SAASxH,oBAAoBA,CAACgH,KAAK,EAAE;IACnC,IAAI,IAAI,CAACnI,IAAI,CAAC2I,2BAA2B,EAAE;MACzC,MAAMC,QAAQ,GAAG,IAAI,CAACvD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;MAClDxI,MAAM,CAAC0L,QAAQ,EAAE,yBAAyB,CAAC;MAC3C1L,MAAM,CAAC0L,QAAQ,CAAC1D,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;MAC1D0D,QAAQ,CAACtC,KAAK,GAAGuC,MAAM,CAACC,QAAQ,CAC9B,IAAI,CAAC3C,cAAc,CAACgC,KAAK,CAAC,EAC1BxK,SAAS,CAACoL,kBACZ,CAAC;MACD,IAAI,CAAC/I,IAAI,CAAC2I,2BAA2B,GAAGtK,SAAS;IACnD;EACF;;EAEA;AACF;AACA;AACA;EACE,SAAS6E,yBAAyBA,CAAA,EAAG;IACnC,MAAMlD,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IACtDkD,IAAI,CAACY,IAAI,GAAGhJ,IAAI;EAClB;;EAEA;AACF;AACA;AACA;EACE,SAASmD,yBAAyBA,CAAA,EAAG;IACnC,MAAMnD,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IACtDkD,IAAI,CAACa,IAAI,GAAGjJ,IAAI;EAClB;;EAEA;AACF;AACA;AACA;EACE,SAASiD,qBAAqBA,CAAA,EAAG;IAC/B;IACA,IAAI,IAAI,CAACjD,IAAI,CAACkJ,cAAc,EAAE;IAC9B,IAAI,CAACvJ,MAAM,CAAC,CAAC;IACb,IAAI,CAACK,IAAI,CAACkJ,cAAc,GAAG,IAAI;EACjC;;EAEA;AACF;AACA;AACA;EACE,SAASnG,gBAAgBA,CAAA,EAAG;IAC1B,MAAM/C,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDkD,IAAI,CAAClK,KAAK,GAAG8B,IAAI,CAACmJ,OAAO,CAAC,0BAA0B,EAAE,EAAE,CAAC;IACzD,IAAI,CAACnJ,IAAI,CAACkJ,cAAc,GAAG7K,SAAS;EACtC;;EAEA;AACF;AACA;AACA;EACE,SAAS+E,kBAAkBA,CAAA,EAAG;IAC5B,MAAMpD,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDkD,IAAI,CAAClK,KAAK,GAAG8B,IAAI,CAACmJ,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;EACE,SAAS5F,2BAA2BA,CAAC4E,KAAK,EAAE;IAC1C,MAAMnH,KAAK,GAAG,IAAI,CAACuE,MAAM,CAAC,CAAC;IAC3B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,YAAY,EAAE,8BAA8B,CAAC;IAElEkD,IAAI,CAACpH,KAAK,GAAGA,KAAK;IAClBoH,IAAI,CAACgB,UAAU,GAAG3L,mBAAmB,CACnC,IAAI,CAAC0I,cAAc,CAACgC,KAAK,CAC3B,CAAC,CAACkB,WAAW,CAAC,CAAC;EACjB;;EAEA;AACF;AACA;AACA;EACE,SAAS7F,2BAA2BA,CAAA,EAAG;IACrC,MAAMxD,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,YAAY,EAAE,8BAA8B,CAAC;IAElEkD,IAAI,CAACkB,KAAK,GAAGtJ,IAAI;EACnB;;EAEA;AACF;AACA;AACA;EACE,SAASsD,iCAAiCA,CAAA,EAAG;IAC3C,MAAMtD,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,YAAY,EAAE,8BAA8B,CAAC;IAElEkD,IAAI,CAACmB,GAAG,GAAGvJ,IAAI;EACjB;;EAEA;AACF;AACA;AACA;EACE,SAASoC,wBAAwBA,CAAC+F,KAAK,EAAE;IACvC,MAAMC,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,SAAS,EAAE,2BAA2B,CAAC;IAE5D,IAAI,CAACkD,IAAI,CAACoB,KAAK,EAAE;MACf,MAAMA,KAAK,GAAG,IAAI,CAACrD,cAAc,CAACgC,KAAK,CAAC,CAACzC,MAAM;MAE/CxI,MAAM,CACJsM,KAAK,KAAK,CAAC,IACTA,KAAK,KAAK,CAAC,IACXA,KAAK,KAAK,CAAC,IACXA,KAAK,KAAK,CAAC,IACXA,KAAK,KAAK,CAAC,IACXA,KAAK,KAAK,CAAC,EACb,sCACF,CAAC;MAEDpB,IAAI,CAACoB,KAAK,GAAGA,KAAK;IACpB;EACF;;EAEA;AACF;AACA;AACA;EACE,SAAS5E,uBAAuBA,CAAA,EAAG;IACjC,IAAI,CAAC5E,IAAI,CAACyJ,4BAA4B,GAAG,IAAI;EAC/C;;EAEA;AACF;AACA;AACA;EACE,SAAS/E,+BAA+BA,CAACyD,KAAK,EAAE;IAC9C,MAAMC,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,SAAS,EAAE,2BAA2B,CAAC;IAE5DkD,IAAI,CAACoB,KAAK,GACR,IAAI,CAACrD,cAAc,CAACgC,KAAK,CAAC,CAACuB,WAAW,CAAC,CAAC,CAAC,KAAKhM,KAAK,CAACiM,QAAQ,GAAG,CAAC,GAAG,CAAC;EACxE;;EAEA;AACF;AACA;AACA;EACE,SAASnF,mBAAmBA,CAAA,EAAG;IAC7B,IAAI,CAACxE,IAAI,CAACyJ,4BAA4B,GAAGpL,SAAS;EACpD;;EAEA;AACF;AACA;AACA;;EAEE,SAASY,WAAWA,CAACkJ,KAAK,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAAC,UAAU,IAAIkL,IAAI,EAAE,0BAA0B,CAAC;IACtD;IACA,MAAMG,QAAQ,GAAGH,IAAI,CAACjD,QAAQ;IAE9B,IAAIS,IAAI,GAAG2C,QAAQ,CAACA,QAAQ,CAAC7C,MAAM,GAAG,CAAC,CAAC;IAExC,IAAI,CAACE,IAAI,IAAIA,IAAI,CAACV,IAAI,KAAK,MAAM,EAAE;MACjC;MACAU,IAAI,GAAGgE,IAAI,CAAC,CAAC;MACbhE,IAAI,CAACS,QAAQ,GAAG;QACdC,KAAK,EAAEC,KAAK,CAAC4B,KAAK,CAAC7B,KAAK,CAAC;QACzB;QACAK,GAAG,EAAEtI;MACP,CAAC;MACDkK,QAAQ,CAAC5C,IAAI,CAACC,IAAI,CAAC;IACrB;IAEA,IAAI,CAACP,KAAK,CAACM,IAAI,CAACC,IAAI,CAAC;EACvB;;EAEA;AACF;AACA;AACA;;EAEE,SAASpD,UAAUA,CAAC2F,KAAK,EAAE;IACzB,MAAMvC,IAAI,GAAG,IAAI,CAACP,KAAK,CAACQ,GAAG,CAAC,CAAC;IAC7B3I,MAAM,CAAC0I,IAAI,EAAE,sCAAsC,CAAC;IACpD1I,MAAM,CAAC,OAAO,IAAI0I,IAAI,EAAE,yCAAyC,CAAC;IAClE1I,MAAM,CAAC0I,IAAI,CAACS,QAAQ,EAAE,0CAA0C,CAAC;IACjET,IAAI,CAAC1H,KAAK,IAAI,IAAI,CAACiI,cAAc,CAACgC,KAAK,CAAC;IACxCvC,IAAI,CAACS,QAAQ,CAACM,GAAG,GAAGJ,KAAK,CAAC4B,KAAK,CAACxB,GAAG,CAAC;EACtC;;EAEA;AACF;AACA;AACA;;EAEE,SAAS1C,gBAAgBA,CAACkE,KAAK,EAAE;IAC/B,MAAM/C,OAAO,GAAG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IACjDxI,MAAM,CAACkI,OAAO,EAAE,iBAAiB,CAAC;;IAElC;IACA,IAAI,IAAI,CAACpF,IAAI,CAAC6J,WAAW,EAAE;MACzB3M,MAAM,CAAC,UAAU,IAAIkI,OAAO,EAAE,mBAAmB,CAAC;MAClD,MAAMQ,IAAI,GAAGR,OAAO,CAACD,QAAQ,CAACC,OAAO,CAACD,QAAQ,CAACO,MAAM,GAAG,CAAC,CAAC;MAC1DxI,MAAM,CAAC0I,IAAI,CAACS,QAAQ,EAAE,2CAA2C,CAAC;MAClET,IAAI,CAACS,QAAQ,CAACM,GAAG,GAAGJ,KAAK,CAAC4B,KAAK,CAACxB,GAAG,CAAC;MACpC,IAAI,CAAC3G,IAAI,CAAC6J,WAAW,GAAGxL,SAAS;MACjC;IACF;IAEA,IACE,CAAC,IAAI,CAAC2B,IAAI,CAACyJ,4BAA4B,IACvChL,MAAM,CAACE,cAAc,CAACmL,QAAQ,CAAC1E,OAAO,CAACF,IAAI,CAAC,EAC5C;MACAjG,WAAW,CAAC+G,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;MAC7B3F,UAAU,CAACwD,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IAC9B;EACF;;EAEA;AACF;AACA;AACA;;EAEE,SAAS1E,eAAeA,CAAA,EAAG;IACzB,IAAI,CAACzD,IAAI,CAAC6J,WAAW,GAAG,IAAI;EAC9B;;EAEA;AACF;AACA;AACA;;EAEE,SAASnG,cAAcA,CAAA,EAAG;IACxB,MAAM1D,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDkD,IAAI,CAAClK,KAAK,GAAG8B,IAAI;EACnB;;EAEA;AACF;AACA;AACA;;EAEE,SAAS2D,cAAcA,CAAA,EAAG;IACxB,MAAM3D,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDkD,IAAI,CAAClK,KAAK,GAAG8B,IAAI;EACnB;;EAEA;AACF;AACA;AACA;;EAEE,SAASqD,cAAcA,CAAA,EAAG;IACxB,MAAMrD,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,YAAY,EAAE,+BAA+B,CAAC;IAEnEkD,IAAI,CAAClK,KAAK,GAAG8B,IAAI;EACnB;;EAEA;AACF;AACA;AACA;;EAEE,SAASkE,UAAUA,CAAA,EAAG;IACpB,MAAMkE,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;;IAEtD;IACA;;IAEA;IACA,IAAI,IAAI,CAAClF,IAAI,CAAC+J,WAAW,EAAE;MACzB;MACA,MAAMC,aAAa,GAAG,IAAI,CAAChK,IAAI,CAACgK,aAAa,IAAI,UAAU;MAE3D5B,IAAI,CAAClD,IAAI,IAAI,WAAW;MACxB;MACAkD,IAAI,CAAC4B,aAAa,GAAGA,aAAa;MAClC;MACA,OAAO5B,IAAI,CAACmB,GAAG;MACf,OAAOnB,IAAI,CAACkB,KAAK;IACnB,CAAC,MAAM;MACL;MACA,OAAOlB,IAAI,CAACgB,UAAU;MACtB;MACA,OAAOhB,IAAI,CAACpH,KAAK;IACnB;IAEA,IAAI,CAAChB,IAAI,CAACgK,aAAa,GAAG3L,SAAS;EACrC;;EAEA;AACF;AACA;AACA;;EAEE,SAASuF,WAAWA,CAAA,EAAG;IACrB,MAAMwE,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,OAAO,EAAE,yBAAyB,CAAC;;IAExD;IACA;;IAEA;IACA,IAAI,IAAI,CAAClF,IAAI,CAAC+J,WAAW,EAAE;MACzB;MACA,MAAMC,aAAa,GAAG,IAAI,CAAChK,IAAI,CAACgK,aAAa,IAAI,UAAU;MAE3D5B,IAAI,CAAClD,IAAI,IAAI,WAAW;MACxB;MACAkD,IAAI,CAAC4B,aAAa,GAAGA,aAAa;MAClC;MACA,OAAO5B,IAAI,CAACmB,GAAG;MACf,OAAOnB,IAAI,CAACkB,KAAK;IACnB,CAAC,MAAM;MACL;MACA,OAAOlB,IAAI,CAACgB,UAAU;MACtB;MACA,OAAOhB,IAAI,CAACpH,KAAK;IACnB;IAEA,IAAI,CAAChB,IAAI,CAACgK,aAAa,GAAG3L,SAAS;EACrC;;EAEA;AACF;AACA;AACA;;EAEE,SAAS0F,eAAeA,CAACoE,KAAK,EAAE;IAC9B,MAAM8B,MAAM,GAAG,IAAI,CAAC9D,cAAc,CAACgC,KAAK,CAAC;IACzC,MAAMS,QAAQ,GAAG,IAAI,CAACvD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAClDxI,MAAM,CAAC0L,QAAQ,EAAE,4BAA4B,CAAC;IAC9C1L,MAAM,CACJ0L,QAAQ,CAAC1D,IAAI,KAAK,OAAO,IAAI0D,QAAQ,CAAC1D,IAAI,KAAK,MAAM,EACrD,iCACF,CAAC;;IAED;IACA;IACA0D,QAAQ,CAAC5H,KAAK,GAAGxD,YAAY,CAACyM,MAAM,CAAC;IACrC;IACArB,QAAQ,CAACQ,UAAU,GAAG3L,mBAAmB,CAACwM,MAAM,CAAC,CAACZ,WAAW,CAAC,CAAC;EACjE;;EAEA;AACF;AACA;AACA;;EAEE,SAASxF,WAAWA,CAAA,EAAG;IACrB,MAAMqG,QAAQ,GAAG,IAAI,CAAC7E,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAClDxI,MAAM,CAACgN,QAAQ,EAAE,wBAAwB,CAAC;IAC1ChN,MAAM,CAACgN,QAAQ,CAAChF,IAAI,KAAK,UAAU,EAAE,4BAA4B,CAAC;IAClE,MAAMhH,KAAK,GAAG,IAAI,CAACqH,MAAM,CAAC,CAAC;IAC3B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CACJkL,IAAI,CAAClD,IAAI,KAAK,OAAO,IAAIkD,IAAI,CAAClD,IAAI,KAAK,MAAM,EAC7C,iCACF,CAAC;;IAED;IACA,IAAI,CAAClF,IAAI,CAAC+J,WAAW,GAAG,IAAI;IAE5B,IAAI3B,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE;MACxB;MACA,MAAMC,QAAQ,GAAG+E,QAAQ,CAAC/E,QAAQ;MAElCiD,IAAI,CAACjD,QAAQ,GAAGA,QAAQ;IAC1B,CAAC,MAAM;MACLiD,IAAI,CAAC+B,GAAG,GAAGjM,KAAK;IAClB;EACF;;EAEA;AACF;AACA;AACA;;EAEE,SAASkG,+BAA+BA,CAAA,EAAG;IACzC,MAAMpE,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CACJkL,IAAI,CAAClD,IAAI,KAAK,OAAO,IAAIkD,IAAI,CAAClD,IAAI,KAAK,MAAM,EAC7C,iCACF,CAAC;IACDkD,IAAI,CAACmB,GAAG,GAAGvJ,IAAI;EACjB;;EAEA;AACF;AACA;AACA;;EAEE,SAASqE,yBAAyBA,CAAA,EAAG;IACnC,MAAMrE,IAAI,GAAG,IAAI,CAACuF,MAAM,CAAC,CAAC;IAC1B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CACJkL,IAAI,CAAClD,IAAI,KAAK,OAAO,IAAIkD,IAAI,CAAClD,IAAI,KAAK,MAAM,EAC7C,iCACF,CAAC;IACDkD,IAAI,CAACkB,KAAK,GAAGtJ,IAAI;EACnB;;EAEA;AACF;AACA;AACA;;EAEE,SAASuE,cAAcA,CAAA,EAAG;IACxB,IAAI,CAACvE,IAAI,CAAC+J,WAAW,GAAG1L,SAAS;EACnC;;EAEA;AACF;AACA;AACA;;EAEE,SAASqD,gBAAgBA,CAAA,EAAG;IAC1B,IAAI,CAAC1B,IAAI,CAACgK,aAAa,GAAG,WAAW;EACvC;;EAEA;AACF;AACA;AACA;;EAEE,SAAS7F,qBAAqBA,CAACgE,KAAK,EAAE;IACpC,MAAMnH,KAAK,GAAG,IAAI,CAACuE,MAAM,CAAC,CAAC;IAC3B,MAAM6C,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CACJkL,IAAI,CAAClD,IAAI,KAAK,OAAO,IAAIkD,IAAI,CAAClD,IAAI,KAAK,MAAM,EAC7C,qDACF,CAAC;;IAED;IACA;IACAkD,IAAI,CAACpH,KAAK,GAAGA,KAAK;IAClB;IACAoH,IAAI,CAACgB,UAAU,GAAG3L,mBAAmB,CACnC,IAAI,CAAC0I,cAAc,CAACgC,KAAK,CAC3B,CAAC,CAACkB,WAAW,CAAC,CAAC;IACf,IAAI,CAACrJ,IAAI,CAACgK,aAAa,GAAG,MAAM;EAClC;;EAEA;AACF;AACA;AACA;;EAEE,SAAStH,8BAA8BA,CAACyF,KAAK,EAAE;IAC7CjL,MAAM,CACJiL,KAAK,CAACjD,IAAI,KAAK,iCAAiC,IAC9CiD,KAAK,CAACjD,IAAI,KAAK,qCACnB,CAAC;IACD,IAAI,CAAClF,IAAI,CAACoK,sBAAsB,GAAGjC,KAAK,CAACjD,IAAI;EAC/C;;EAEA;AACF;AACA;AACA;EACE,SAASrC,6BAA6BA,CAACsF,KAAK,EAAE;IAC5C,MAAMnI,IAAI,GAAG,IAAI,CAACmG,cAAc,CAACgC,KAAK,CAAC;IACvC,MAAMjD,IAAI,GAAG,IAAI,CAAClF,IAAI,CAACoK,sBAAsB;IAC7C;IACA,IAAIlM,KAAK;IAET,IAAIgH,IAAI,EAAE;MACRhH,KAAK,GAAGX,+BAA+B,CACrCyC,IAAI,EACJkF,IAAI,KAAKtH,KAAK,CAAC+E,+BAA+B,GAC1ChF,SAAS,CAACoL,kBAAkB,GAC5BpL,SAAS,CAAC0M,sBAChB,CAAC;MACD,IAAI,CAACrK,IAAI,CAACoK,sBAAsB,GAAG/L,SAAS;IAC9C,CAAC,MAAM;MACL,MAAMiM,MAAM,GAAGzM,6BAA6B,CAACmC,IAAI,CAAC;MAClD9C,MAAM,CAACoN,MAAM,KAAK,KAAK,EAAE,8BAA8B,CAAC;MACxDpM,KAAK,GAAGoM,MAAM;IAChB;IAEA,MAAM1E,IAAI,GAAG,IAAI,CAACP,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAAC0I,IAAI,EAAE,iBAAiB,CAAC;IAC/B1I,MAAM,CAAC,OAAO,IAAI0I,IAAI,EAAE,uBAAuB,CAAC;IAChDA,IAAI,CAAC1H,KAAK,IAAIA,KAAK;EACrB;;EAEA;AACF;AACA;AACA;EACE,SAAS4E,wBAAwBA,CAACqF,KAAK,EAAE;IACvC,MAAMvC,IAAI,GAAG,IAAI,CAACP,KAAK,CAACQ,GAAG,CAAC,CAAC;IAC7B3I,MAAM,CAAC0I,IAAI,EAAE,iBAAiB,CAAC;IAC/B1I,MAAM,CAAC0I,IAAI,CAACS,QAAQ,EAAE,0BAA0B,CAAC;IACjDT,IAAI,CAACS,QAAQ,CAACM,GAAG,GAAGJ,KAAK,CAAC4B,KAAK,CAACxB,GAAG,CAAC;EACtC;;EAEA;AACF;AACA;AACA;EACE,SAASrE,sBAAsBA,CAAC6F,KAAK,EAAE;IACrC3F,UAAU,CAACwD,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IAC5B,MAAMC,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDkD,IAAI,CAACmB,GAAG,GAAG,IAAI,CAACpD,cAAc,CAACgC,KAAK,CAAC;EACvC;;EAEA;AACF;AACA;AACA;EACE,SAAS9F,mBAAmBA,CAAC8F,KAAK,EAAE;IAClC3F,UAAU,CAACwD,IAAI,CAAC,IAAI,EAAEmC,KAAK,CAAC;IAC5B,MAAMC,IAAI,GAAG,IAAI,CAAC/C,KAAK,CAAC,IAAI,CAACA,KAAK,CAACK,MAAM,GAAG,CAAC,CAAC;IAC9CxI,MAAM,CAACkL,IAAI,EAAE,wBAAwB,CAAC;IACtClL,MAAM,CAACkL,IAAI,CAAClD,IAAI,KAAK,MAAM,EAAE,wBAAwB,CAAC;IAEtDkD,IAAI,CAACmB,GAAG,GAAG,SAAS,GAAG,IAAI,CAACpD,cAAc,CAACgC,KAAK,CAAC;EACnD;;EAEA;EACA;EACA;;EAEA;EACA,SAAS9I,UAAUA,CAAA,EAAG;IACpB,OAAO;MAAC6F,IAAI,EAAE,YAAY;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAC3C;;EAEA;EACA,SAAS1F,QAAQA,CAAA,EAAG;IAClB,OAAO;MAACyF,IAAI,EAAE,MAAM;MAAE8D,IAAI,EAAE,IAAI;MAAEC,IAAI,EAAE,IAAI;MAAE/K,KAAK,EAAE;IAAE,CAAC;EAC1D;;EAEA;EACA,SAAS4B,QAAQA,CAAA,EAAG;IAClB,OAAO;MAACoF,IAAI,EAAE,YAAY;MAAEhH,KAAK,EAAE;IAAE,CAAC;EACxC;;EAEA;EACA,SAASgC,UAAUA,CAAA,EAAG;IACpB,OAAO;MACLgF,IAAI,EAAE,YAAY;MAClBkE,UAAU,EAAE,EAAE;MACdpI,KAAK,EAAE,IAAI;MACXsI,KAAK,EAAE,IAAI;MACXC,GAAG,EAAE;IACP,CAAC;EACH;;EAEA;EACA,SAASjJ,QAAQA,CAAA,EAAG;IAClB,OAAO;MAAC4E,IAAI,EAAE,UAAU;MAAEC,QAAQ,EAAE;IAAE,CAAC;EACzC;;EAEA;EACA,SAAS/F,OAAOA,CAAA,EAAG;IACjB,OAAO;MACL8F,IAAI,EAAE,SAAS;MACf;MACAsE,KAAK,EAAE,CAAC;MACRrE,QAAQ,EAAE;IACZ,CAAC;EACH;;EAEA;EACA,SAAS3E,SAASA,CAAA,EAAG;IACnB,OAAO;MAAC0E,IAAI,EAAE;IAAO,CAAC;EACxB;;EAEA;EACA,SAASvE,IAAIA,CAAA,EAAG;IACd,OAAO;MAACuE,IAAI,EAAE,MAAM;MAAEhH,KAAK,EAAE;IAAE,CAAC;EAClC;;EAEA;EACA,SAAS6C,KAAKA,CAAA,EAAG;IACf,OAAO;MAACmE,IAAI,EAAE,OAAO;MAAEoE,KAAK,EAAE,IAAI;MAAEC,GAAG,EAAE,EAAE;MAAEY,GAAG,EAAE;IAAI,CAAC;EACzD;;EAEA;EACA,SAASpL,IAAIA,CAAA,EAAG;IACd,OAAO;MAACmG,IAAI,EAAE,MAAM;MAAEoE,KAAK,EAAE,IAAI;MAAEC,GAAG,EAAE,EAAE;MAAEpE,QAAQ,EAAE;IAAE,CAAC;EAC3D;;EAEA;AACF;AACA;AACA;EACE,SAAS9D,IAAIA,CAAC8G,KAAK,EAAE;IACnB,OAAO;MACLjD,IAAI,EAAE,MAAM;MACZqF,OAAO,EAAEpC,KAAK,CAACjD,IAAI,KAAK,aAAa;MACrCoB,KAAK,EAAE,IAAI;MACXkE,MAAM,EAAErC,KAAK,CAACN,OAAO;MACrB1C,QAAQ,EAAE;IACZ,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACE,SAASlE,QAAQA,CAACkH,KAAK,EAAE;IACvB,OAAO;MACLjD,IAAI,EAAE,UAAU;MAChBsF,MAAM,EAAErC,KAAK,CAACN,OAAO;MACrB4C,OAAO,EAAE,IAAI;MACbtF,QAAQ,EAAE;IACZ,CAAC;EACH;;EAEA;EACA,SAAS3D,SAASA,CAAA,EAAG;IACnB,OAAO;MAAC0D,IAAI,EAAE,WAAW;MAAEC,QAAQ,EAAE;IAAE,CAAC;EAC1C;;EAEA;EACA,SAASpD,MAAMA,CAAA,EAAG;IAChB,OAAO;MAACmD,IAAI,EAAE,QAAQ;MAAEC,QAAQ,EAAE;IAAE,CAAC;EACvC;;EAEA;EACA,SAASyE,IAAIA,CAAA,EAAG;IACd,OAAO;MAAC1E,IAAI,EAAE,MAAM;MAAEhH,KAAK,EAAE;IAAE,CAAC;EAClC;;EAEA;EACA,SAAS8D,aAAaA,CAAA,EAAG;IACvB,OAAO;MAACkD,IAAI,EAAE;IAAe,CAAC;EAChC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,KAAKA,CAACmE,CAAC,EAAE;EAChB,OAAO;IAAClE,IAAI,EAAEkE,CAAC,CAAClE,IAAI;IAAEC,MAAM,EAAEiE,CAAC,CAACjE,MAAM;IAAEC,MAAM,EAAEgE,CAAC,CAAChE;EAAM,CAAC;AAC3D;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS7B,SAASA,CAAC8F,QAAQ,EAAEC,UAAU,EAAE;EACvC,IAAInF,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGmF,UAAU,CAAClF,MAAM,EAAE;IAClC,MAAMxH,KAAK,GAAG0M,UAAU,CAACnF,KAAK,CAAC;IAE/B,IAAIoF,KAAK,CAACC,OAAO,CAAC5M,KAAK,CAAC,EAAE;MACxB2G,SAAS,CAAC8F,QAAQ,EAAEzM,KAAK,CAAC;IAC5B,CAAC,MAAM;MACL6M,SAAS,CAACJ,QAAQ,EAAEzM,KAAK,CAAC;IAC5B;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS6M,SAASA,CAACJ,QAAQ,EAAEI,SAAS,EAAE;EACtC;EACA,IAAIC,GAAG;EAEP,KAAKA,GAAG,IAAID,SAAS,EAAE;IACrB,IAAIhN,GAAG,CAACiI,IAAI,CAAC+E,SAAS,EAAEC,GAAG,CAAC,EAAE;MAC5B,QAAQA,GAAG;QACT,KAAK,gBAAgB;UAAE;YACrB,MAAMC,KAAK,GAAGF,SAAS,CAACC,GAAG,CAAC;YAC5B,IAAIC,KAAK,EAAE;cACTN,QAAQ,CAACK,GAAG,CAAC,CAACrF,IAAI,CAAC,GAAGsF,KAAK,CAAC;YAC9B;YAEA;UACF;QAEA,KAAK,YAAY;UAAE;YACjB,MAAMA,KAAK,GAAGF,SAAS,CAACC,GAAG,CAAC;YAC5B,IAAIC,KAAK,EAAE;cACTN,QAAQ,CAACK,GAAG,CAAC,CAACrF,IAAI,CAAC,GAAGsF,KAAK,CAAC;YAC9B;YAEA;UACF;QAEA,KAAK,OAAO;QACZ,KAAK,MAAM;UAAE;YACX,MAAMA,KAAK,GAAGF,SAAS,CAACC,GAAG,CAAC;YAC5B,IAAIC,KAAK,EAAE;cACThF,MAAM,CAACC,MAAM,CAACyE,QAAQ,CAACK,GAAG,CAAC,EAAEC,KAAK,CAAC;YACrC;YAEA;UACF;QACA;MACF;IACF;EACF;AACF;;AAEA;AACA,SAAS7E,cAAcA,CAAC8E,IAAI,EAAED,KAAK,EAAE;EACnC,IAAIC,IAAI,EAAE;IACR,MAAM,IAAIxC,KAAK,CACb,gBAAgB,GACdwC,IAAI,CAAChG,IAAI,GACT,KAAK,GACLpH,iBAAiB,CAAC;MAACwI,KAAK,EAAE4E,IAAI,CAAC5E,KAAK;MAAEK,GAAG,EAAEuE,IAAI,CAACvE;IAAG,CAAC,CAAC,GACrD,yBAAyB,GACzBsE,KAAK,CAAC/F,IAAI,GACV,KAAK,GACLpH,iBAAiB,CAAC;MAACwI,KAAK,EAAE2E,KAAK,CAAC3E,KAAK;MAAEK,GAAG,EAAEsE,KAAK,CAACtE;IAAG,CAAC,CAAC,GACvD,WACJ,CAAC;EACH,CAAC,MAAM;IACL,MAAM,IAAI+B,KAAK,CACb,mCAAmC,GACjCuC,KAAK,CAAC/F,IAAI,GACV,KAAK,GACLpH,iBAAiB,CAAC;MAACwI,KAAK,EAAE2E,KAAK,CAAC3E,KAAK;MAAEK,GAAG,EAAEsE,KAAK,CAACtE;IAAG,CAAC,CAAC,GACvD,iBACJ,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}