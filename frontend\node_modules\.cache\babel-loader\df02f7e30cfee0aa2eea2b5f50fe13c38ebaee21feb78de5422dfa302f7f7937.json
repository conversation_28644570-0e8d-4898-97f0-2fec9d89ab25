{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n'';\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function code(state, node) {\n  const value = node.value ? node.value + '\\n' : '';\n  /** @type {Properties} */\n  const properties = {};\n  if (node.lang) {\n    properties.className = ['language-' + node.lang];\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{\n      type: 'text',\n      value\n    }]\n  };\n  if (node.meta) {\n    result.data = {\n      meta: node.meta\n    };\n  }\n  state.patch(node, result);\n  result = state.applyData(node, result);\n\n  // Create `<pre>`.\n  result = {\n    type: 'element',\n    tagName: 'pre',\n    properties: {},\n    children: [result]\n  };\n  state.patch(node, result);\n  return result;\n}", "map": {"version": 3, "names": ["code", "state", "node", "value", "properties", "lang", "className", "result", "type", "tagName", "children", "meta", "data", "patch", "applyData"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/mdast-util-to-hast/lib/handlers/code.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function code(state, node) {\n  const value = node.value ? node.value + '\\n' : ''\n  /** @type {Properties} */\n  const properties = {}\n\n  if (node.lang) {\n    properties.className = ['language-' + node.lang]\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{type: 'text', value}]\n  }\n\n  if (node.meta) {\n    result.data = {meta: node.meta}\n  }\n\n  state.patch(node, result)\n  result = state.applyData(node, result)\n\n  // Create `<pre>`.\n  result = {type: 'element', tagName: 'pre', properties: {}, children: [result]}\n  state.patch(node, result)\n  return result\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC,MAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACC,KAAK,GAAG,IAAI,GAAG,EAAE;EACjD;EACA,MAAMC,UAAU,GAAG,CAAC,CAAC;EAErB,IAAIF,IAAI,CAACG,IAAI,EAAE;IACbD,UAAU,CAACE,SAAS,GAAG,CAAC,WAAW,GAAGJ,IAAI,CAACG,IAAI,CAAC;EAClD;;EAEA;EACA;EACA,IAAIE,MAAM,GAAG;IACXC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,MAAM;IACfL,UAAU;IACVM,QAAQ,EAAE,CAAC;MAACF,IAAI,EAAE,MAAM;MAAEL;IAAK,CAAC;EAClC,CAAC;EAED,IAAID,IAAI,CAACS,IAAI,EAAE;IACbJ,MAAM,CAACK,IAAI,GAAG;MAACD,IAAI,EAAET,IAAI,CAACS;IAAI,CAAC;EACjC;EAEAV,KAAK,CAACY,KAAK,CAACX,IAAI,EAAEK,MAAM,CAAC;EACzBA,MAAM,GAAGN,KAAK,CAACa,SAAS,CAACZ,IAAI,EAAEK,MAAM,CAAC;;EAEtC;EACAA,MAAM,GAAG;IAACC,IAAI,EAAE,SAAS;IAAEC,OAAO,EAAE,KAAK;IAAEL,UAAU,EAAE,CAAC,CAAC;IAAEM,QAAQ,EAAE,CAACH,MAAM;EAAC,CAAC;EAC9EN,KAAK,CAACY,KAAK,CAACX,IAAI,EAAEK,MAAM,CAAC;EACzB,OAAOA,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}