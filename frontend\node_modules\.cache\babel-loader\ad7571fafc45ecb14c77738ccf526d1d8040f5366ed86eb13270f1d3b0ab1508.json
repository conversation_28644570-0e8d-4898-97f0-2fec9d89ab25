{"ast": null, "code": "/**\n * @import {Effects, State} from 'micromark-util-types'\n */\n\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { types } from 'micromark-util-symbol';\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   line endings or spaces in markdown are often optional, in which case this\n *     factory can be used and `ok` will be switched to whether spaces were found\n *     or not\n * *   one line ending or space can be detected with\n *     `markdownLineEndingOrSpace(code)` right before using `factoryWhitespace`\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @returns {State}\n *   Start state.\n */\nexport function factoryWhitespace(effects, ok) {\n  /** @type {boolean} */\n  let seen;\n  return start;\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding);\n      effects.consume(code);\n      effects.exit(types.lineEnding);\n      seen = true;\n      return start;\n    }\n    if (markdownSpace(code)) {\n      return factorySpace(effects, start, seen ? types.linePrefix : types.lineSuffix)(code);\n    }\n    return ok(code);\n  }\n}", "map": {"version": 3, "names": ["factorySpace", "markdownLineEnding", "markdownSpace", "types", "factoryWhitespace", "effects", "ok", "seen", "start", "code", "enter", "lineEnding", "consume", "exit", "linePrefix", "lineSuffix"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-factory-whitespace/dev/index.js"], "sourcesContent": ["/**\n * @import {Effects, State} from 'micromark-util-types'\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {types} from 'micromark-util-symbol'\n\n/**\n * Parse spaces and tabs.\n *\n * There is no `nok` parameter:\n *\n * *   line endings or spaces in markdown are often optional, in which case this\n *     factory can be used and `ok` will be switched to whether spaces were found\n *     or not\n * *   one line ending or space can be detected with\n *     `markdownLineEndingOrSpace(code)` right before using `factoryWhitespace`\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @returns {State}\n *   Start state.\n */\nexport function factoryWhitespace(effects, ok) {\n  /** @type {boolean} */\n  let seen\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      seen = true\n      return start\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(\n        effects,\n        start,\n        seen ? types.linePrefix : types.lineSuffix\n      )(code)\n    }\n\n    return ok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,EAAEC,aAAa,QAAO,0BAA0B;AAC1E,SAAQC,KAAK,QAAO,uBAAuB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAEC,EAAE,EAAE;EAC7C;EACA,IAAIC,IAAI;EAER,OAAOC,KAAK;;EAEZ;EACA,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIR,kBAAkB,CAACQ,IAAI,CAAC,EAAE;MAC5BJ,OAAO,CAACK,KAAK,CAACP,KAAK,CAACQ,UAAU,CAAC;MAC/BN,OAAO,CAACO,OAAO,CAACH,IAAI,CAAC;MACrBJ,OAAO,CAACQ,IAAI,CAACV,KAAK,CAACQ,UAAU,CAAC;MAC9BJ,IAAI,GAAG,IAAI;MACX,OAAOC,KAAK;IACd;IAEA,IAAIN,aAAa,CAACO,IAAI,CAAC,EAAE;MACvB,OAAOT,YAAY,CACjBK,OAAO,EACPG,KAAK,EACLD,IAAI,GAAGJ,KAAK,CAACW,UAAU,GAAGX,KAAK,CAACY,UAClC,CAAC,CAACN,IAAI,CAAC;IACT;IAEA,OAAOH,EAAE,CAACG,IAAI,CAAC;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}