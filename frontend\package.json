{"name": "knowledge-platform-frontend", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "react-scripts": "5.0.1", "axios": "^1.6.0", "react-markdown": "^9.0.1", "react-hook-form": "^7.47.0", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.5", "autoprefixer": "^10.4.16", "postcss": "^8.4.31"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}