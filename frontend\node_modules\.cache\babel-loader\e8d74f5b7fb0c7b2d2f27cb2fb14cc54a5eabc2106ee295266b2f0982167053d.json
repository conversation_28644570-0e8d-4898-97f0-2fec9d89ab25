{"ast": null, "code": "// Note: types exported from `index.d.ts`\nexport { CONTINUE, EXIT, SKIP, visit } from './lib/index.js';", "map": {"version": 3, "names": ["CONTINUE", "EXIT", "SKIP", "visit"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/unist-util-visit/index.js"], "sourcesContent": ["// Note: types exported from `index.d.ts`\nexport {CONTINUE, EXIT, SKIP, visit} from './lib/index.js'\n"], "mappings": "AAAA;AACA,SAAQA,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}