{"ast": null, "code": "/**\n * @import {Schema} from 'property-information'\n */\n\nimport { DefinedInfo } from './util/defined-info.js';\nimport { Info } from './util/info.js';\nimport { normalize } from './normalize.js';\nconst cap = /[A-Z]/g;\nconst dash = /-[a-z]/g;\nconst valid = /^data[-\\w.:]+$/i;\n\n/**\n * Look up info on a property.\n *\n * In most cases the given `schema` contains info on the property.\n * All standard,\n * most legacy,\n * and some non-standard properties are supported.\n * For these cases,\n * the returned `Info` has hints about the value of the property.\n *\n * `name` can also be a valid data attribute or property,\n * in which case an `Info` object with the correctly cased `attribute` and\n * `property` is returned.\n *\n * `name` can be an unknown attribute,\n * in which case an `Info` object with `attribute` and `property` set to the\n * given name is returned.\n * It is not recommended to provide unsupported legacy or recently specced\n * properties.\n *\n *\n * @param {Schema} schema\n *   Schema;\n *   either the `html` or `svg` export.\n * @param {string} value\n *   An attribute-like or property-like name;\n *   it will be passed through `normalize` to hopefully find the correct info.\n * @returns {Info}\n *   Info.\n */\nexport function find(schema, value) {\n  const normal = normalize(value);\n  let property = value;\n  let Type = Info;\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]];\n  }\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase);\n      property = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1);\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4);\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab);\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes;\n        }\n        value = 'data' + dashes;\n      }\n    }\n    Type = DefinedInfo;\n  }\n  return new Type(property, value);\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Kebab.\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase();\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Camel.\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase();\n}", "map": {"version": 3, "names": ["DefinedInfo", "Info", "normalize", "cap", "dash", "valid", "find", "schema", "value", "normal", "property", "Type", "length", "slice", "test", "char<PERSON>t", "rest", "replace", "camelcase", "toUpperCase", "dashes", "kebab", "$0", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/property-information/lib/find.js"], "sourcesContent": ["/**\n * @import {Schema} from 'property-information'\n */\n\nimport {DefinedInfo} from './util/defined-info.js'\nimport {Info} from './util/info.js'\nimport {normalize} from './normalize.js'\n\nconst cap = /[A-Z]/g\nconst dash = /-[a-z]/g\nconst valid = /^data[-\\w.:]+$/i\n\n/**\n * Look up info on a property.\n *\n * In most cases the given `schema` contains info on the property.\n * All standard,\n * most legacy,\n * and some non-standard properties are supported.\n * For these cases,\n * the returned `Info` has hints about the value of the property.\n *\n * `name` can also be a valid data attribute or property,\n * in which case an `Info` object with the correctly cased `attribute` and\n * `property` is returned.\n *\n * `name` can be an unknown attribute,\n * in which case an `Info` object with `attribute` and `property` set to the\n * given name is returned.\n * It is not recommended to provide unsupported legacy or recently specced\n * properties.\n *\n *\n * @param {Schema} schema\n *   Schema;\n *   either the `html` or `svg` export.\n * @param {string} value\n *   An attribute-like or property-like name;\n *   it will be passed through `normalize` to hopefully find the correct info.\n * @returns {Info}\n *   Info.\n */\nexport function find(schema, value) {\n  const normal = normalize(value)\n  let property = value\n  let Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === 'data' && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      // Turn it into a property.\n      const rest = value.slice(5).replace(dash, camelcase)\n      property = 'data' + rest.charAt(0).toUpperCase() + rest.slice(1)\n    } else {\n      // Turn it into an attribute.\n      const rest = value.slice(4)\n\n      if (!dash.test(rest)) {\n        let dashes = rest.replace(cap, kebab)\n\n        if (dashes.charAt(0) !== '-') {\n          dashes = '-' + dashes\n        }\n\n        value = 'data' + dashes\n      }\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(property, value)\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Kebab.\n */\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\n/**\n * @param {string} $0\n *   Value.\n * @returns {string}\n *   Camel.\n */\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAAQA,WAAW,QAAO,wBAAwB;AAClD,SAAQC,IAAI,QAAO,gBAAgB;AACnC,SAAQC,SAAS,QAAO,gBAAgB;AAExC,MAAMC,GAAG,GAAG,QAAQ;AACpB,MAAMC,IAAI,GAAG,SAAS;AACtB,MAAMC,KAAK,GAAG,iBAAiB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAClC,MAAMC,MAAM,GAAGP,SAAS,CAACM,KAAK,CAAC;EAC/B,IAAIE,QAAQ,GAAGF,KAAK;EACpB,IAAIG,IAAI,GAAGV,IAAI;EAEf,IAAIQ,MAAM,IAAIF,MAAM,CAACE,MAAM,EAAE;IAC3B,OAAOF,MAAM,CAACG,QAAQ,CAACH,MAAM,CAACE,MAAM,CAACA,MAAM,CAAC,CAAC;EAC/C;EAEA,IAAIA,MAAM,CAACG,MAAM,GAAG,CAAC,IAAIH,MAAM,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,IAAIR,KAAK,CAACS,IAAI,CAACN,KAAK,CAAC,EAAE;IAC3E;IACA,IAAIA,KAAK,CAACO,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAC3B;MACA,MAAMC,IAAI,GAAGR,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC,CAACI,OAAO,CAACb,IAAI,EAAEc,SAAS,CAAC;MACpDR,QAAQ,GAAG,MAAM,GAAGM,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,GAAGH,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC;IAClE,CAAC,MAAM;MACL;MACA,MAAMG,IAAI,GAAGR,KAAK,CAACK,KAAK,CAAC,CAAC,CAAC;MAE3B,IAAI,CAACT,IAAI,CAACU,IAAI,CAACE,IAAI,CAAC,EAAE;QACpB,IAAII,MAAM,GAAGJ,IAAI,CAACC,OAAO,CAACd,GAAG,EAAEkB,KAAK,CAAC;QAErC,IAAID,MAAM,CAACL,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UAC5BK,MAAM,GAAG,GAAG,GAAGA,MAAM;QACvB;QAEAZ,KAAK,GAAG,MAAM,GAAGY,MAAM;MACzB;IACF;IAEAT,IAAI,GAAGX,WAAW;EACpB;EAEA,OAAO,IAAIW,IAAI,CAACD,QAAQ,EAAEF,KAAK,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,KAAKA,CAACC,EAAE,EAAE;EACjB,OAAO,GAAG,GAAGA,EAAE,CAACC,WAAW,CAAC,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,SAASA,CAACI,EAAE,EAAE;EACrB,OAAOA,EAAE,CAACP,MAAM,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}