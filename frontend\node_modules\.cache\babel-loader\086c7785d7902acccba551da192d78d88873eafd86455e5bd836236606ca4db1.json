{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */\n\nimport structuredClone from '@ungap/structured-clone';\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */\nexport function defaultFootnoteBackContent(_, rereferenceIndex) {\n  /** @type {Array<ElementContent>} */\n  const result = [{\n    type: 'text',\n    value: '↩'\n  }];\n  if (rereferenceIndex > 1) {\n    result.push({\n      type: 'element',\n      tagName: 'sup',\n      properties: {},\n      children: [{\n        type: 'text',\n        value: String(rereferenceIndex)\n      }]\n    });\n  }\n  return result;\n}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */\nexport function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n  return 'Back to reference ' + (referenceIndex + 1) + (rereferenceIndex > 1 ? '-' + rereferenceIndex : '');\n}\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\n// eslint-disable-next-line complexity\nexport function footer(state) {\n  const clobberPrefix = typeof state.options.clobberPrefix === 'string' ? state.options.clobberPrefix : 'user-content-';\n  const footnoteBackContent = state.options.footnoteBackContent || defaultFootnoteBackContent;\n  const footnoteBackLabel = state.options.footnoteBackLabel || defaultFootnoteBackLabel;\n  const footnoteLabel = state.options.footnoteLabel || 'Footnotes';\n  const footnoteLabelTagName = state.options.footnoteLabelTagName || 'h2';\n  const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n    className: ['sr-only']\n  };\n  /** @type {Array<ElementContent>} */\n  const listItems = [];\n  let referenceIndex = -1;\n  while (++referenceIndex < state.footnoteOrder.length) {\n    const definition = state.footnoteById.get(state.footnoteOrder[referenceIndex]);\n    if (!definition) {\n      continue;\n    }\n    const content = state.all(definition);\n    const id = String(definition.identifier).toUpperCase();\n    const safeId = normalizeUri(id.toLowerCase());\n    let rereferenceIndex = 0;\n    /** @type {Array<ElementContent>} */\n    const backReferences = [];\n    const counts = state.footnoteCounts.get(id);\n\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (counts !== undefined && ++rereferenceIndex <= counts) {\n      if (backReferences.length > 0) {\n        backReferences.push({\n          type: 'text',\n          value: ' '\n        });\n      }\n      let children = typeof footnoteBackContent === 'string' ? footnoteBackContent : footnoteBackContent(referenceIndex, rereferenceIndex);\n      if (typeof children === 'string') {\n        children = {\n          type: 'text',\n          value: children\n        };\n      }\n      backReferences.push({\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href: '#' + clobberPrefix + 'fnref-' + safeId + (rereferenceIndex > 1 ? '-' + rereferenceIndex : ''),\n          dataFootnoteBackref: '',\n          ariaLabel: typeof footnoteBackLabel === 'string' ? footnoteBackLabel : footnoteBackLabel(referenceIndex, rereferenceIndex),\n          className: ['data-footnote-backref']\n        },\n        children: Array.isArray(children) ? children : [children]\n      });\n    }\n    const tail = content[content.length - 1];\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1];\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' ';\n      } else {\n        tail.children.push({\n          type: 'text',\n          value: ' '\n        });\n      }\n      tail.children.push(...backReferences);\n    } else {\n      content.push(...backReferences);\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {\n        id: clobberPrefix + 'fn-' + safeId\n      },\n      children: state.wrap(content, true)\n    };\n    state.patch(definition, listItem);\n    listItems.push(listItem);\n  }\n  if (listItems.length === 0) {\n    return;\n  }\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {\n      dataFootnotes: true,\n      className: ['footnotes']\n    },\n    children: [{\n      type: 'element',\n      tagName: footnoteLabelTagName,\n      properties: {\n        ...structuredClone(footnoteLabelProperties),\n        id: 'footnote-label'\n      },\n      children: [{\n        type: 'text',\n        value: footnoteLabel\n      }]\n    }, {\n      type: 'text',\n      value: '\\n'\n    }, {\n      type: 'element',\n      tagName: 'ol',\n      properties: {},\n      children: state.wrap(listItems, true)\n    }, {\n      type: 'text',\n      value: '\\n'\n    }]\n  };\n}", "map": {"version": 3, "names": ["structuredClone", "normalizeUri", "defaultFootnoteBackContent", "_", "rereferenceIndex", "result", "type", "value", "push", "tagName", "properties", "children", "String", "defaultFootnoteBackLabel", "referenceIndex", "footer", "state", "clobberPrefix", "options", "footnoteBackContent", "footnoteBackLabel", "footnote<PERSON>abel", "footnoteLabelTagName", "footnoteLabelProperties", "className", "listItems", "footnoteOrder", "length", "definition", "footnoteById", "get", "content", "all", "id", "identifier", "toUpperCase", "safeId", "toLowerCase", "backReferences", "counts", "footnoteCounts", "undefined", "href", "dataFootnoteBackref", "aria<PERSON><PERSON><PERSON>", "Array", "isArray", "tail", "tailTail", "listItem", "wrap", "patch", "dataFootnotes"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/mdast-util-to-hast/lib/footer.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */\nexport function defaultFootnoteBackContent(_, rereferenceIndex) {\n  /** @type {Array<ElementContent>} */\n  const result = [{type: 'text', value: '↩'}]\n\n  if (rereferenceIndex > 1) {\n    result.push({\n      type: 'element',\n      tagName: 'sup',\n      properties: {},\n      children: [{type: 'text', value: String(rereferenceIndex)}]\n    })\n  }\n\n  return result\n}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */\nexport function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\n// eslint-disable-next-line complexity\nexport function footer(state) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const footnoteBackContent =\n    state.options.footnoteBackContent || defaultFootnoteBackContent\n  const footnoteBackLabel =\n    state.options.footnoteBackLabel || defaultFootnoteBackLabel\n  const footnoteLabel = state.options.footnoteLabel || 'Footnotes'\n  const footnoteLabelTagName = state.options.footnoteLabelTagName || 'h2'\n  const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n    className: ['sr-only']\n  }\n  /** @type {Array<ElementContent>} */\n  const listItems = []\n  let referenceIndex = -1\n\n  while (++referenceIndex < state.footnoteOrder.length) {\n    const definition = state.footnoteById.get(\n      state.footnoteOrder[referenceIndex]\n    )\n\n    if (!definition) {\n      continue\n    }\n\n    const content = state.all(definition)\n    const id = String(definition.identifier).toUpperCase()\n    const safeId = normalizeUri(id.toLowerCase())\n    let rereferenceIndex = 0\n    /** @type {Array<ElementContent>} */\n    const backReferences = []\n    const counts = state.footnoteCounts.get(id)\n\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (counts !== undefined && ++rereferenceIndex <= counts) {\n      if (backReferences.length > 0) {\n        backReferences.push({type: 'text', value: ' '})\n      }\n\n      let children =\n        typeof footnoteBackContent === 'string'\n          ? footnoteBackContent\n          : footnoteBackContent(referenceIndex, rereferenceIndex)\n\n      if (typeof children === 'string') {\n        children = {type: 'text', value: children}\n      }\n\n      backReferences.push({\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href:\n            '#' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (rereferenceIndex > 1 ? '-' + rereferenceIndex : ''),\n          dataFootnoteBackref: '',\n          ariaLabel:\n            typeof footnoteBackLabel === 'string'\n              ? footnoteBackLabel\n              : footnoteBackLabel(referenceIndex, rereferenceIndex),\n          className: ['data-footnote-backref']\n        },\n        children: Array.isArray(children) ? children : [children]\n      })\n    }\n\n    const tail = content[content.length - 1]\n\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1]\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' '\n      } else {\n        tail.children.push({type: 'text', value: ' '})\n      }\n\n      tail.children.push(...backReferences)\n    } else {\n      content.push(...backReferences)\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {id: clobberPrefix + 'fn-' + safeId},\n      children: state.wrap(content, true)\n    }\n\n    state.patch(definition, listItem)\n\n    listItems.push(listItem)\n  }\n\n  if (listItems.length === 0) {\n    return\n  }\n\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {dataFootnotes: true, className: ['footnotes']},\n    children: [\n      {\n        type: 'element',\n        tagName: footnoteLabelTagName,\n        properties: {\n          ...structuredClone(footnoteLabelProperties),\n          id: 'footnote-label'\n        },\n        children: [{type: 'text', value: footnoteLabel}]\n      },\n      {type: 'text', value: '\\n'},\n      {\n        type: 'element',\n        tagName: 'ol',\n        properties: {},\n        children: state.wrap(listItems, true)\n      },\n      {type: 'text', value: '\\n'}\n    ]\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,eAAe,MAAM,yBAAyB;AACrD,SAAQC,YAAY,QAAO,6BAA6B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,0BAA0BA,CAACC,CAAC,EAAEC,gBAAgB,EAAE;EAC9D;EACA,MAAMC,MAAM,GAAG,CAAC;IAACC,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAG,CAAC,CAAC;EAE3C,IAAIH,gBAAgB,GAAG,CAAC,EAAE;IACxBC,MAAM,CAACG,IAAI,CAAC;MACVF,IAAI,EAAE,SAAS;MACfG,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAE,CAAC;QAACL,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAEK,MAAM,CAACR,gBAAgB;MAAC,CAAC;IAC5D,CAAC,CAAC;EACJ;EAEA,OAAOC,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,wBAAwBA,CAACC,cAAc,EAAEV,gBAAgB,EAAE;EACzE,OACE,oBAAoB,IACnBU,cAAc,GAAG,CAAC,CAAC,IACnBV,gBAAgB,GAAG,CAAC,GAAG,GAAG,GAAGA,gBAAgB,GAAG,EAAE,CAAC;AAExD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASW,MAAMA,CAACC,KAAK,EAAE;EAC5B,MAAMC,aAAa,GACjB,OAAOD,KAAK,CAACE,OAAO,CAACD,aAAa,KAAK,QAAQ,GAC3CD,KAAK,CAACE,OAAO,CAACD,aAAa,GAC3B,eAAe;EACrB,MAAME,mBAAmB,GACvBH,KAAK,CAACE,OAAO,CAACC,mBAAmB,IAAIjB,0BAA0B;EACjE,MAAMkB,iBAAiB,GACrBJ,KAAK,CAACE,OAAO,CAACE,iBAAiB,IAAIP,wBAAwB;EAC7D,MAAMQ,aAAa,GAAGL,KAAK,CAACE,OAAO,CAACG,aAAa,IAAI,WAAW;EAChE,MAAMC,oBAAoB,GAAGN,KAAK,CAACE,OAAO,CAACI,oBAAoB,IAAI,IAAI;EACvE,MAAMC,uBAAuB,GAAGP,KAAK,CAACE,OAAO,CAACK,uBAAuB,IAAI;IACvEC,SAAS,EAAE,CAAC,SAAS;EACvB,CAAC;EACD;EACA,MAAMC,SAAS,GAAG,EAAE;EACpB,IAAIX,cAAc,GAAG,CAAC,CAAC;EAEvB,OAAO,EAAEA,cAAc,GAAGE,KAAK,CAACU,aAAa,CAACC,MAAM,EAAE;IACpD,MAAMC,UAAU,GAAGZ,KAAK,CAACa,YAAY,CAACC,GAAG,CACvCd,KAAK,CAACU,aAAa,CAACZ,cAAc,CACpC,CAAC;IAED,IAAI,CAACc,UAAU,EAAE;MACf;IACF;IAEA,MAAMG,OAAO,GAAGf,KAAK,CAACgB,GAAG,CAACJ,UAAU,CAAC;IACrC,MAAMK,EAAE,GAAGrB,MAAM,CAACgB,UAAU,CAACM,UAAU,CAAC,CAACC,WAAW,CAAC,CAAC;IACtD,MAAMC,MAAM,GAAGnC,YAAY,CAACgC,EAAE,CAACI,WAAW,CAAC,CAAC,CAAC;IAC7C,IAAIjC,gBAAgB,GAAG,CAAC;IACxB;IACA,MAAMkC,cAAc,GAAG,EAAE;IACzB,MAAMC,MAAM,GAAGvB,KAAK,CAACwB,cAAc,CAACV,GAAG,CAACG,EAAE,CAAC;;IAE3C;IACA,OAAOM,MAAM,KAAKE,SAAS,IAAI,EAAErC,gBAAgB,IAAImC,MAAM,EAAE;MAC3D,IAAID,cAAc,CAACX,MAAM,GAAG,CAAC,EAAE;QAC7BW,cAAc,CAAC9B,IAAI,CAAC;UAACF,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAG,CAAC,CAAC;MACjD;MAEA,IAAII,QAAQ,GACV,OAAOQ,mBAAmB,KAAK,QAAQ,GACnCA,mBAAmB,GACnBA,mBAAmB,CAACL,cAAc,EAAEV,gBAAgB,CAAC;MAE3D,IAAI,OAAOO,QAAQ,KAAK,QAAQ,EAAE;QAChCA,QAAQ,GAAG;UAACL,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAEI;QAAQ,CAAC;MAC5C;MAEA2B,cAAc,CAAC9B,IAAI,CAAC;QAClBF,IAAI,EAAE,SAAS;QACfG,OAAO,EAAE,GAAG;QACZC,UAAU,EAAE;UACVgC,IAAI,EACF,GAAG,GACHzB,aAAa,GACb,QAAQ,GACRmB,MAAM,IACLhC,gBAAgB,GAAG,CAAC,GAAG,GAAG,GAAGA,gBAAgB,GAAG,EAAE,CAAC;UACtDuC,mBAAmB,EAAE,EAAE;UACvBC,SAAS,EACP,OAAOxB,iBAAiB,KAAK,QAAQ,GACjCA,iBAAiB,GACjBA,iBAAiB,CAACN,cAAc,EAAEV,gBAAgB,CAAC;UACzDoB,SAAS,EAAE,CAAC,uBAAuB;QACrC,CAAC;QACDb,QAAQ,EAAEkC,KAAK,CAACC,OAAO,CAACnC,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ;MAC1D,CAAC,CAAC;IACJ;IAEA,MAAMoC,IAAI,GAAGhB,OAAO,CAACA,OAAO,CAACJ,MAAM,GAAG,CAAC,CAAC;IAExC,IAAIoB,IAAI,IAAIA,IAAI,CAACzC,IAAI,KAAK,SAAS,IAAIyC,IAAI,CAACtC,OAAO,KAAK,GAAG,EAAE;MAC3D,MAAMuC,QAAQ,GAAGD,IAAI,CAACpC,QAAQ,CAACoC,IAAI,CAACpC,QAAQ,CAACgB,MAAM,GAAG,CAAC,CAAC;MACxD,IAAIqB,QAAQ,IAAIA,QAAQ,CAAC1C,IAAI,KAAK,MAAM,EAAE;QACxC0C,QAAQ,CAACzC,KAAK,IAAI,GAAG;MACvB,CAAC,MAAM;QACLwC,IAAI,CAACpC,QAAQ,CAACH,IAAI,CAAC;UAACF,IAAI,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAG,CAAC,CAAC;MAChD;MAEAwC,IAAI,CAACpC,QAAQ,CAACH,IAAI,CAAC,GAAG8B,cAAc,CAAC;IACvC,CAAC,MAAM;MACLP,OAAO,CAACvB,IAAI,CAAC,GAAG8B,cAAc,CAAC;IACjC;;IAEA;IACA,MAAMW,QAAQ,GAAG;MACf3C,IAAI,EAAE,SAAS;MACfG,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE;QAACuB,EAAE,EAAEhB,aAAa,GAAG,KAAK,GAAGmB;MAAM,CAAC;MAChDzB,QAAQ,EAAEK,KAAK,CAACkC,IAAI,CAACnB,OAAO,EAAE,IAAI;IACpC,CAAC;IAEDf,KAAK,CAACmC,KAAK,CAACvB,UAAU,EAAEqB,QAAQ,CAAC;IAEjCxB,SAAS,CAACjB,IAAI,CAACyC,QAAQ,CAAC;EAC1B;EAEA,IAAIxB,SAAS,CAACE,MAAM,KAAK,CAAC,EAAE;IAC1B;EACF;EAEA,OAAO;IACLrB,IAAI,EAAE,SAAS;IACfG,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE;MAAC0C,aAAa,EAAE,IAAI;MAAE5B,SAAS,EAAE,CAAC,WAAW;IAAC,CAAC;IAC3Db,QAAQ,EAAE,CACR;MACEL,IAAI,EAAE,SAAS;MACfG,OAAO,EAAEa,oBAAoB;MAC7BZ,UAAU,EAAE;QACV,GAAGV,eAAe,CAACuB,uBAAuB,CAAC;QAC3CU,EAAE,EAAE;MACN,CAAC;MACDtB,QAAQ,EAAE,CAAC;QAACL,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAEc;MAAa,CAAC;IACjD,CAAC,EACD;MAACf,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAI,CAAC,EAC3B;MACED,IAAI,EAAE,SAAS;MACfG,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,CAAC,CAAC;MACdC,QAAQ,EAAEK,KAAK,CAACkC,IAAI,CAACzB,SAAS,EAAE,IAAI;IACtC,CAAC,EACD;MAACnB,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAI,CAAC;EAE/B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}