{"ast": null, "code": "/**\n * @import {Info as InfoType} from 'property-information'\n */\n\n/** @type {InfoType} */\nexport class Info {\n  /**\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute) {\n    this.attribute = attribute;\n    this.property = property;\n  }\n}\nInfo.prototype.attribute = '';\nInfo.prototype.booleanish = false;\nInfo.prototype.boolean = false;\nInfo.prototype.commaOrSpaceSeparated = false;\nInfo.prototype.commaSeparated = false;\nInfo.prototype.defined = false;\nInfo.prototype.mustUseProperty = false;\nInfo.prototype.number = false;\nInfo.prototype.overloadedBoolean = false;\nInfo.prototype.property = '';\nInfo.prototype.spaceSeparated = false;\nInfo.prototype.space = undefined;", "map": {"version": 3, "names": ["Info", "constructor", "property", "attribute", "prototype", "booleanish", "boolean", "commaOrSpaceSeparated", "commaSeparated", "defined", "mustUseProperty", "number", "overloadedBoolean", "spaceSeparated", "space", "undefined"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/property-information/lib/util/info.js"], "sourcesContent": ["/**\n * @import {Info as InfoType} from 'property-information'\n */\n\n/** @type {InfoType} */\nexport class Info {\n  /**\n   * @param {string} property\n   *   Property.\n   * @param {string} attribute\n   *   Attribute.\n   * @returns\n   *   Info.\n   */\n  constructor(property, attribute) {\n    this.attribute = attribute\n    this.property = property\n  }\n}\n\nInfo.prototype.attribute = ''\nInfo.prototype.booleanish = false\nInfo.prototype.boolean = false\nInfo.prototype.commaOrSpaceSeparated = false\nInfo.prototype.commaSeparated = false\nInfo.prototype.defined = false\nInfo.prototype.mustUseProperty = false\nInfo.prototype.number = false\nInfo.prototype.overloadedBoolean = false\nInfo.prototype.property = ''\nInfo.prototype.spaceSeparated = false\nInfo.prototype.space = undefined\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,IAAI,CAAC;EAChB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,WAAWA,CAACC,QAAQ,EAAEC,SAAS,EAAE;IAC/B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAEAF,IAAI,CAACI,SAAS,CAACD,SAAS,GAAG,EAAE;AAC7BH,IAAI,CAACI,SAAS,CAACC,UAAU,GAAG,KAAK;AACjCL,IAAI,CAACI,SAAS,CAACE,OAAO,GAAG,KAAK;AAC9BN,IAAI,CAACI,SAAS,CAACG,qBAAqB,GAAG,KAAK;AAC5CP,IAAI,CAACI,SAAS,CAACI,cAAc,GAAG,KAAK;AACrCR,IAAI,CAACI,SAAS,CAACK,OAAO,GAAG,KAAK;AAC9BT,IAAI,CAACI,SAAS,CAACM,eAAe,GAAG,KAAK;AACtCV,IAAI,CAACI,SAAS,CAACO,MAAM,GAAG,KAAK;AAC7BX,IAAI,CAACI,SAAS,CAACQ,iBAAiB,GAAG,KAAK;AACxCZ,IAAI,CAACI,SAAS,CAACF,QAAQ,GAAG,EAAE;AAC5BF,IAAI,CAACI,SAAS,CAACS,cAAc,GAAG,KAAK;AACrCb,IAAI,CAACI,SAAS,CAACU,KAAK,GAAGC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}