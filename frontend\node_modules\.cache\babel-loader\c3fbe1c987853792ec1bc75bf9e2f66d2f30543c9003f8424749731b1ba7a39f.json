{"ast": null, "code": "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Point | PointLike | Position | PositionLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nexport function stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return '';\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position);\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value);\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value);\n  }\n\n  // ?\n  return '';\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column);\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end);\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1;\n}", "map": {"version": 3, "names": ["stringifyPosition", "value", "position", "point", "index", "line", "column", "pos", "start", "end"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/unist-util-stringify-position/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Point | PointLike | Position | PositionLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nexport function stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return ''\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position)\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value)\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value)\n  }\n\n  // ?\n  return ''\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column)\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end)\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,iBAAiBA,CAACC,KAAK,EAAE;EACvC;EACA,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAO,EAAE;EACX;;EAEA;EACA,IAAI,UAAU,IAAIA,KAAK,IAAI,MAAM,IAAIA,KAAK,EAAE;IAC1C,OAAOC,QAAQ,CAACD,KAAK,CAACC,QAAQ,CAAC;EACjC;;EAEA;EACA,IAAI,OAAO,IAAID,KAAK,IAAI,KAAK,IAAIA,KAAK,EAAE;IACtC,OAAOC,QAAQ,CAACD,KAAK,CAAC;EACxB;;EAEA;EACA,IAAI,MAAM,IAAIA,KAAK,IAAI,QAAQ,IAAIA,KAAK,EAAE;IACxC,OAAOE,KAAK,CAACF,KAAK,CAAC;EACrB;;EAEA;EACA,OAAO,EAAE;AACX;;AAEA;AACA;AACA;AACA;AACA,SAASE,KAAKA,CAACA,KAAK,EAAE;EACpB,OAAOC,KAAK,CAACD,KAAK,IAAIA,KAAK,CAACE,IAAI,CAAC,GAAG,GAAG,GAAGD,KAAK,CAACD,KAAK,IAAIA,KAAK,CAACG,MAAM,CAAC;AACxE;;AAEA;AACA;AACA;AACA;AACA,SAASJ,QAAQA,CAACK,GAAG,EAAE;EACrB,OAAOJ,KAAK,CAACI,GAAG,IAAIA,GAAG,CAACC,KAAK,CAAC,GAAG,GAAG,GAAGL,KAAK,CAACI,GAAG,IAAIA,GAAG,CAACE,GAAG,CAAC;AAC9D;;AAEA;AACA;AACA;AACA;AACA,SAASL,KAAKA,CAACH,KAAK,EAAE;EACpB,OAAOA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}