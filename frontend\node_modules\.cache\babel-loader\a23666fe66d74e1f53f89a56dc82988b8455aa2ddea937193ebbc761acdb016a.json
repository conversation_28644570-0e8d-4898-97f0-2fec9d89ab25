{"ast": null, "code": "/**\n * @import {Identifier, Literal, MemberExpression} from 'estree'\n * @import {Jsx, JsxDev, Op<PERSON>, Props} from 'hast-util-to-jsx-runtime'\n * @import {Element, Nodes, Parents, Root, Text} from 'hast'\n * @import {MdxFlowExpressionHast, MdxTextExpressionHast} from 'mdast-util-mdx-expression'\n * @import {MdxJsxFlowElementHast, MdxJsxTextElementHast} from 'mdast-util-mdx-jsx'\n * @import {MdxjsEsmHast} from 'mdast-util-mdxjs-esm'\n * @import {Position} from 'unist'\n * @import {Child, Create, Field, JsxElement, State, Style} from './types.js'\n */\n\nimport { stringify as commas } from 'comma-separated-tokens';\nimport { ok as assert } from 'devlop';\nimport { name as isIdentifierName } from 'estree-util-is-identifier-name';\nimport { whitespace } from 'hast-util-whitespace';\nimport { find, hastToReact, html, svg } from 'property-information';\nimport { stringify as spaces } from 'space-separated-tokens';\nimport styleToJs from 'style-to-js';\nimport { pointStart } from 'unist-util-position';\nimport { VFileMessage } from 'vfile-message';\n\n// To do: next major: `Object.hasOwn`.\nconst own = {}.hasOwnProperty;\n\n/** @type {Map<string, number>} */\nconst emptyMap = new Map();\nconst cap = /[A-Z]/g;\n\n// `react-dom` triggers a warning for *any* white space in tables.\n// To follow GFM, `mdast-util-to-hast` injects line endings between elements.\n// Other tools might do so too, but they don’t do here, so we remove all of\n// that.\n\n// See: <https://github.com/facebook/react/pull/7081>.\n// See: <https://github.com/facebook/react/pull/7515>.\n// See: <https://github.com/remarkjs/remark-react/issues/64>.\n// See: <https://github.com/rehypejs/rehype-react/pull/29>.\n// See: <https://github.com/rehypejs/rehype-react/pull/32>.\n// See: <https://github.com/rehypejs/rehype-react/pull/45>.\nconst tableElements = new Set(['table', 'tbody', 'thead', 'tfoot', 'tr']);\nconst tableCellElement = new Set(['td', 'th']);\nconst docs = 'https://github.com/syntax-tree/hast-util-to-jsx-runtime';\n\n/**\n * Transform a hast tree to preact, react, solid, svelte, vue, etc.,\n * with an automatic JSX runtime.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options} options\n *   Configuration (required).\n * @returns {JsxElement}\n *   JSX element.\n */\n\nexport function toJsxRuntime(tree, options) {\n  if (!options || options.Fragment === undefined) {\n    throw new TypeError('Expected `Fragment` in options');\n  }\n  const filePath = options.filePath || undefined;\n  /** @type {Create} */\n  let create;\n  if (options.development) {\n    if (typeof options.jsxDEV !== 'function') {\n      throw new TypeError('Expected `jsxDEV` in options when `development: true`');\n    }\n    create = developmentCreate(filePath, options.jsxDEV);\n  } else {\n    if (typeof options.jsx !== 'function') {\n      throw new TypeError('Expected `jsx` in production options');\n    }\n    if (typeof options.jsxs !== 'function') {\n      throw new TypeError('Expected `jsxs` in production options');\n    }\n    create = productionCreate(filePath, options.jsx, options.jsxs);\n  }\n\n  /** @type {State} */\n  const state = {\n    Fragment: options.Fragment,\n    ancestors: [],\n    components: options.components || {},\n    create,\n    elementAttributeNameCase: options.elementAttributeNameCase || 'react',\n    evaluater: options.createEvaluater ? options.createEvaluater() : undefined,\n    filePath,\n    ignoreInvalidStyle: options.ignoreInvalidStyle || false,\n    passKeys: options.passKeys !== false,\n    passNode: options.passNode || false,\n    schema: options.space === 'svg' ? svg : html,\n    stylePropertyNameCase: options.stylePropertyNameCase || 'dom',\n    tableCellAlignToStyle: options.tableCellAlignToStyle !== false\n  };\n  const result = one(state, tree, undefined);\n\n  // JSX element.\n  if (result && typeof result !== 'string') {\n    return result;\n  }\n\n  // Text node or something that turned into nothing.\n  return state.create(tree, state.Fragment, {\n    children: result || undefined\n  }, undefined);\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction one(state, node, key) {\n  if (node.type === 'element') {\n    return element(state, node, key);\n  }\n  if (node.type === 'mdxFlowExpression' || node.type === 'mdxTextExpression') {\n    return mdxExpression(state, node);\n  }\n  if (node.type === 'mdxJsxFlowElement' || node.type === 'mdxJsxTextElement') {\n    return mdxJsxElement(state, node, key);\n  }\n  if (node.type === 'mdxjsEsm') {\n    return mdxEsm(state, node);\n  }\n  if (node.type === 'root') {\n    return root(state, node, key);\n  }\n  if (node.type === 'text') {\n    return text(state, node);\n  }\n}\n\n/**\n * Handle element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction element(state, node, key) {\n  const parentSchema = state.schema;\n  let schema = parentSchema;\n  if (node.tagName.toLowerCase() === 'svg' && parentSchema.space === 'html') {\n    schema = svg;\n    state.schema = schema;\n  }\n  state.ancestors.push(node);\n  const type = findComponentFromName(state, node.tagName, false);\n  const props = createElementProps(state, node);\n  let children = createChildren(state, node);\n  if (tableElements.has(node.tagName)) {\n    children = children.filter(function (child) {\n      return typeof child === 'string' ? !whitespace(child) : true;\n    });\n  }\n  addNode(state, props, type, node);\n  addChildren(props, children);\n\n  // Restore.\n  state.ancestors.pop();\n  state.schema = parentSchema;\n  return state.create(node, type, props, key);\n}\n\n/**\n * Handle MDX expression.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxFlowExpressionHast | MdxTextExpressionHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxExpression(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    const program = node.data.estree;\n    const expression = program.body[0];\n    assert(expression.type === 'ExpressionStatement');\n\n    // Assume result is a child.\n    return /** @type {Child | undefined} */state.evaluater.evaluateExpression(expression.expression);\n  }\n  crashEstree(state, node.position);\n}\n\n/**\n * Handle MDX ESM.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxjsEsmHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxEsm(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    // Assume result is a child.\n    return /** @type {Child | undefined} */state.evaluater.evaluateProgram(node.data.estree);\n  }\n  crashEstree(state, node.position);\n}\n\n/**\n * Handle MDX JSX.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxJsxElement(state, node, key) {\n  const parentSchema = state.schema;\n  let schema = parentSchema;\n  if (node.name === 'svg' && parentSchema.space === 'html') {\n    schema = svg;\n    state.schema = schema;\n  }\n  state.ancestors.push(node);\n  const type = node.name === null ? state.Fragment : findComponentFromName(state, node.name, true);\n  const props = createJsxElementProps(state, node);\n  const children = createChildren(state, node);\n  addNode(state, props, type, node);\n  addChildren(props, children);\n\n  // Restore.\n  state.ancestors.pop();\n  state.schema = parentSchema;\n  return state.create(node, type, props, key);\n}\n\n/**\n * Handle root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Root} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction root(state, node, key) {\n  /** @type {Props} */\n  const props = {};\n  addChildren(props, createChildren(state, node));\n  return state.create(node, state.Fragment, props, key);\n}\n\n/**\n * Handle text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Text} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction text(_, node) {\n  return node.value;\n}\n\n/**\n * Add `node` to props.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Props} props\n *   Props.\n * @param {unknown} type\n *   Type.\n * @param {Element | MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addNode(state, props, type, node) {\n  // If this is swapped out for a component:\n  if (typeof type !== 'string' && type !== state.Fragment && state.passNode) {\n    props.node = node;\n  }\n}\n\n/**\n * Add children to props.\n *\n * @param {Props} props\n *   Props.\n * @param {Array<Child>} children\n *   Children.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChildren(props, children) {\n  if (children.length > 0) {\n    const value = children.length > 1 ? children : children[0];\n    if (value) {\n      props.children = value;\n    }\n  }\n}\n\n/**\n * @param {string | undefined} _\n *   Path to file.\n * @param {Jsx} jsx\n *   Dynamic.\n * @param {Jsx} jsxs\n *   Static.\n * @returns {Create}\n *   Create a production element.\n */\nfunction productionCreate(_, jsx, jsxs) {\n  return create;\n  /** @type {Create} */\n  function create(_, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children);\n    const fn = isStaticChildren ? jsxs : jsx;\n    return key ? fn(type, props, key) : fn(type, props);\n  }\n}\n\n/**\n * @param {string | undefined} filePath\n *   Path to file.\n * @param {JsxDev} jsxDEV\n *   Development.\n * @returns {Create}\n *   Create a development element.\n */\nfunction developmentCreate(filePath, jsxDEV) {\n  return create;\n  /** @type {Create} */\n  function create(node, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children);\n    const point = pointStart(node);\n    return jsxDEV(type, props, key, isStaticChildren, {\n      columnNumber: point ? point.column - 1 : undefined,\n      fileName: filePath,\n      lineNumber: point ? point.line : undefined\n    }, undefined);\n  }\n}\n\n/**\n * Create props from an element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current element.\n * @returns {Props}\n *   Props.\n */\nfunction createElementProps(state, node) {\n  /** @type {Props} */\n  const props = {};\n  /** @type {string | undefined} */\n  let alignValue;\n  /** @type {string} */\n  let prop;\n  for (prop in node.properties) {\n    if (prop !== 'children' && own.call(node.properties, prop)) {\n      const result = createProperty(state, prop, node.properties[prop]);\n      if (result) {\n        const [key, value] = result;\n        if (state.tableCellAlignToStyle && key === 'align' && typeof value === 'string' && tableCellElement.has(node.tagName)) {\n          alignValue = value;\n        } else {\n          props[key] = value;\n        }\n      }\n    }\n  }\n  if (alignValue) {\n    // Assume style is an object.\n    const style = /** @type {Style} */props.style || (props.style = {});\n    style[state.stylePropertyNameCase === 'css' ? 'text-align' : 'textAlign'] = alignValue;\n  }\n  return props;\n}\n\n/**\n * Create props from a JSX element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current JSX element.\n * @returns {Props}\n *   Props.\n */\nfunction createJsxElementProps(state, node) {\n  /** @type {Props} */\n  const props = {};\n  for (const attribute of node.attributes) {\n    if (attribute.type === 'mdxJsxExpressionAttribute') {\n      if (attribute.data && attribute.data.estree && state.evaluater) {\n        const program = attribute.data.estree;\n        const expression = program.body[0];\n        assert(expression.type === 'ExpressionStatement');\n        const objectExpression = expression.expression;\n        assert(objectExpression.type === 'ObjectExpression');\n        const property = objectExpression.properties[0];\n        assert(property.type === 'SpreadElement');\n        Object.assign(props, state.evaluater.evaluateExpression(property.argument));\n      } else {\n        crashEstree(state, node.position);\n      }\n    } else {\n      // For JSX, the author is responsible of passing in the correct values.\n      const name = attribute.name;\n      /** @type {unknown} */\n      let value;\n      if (attribute.value && typeof attribute.value === 'object') {\n        if (attribute.value.data && attribute.value.data.estree && state.evaluater) {\n          const program = attribute.value.data.estree;\n          const expression = program.body[0];\n          assert(expression.type === 'ExpressionStatement');\n          value = state.evaluater.evaluateExpression(expression.expression);\n        } else {\n          crashEstree(state, node.position);\n        }\n      } else {\n        value = attribute.value === null ? true : attribute.value;\n      }\n\n      // Assume a prop.\n      props[name] = /** @type {Props[keyof Props]} */value;\n    }\n  }\n  return props;\n}\n\n/**\n * Create children.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Parents} node\n *   Current element.\n * @returns {Array<Child>}\n *   Children.\n */\nfunction createChildren(state, node) {\n  /** @type {Array<Child>} */\n  const children = [];\n  let index = -1;\n  /** @type {Map<string, number>} */\n  // Note: test this when Solid doesn’t want to merge my upcoming PR.\n  /* c8 ignore next */\n  const countsByName = state.passKeys ? new Map() : emptyMap;\n  while (++index < node.children.length) {\n    const child = node.children[index];\n    /** @type {string | undefined} */\n    let key;\n    if (state.passKeys) {\n      const name = child.type === 'element' ? child.tagName : child.type === 'mdxJsxFlowElement' || child.type === 'mdxJsxTextElement' ? child.name : undefined;\n      if (name) {\n        const count = countsByName.get(name) || 0;\n        key = name + '-' + count;\n        countsByName.set(name, count + 1);\n      }\n    }\n    const result = one(state, child, key);\n    if (result !== undefined) children.push(result);\n  }\n  return children;\n}\n\n/**\n * Handle a property.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Field | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(state, prop, value) {\n  const info = find(state.schema, prop);\n\n  // Ignore nullish and `NaN` values.\n  if (value === null || value === undefined || typeof value === 'number' && Number.isNaN(value)) {\n    return;\n  }\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? commas(value) : spaces(value);\n  }\n\n  // React only accepts `style` as object.\n  if (info.property === 'style') {\n    let styleObject = typeof value === 'object' ? value : parseStyle(state, String(value));\n    if (state.stylePropertyNameCase === 'css') {\n      styleObject = transformStylesToCssCasing(styleObject);\n    }\n    return ['style', styleObject];\n  }\n  return [state.elementAttributeNameCase === 'react' && info.space ? hastToReact[info.property] || info.property : info.attribute, value];\n}\n\n/**\n * Parse a CSS declaration to an object.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} value\n *   CSS declarations.\n * @returns {Style}\n *   Properties.\n * @throws\n *   Throws `VFileMessage` when CSS cannot be parsed.\n */\nfunction parseStyle(state, value) {\n  try {\n    return styleToJs(value, {\n      reactCompat: true\n    });\n  } catch (error) {\n    if (state.ignoreInvalidStyle) {\n      return {};\n    }\n    const cause = /** @type {Error} */error;\n    const message = new VFileMessage('Cannot parse `style` attribute', {\n      ancestors: state.ancestors,\n      cause,\n      ruleId: 'style',\n      source: 'hast-util-to-jsx-runtime'\n    });\n    message.file = state.filePath || undefined;\n    message.url = docs + '#cannot-parse-style-attribute';\n    throw message;\n  }\n}\n\n/**\n * Create a JSX name from a string.\n *\n * @param {State} state\n *   To do.\n * @param {string} name\n *   Name.\n * @param {boolean} allowExpression\n *   Allow member expressions and identifiers.\n * @returns {unknown}\n *   To do.\n */\nfunction findComponentFromName(state, name, allowExpression) {\n  /** @type {Identifier | Literal | MemberExpression} */\n  let result;\n  if (!allowExpression) {\n    result = {\n      type: 'Literal',\n      value: name\n    };\n  } else if (name.includes('.')) {\n    const identifiers = name.split('.');\n    let index = -1;\n    /** @type {Identifier | Literal | MemberExpression | undefined} */\n    let node;\n    while (++index < identifiers.length) {\n      /** @type {Identifier | Literal} */\n      const prop = isIdentifierName(identifiers[index]) ? {\n        type: 'Identifier',\n        name: identifiers[index]\n      } : {\n        type: 'Literal',\n        value: identifiers[index]\n      };\n      node = node ? {\n        type: 'MemberExpression',\n        object: node,\n        property: prop,\n        computed: Boolean(index && prop.type === 'Literal'),\n        optional: false\n      } : prop;\n    }\n    assert(node, 'always a result');\n    result = node;\n  } else {\n    result = isIdentifierName(name) && !/^[a-z]/.test(name) ? {\n      type: 'Identifier',\n      name\n    } : {\n      type: 'Literal',\n      value: name\n    };\n  }\n\n  // Only literals can be passed in `components` currently.\n  // No identifiers / member expressions.\n  if (result.type === 'Literal') {\n    const name = /** @type {string | number} */result.value;\n    return own.call(state.components, name) ? state.components[name] : name;\n  }\n\n  // Assume component.\n  if (state.evaluater) {\n    return state.evaluater.evaluateExpression(result);\n  }\n  crashEstree(state);\n}\n\n/**\n * @param {State} state\n * @param {Position | undefined} [place]\n * @returns {never}\n */\nfunction crashEstree(state, place) {\n  const message = new VFileMessage('Cannot handle MDX estrees without `createEvaluater`', {\n    ancestors: state.ancestors,\n    place,\n    ruleId: 'mdx-estree',\n    source: 'hast-util-to-jsx-runtime'\n  });\n  message.file = state.filePath || undefined;\n  message.url = docs + '#cannot-handle-mdx-estrees-without-createevaluater';\n  throw message;\n}\n\n/**\n * Transform a DOM casing style object to a CSS casing style object.\n *\n * @param {Style} domCasing\n * @returns {Style}\n */\nfunction transformStylesToCssCasing(domCasing) {\n  /** @type {Style} */\n  const cssCasing = {};\n  /** @type {string} */\n  let from;\n  for (from in domCasing) {\n    if (own.call(domCasing, from)) {\n      cssCasing[transformStyleToCssCasing(from)] = domCasing[from];\n    }\n  }\n  return cssCasing;\n}\n\n/**\n * Transform a DOM casing style field to a CSS casing style field.\n *\n * @param {string} from\n * @returns {string}\n */\nfunction transformStyleToCssCasing(from) {\n  let to = from.replace(cap, toDash);\n  // Handle `ms-xxx` -> `-ms-xxx`.\n  if (to.slice(0, 3) === 'ms-') to = '-' + to;\n  return to;\n}\n\n/**\n * Make `$0` dash cased.\n *\n * @param {string} $0\n *   Capitalized ASCII leter.\n * @returns {string}\n *   Dash and lower letter.\n */\nfunction toDash($0) {\n  return '-' + $0.toLowerCase();\n}", "map": {"version": 3, "names": ["stringify", "commas", "ok", "assert", "name", "isIdentifierName", "whitespace", "find", "hastToReact", "html", "svg", "spaces", "styleToJs", "pointStart", "VFileMessage", "own", "hasOwnProperty", "emptyMap", "Map", "cap", "tableElements", "Set", "tableCellElement", "docs", "toJsxRuntime", "tree", "options", "Fragment", "undefined", "TypeError", "filePath", "create", "development", "jsxDEV", "developmentCreate", "jsx", "jsxs", "productionCreate", "state", "ancestors", "components", "elementAttributeNameCase", "evaluater", "createEvaluater", "ignoreInvalidStyle", "pass<PERSON><PERSON><PERSON>", "passNode", "schema", "space", "stylePropertyNameCase", "tableCellAlignToStyle", "result", "one", "children", "node", "key", "type", "element", "mdxExpression", "mdxJsxElement", "mdxEsm", "root", "text", "parentSchema", "tagName", "toLowerCase", "push", "findComponentFromName", "props", "createElementProps", "createChildren", "has", "filter", "child", "addNode", "add<PERSON><PERSON><PERSON><PERSON>", "pop", "data", "estree", "program", "expression", "body", "evaluateExpression", "crashEstree", "position", "evaluateProgram", "createJsxElementProps", "_", "value", "length", "isStaticChildren", "Array", "isArray", "fn", "point", "columnNumber", "column", "fileName", "lineNumber", "line", "alignValue", "prop", "properties", "call", "createProperty", "style", "attribute", "attributes", "objectExpression", "property", "Object", "assign", "argument", "index", "countsByName", "count", "get", "set", "info", "Number", "isNaN", "commaSeparated", "styleObject", "parseStyle", "String", "transformStylesToCssCasing", "reactCompat", "error", "cause", "message", "ruleId", "source", "file", "url", "allowExpression", "includes", "identifiers", "split", "object", "computed", "Boolean", "optional", "test", "place", "domCasing", "cssCasing", "from", "transformStyleToCssCasing", "to", "replace", "to<PERSON>ash", "slice", "$0"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/hast-util-to-jsx-runtime/lib/index.js"], "sourcesContent": ["/**\n * @import {Identifier, Literal, MemberExpression} from 'estree'\n * @import {Jsx, JsxDev, <PERSON><PERSON>, Props} from 'hast-util-to-jsx-runtime'\n * @import {Element, Nodes, Parents, Root, Text} from 'hast'\n * @import {MdxFlowExpressionHast, MdxTextExpressionHast} from 'mdast-util-mdx-expression'\n * @import {MdxJsxFlowElementHast, MdxJsxTextElementHast} from 'mdast-util-mdx-jsx'\n * @import {MdxjsEsmHast} from 'mdast-util-mdxjs-esm'\n * @import {Position} from 'unist'\n * @import {Child, Create, Field, JsxElement, State, Style} from './types.js'\n */\n\nimport {stringify as commas} from 'comma-separated-tokens'\nimport {ok as assert} from 'devlop'\nimport {name as isIdentifierName} from 'estree-util-is-identifier-name'\nimport {whitespace} from 'hast-util-whitespace'\nimport {find, hastToReact, html, svg} from 'property-information'\nimport {stringify as spaces} from 'space-separated-tokens'\nimport styleToJs from 'style-to-js'\nimport {pointStart} from 'unist-util-position'\nimport {VFileMessage} from 'vfile-message'\n\n// To do: next major: `Object.hasOwn`.\nconst own = {}.hasOwnProperty\n\n/** @type {Map<string, number>} */\nconst emptyMap = new Map()\n\nconst cap = /[A-Z]/g\n\n// `react-dom` triggers a warning for *any* white space in tables.\n// To follow GFM, `mdast-util-to-hast` injects line endings between elements.\n// Other tools might do so too, but they don’t do here, so we remove all of\n// that.\n\n// See: <https://github.com/facebook/react/pull/7081>.\n// See: <https://github.com/facebook/react/pull/7515>.\n// See: <https://github.com/remarkjs/remark-react/issues/64>.\n// See: <https://github.com/rehypejs/rehype-react/pull/29>.\n// See: <https://github.com/rehypejs/rehype-react/pull/32>.\n// See: <https://github.com/rehypejs/rehype-react/pull/45>.\nconst tableElements = new Set(['table', 'tbody', 'thead', 'tfoot', 'tr'])\n\nconst tableCellElement = new Set(['td', 'th'])\n\nconst docs = 'https://github.com/syntax-tree/hast-util-to-jsx-runtime'\n\n/**\n * Transform a hast tree to preact, react, solid, svelte, vue, etc.,\n * with an automatic JSX runtime.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options} options\n *   Configuration (required).\n * @returns {JsxElement}\n *   JSX element.\n */\n\nexport function toJsxRuntime(tree, options) {\n  if (!options || options.Fragment === undefined) {\n    throw new TypeError('Expected `Fragment` in options')\n  }\n\n  const filePath = options.filePath || undefined\n  /** @type {Create} */\n  let create\n\n  if (options.development) {\n    if (typeof options.jsxDEV !== 'function') {\n      throw new TypeError(\n        'Expected `jsxDEV` in options when `development: true`'\n      )\n    }\n\n    create = developmentCreate(filePath, options.jsxDEV)\n  } else {\n    if (typeof options.jsx !== 'function') {\n      throw new TypeError('Expected `jsx` in production options')\n    }\n\n    if (typeof options.jsxs !== 'function') {\n      throw new TypeError('Expected `jsxs` in production options')\n    }\n\n    create = productionCreate(filePath, options.jsx, options.jsxs)\n  }\n\n  /** @type {State} */\n  const state = {\n    Fragment: options.Fragment,\n    ancestors: [],\n    components: options.components || {},\n    create,\n    elementAttributeNameCase: options.elementAttributeNameCase || 'react',\n    evaluater: options.createEvaluater ? options.createEvaluater() : undefined,\n    filePath,\n    ignoreInvalidStyle: options.ignoreInvalidStyle || false,\n    passKeys: options.passKeys !== false,\n    passNode: options.passNode || false,\n    schema: options.space === 'svg' ? svg : html,\n    stylePropertyNameCase: options.stylePropertyNameCase || 'dom',\n    tableCellAlignToStyle: options.tableCellAlignToStyle !== false\n  }\n\n  const result = one(state, tree, undefined)\n\n  // JSX element.\n  if (result && typeof result !== 'string') {\n    return result\n  }\n\n  // Text node or something that turned into nothing.\n  return state.create(\n    tree,\n    state.Fragment,\n    {children: result || undefined},\n    undefined\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction one(state, node, key) {\n  if (node.type === 'element') {\n    return element(state, node, key)\n  }\n\n  if (node.type === 'mdxFlowExpression' || node.type === 'mdxTextExpression') {\n    return mdxExpression(state, node)\n  }\n\n  if (node.type === 'mdxJsxFlowElement' || node.type === 'mdxJsxTextElement') {\n    return mdxJsxElement(state, node, key)\n  }\n\n  if (node.type === 'mdxjsEsm') {\n    return mdxEsm(state, node)\n  }\n\n  if (node.type === 'root') {\n    return root(state, node, key)\n  }\n\n  if (node.type === 'text') {\n    return text(state, node)\n  }\n}\n\n/**\n * Handle element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction element(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.tagName.toLowerCase() === 'svg' && parentSchema.space === 'html') {\n    schema = svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type = findComponentFromName(state, node.tagName, false)\n  const props = createElementProps(state, node)\n  let children = createChildren(state, node)\n\n  if (tableElements.has(node.tagName)) {\n    children = children.filter(function (child) {\n      return typeof child === 'string' ? !whitespace(child) : true\n    })\n  }\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle MDX expression.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxFlowExpressionHast | MdxTextExpressionHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxExpression(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    const program = node.data.estree\n    const expression = program.body[0]\n    assert(expression.type === 'ExpressionStatement')\n\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateExpression(expression.expression)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX ESM.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxjsEsmHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxEsm(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateProgram(node.data.estree)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX JSX.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxJsxElement(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.name === 'svg' && parentSchema.space === 'html') {\n    schema = svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type =\n    node.name === null\n      ? state.Fragment\n      : findComponentFromName(state, node.name, true)\n  const props = createJsxElementProps(state, node)\n  const children = createChildren(state, node)\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Root} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction root(state, node, key) {\n  /** @type {Props} */\n  const props = {}\n\n  addChildren(props, createChildren(state, node))\n\n  return state.create(node, state.Fragment, props, key)\n}\n\n/**\n * Handle text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Text} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction text(_, node) {\n  return node.value\n}\n\n/**\n * Add `node` to props.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Props} props\n *   Props.\n * @param {unknown} type\n *   Type.\n * @param {Element | MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addNode(state, props, type, node) {\n  // If this is swapped out for a component:\n  if (typeof type !== 'string' && type !== state.Fragment && state.passNode) {\n    props.node = node\n  }\n}\n\n/**\n * Add children to props.\n *\n * @param {Props} props\n *   Props.\n * @param {Array<Child>} children\n *   Children.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChildren(props, children) {\n  if (children.length > 0) {\n    const value = children.length > 1 ? children : children[0]\n\n    if (value) {\n      props.children = value\n    }\n  }\n}\n\n/**\n * @param {string | undefined} _\n *   Path to file.\n * @param {Jsx} jsx\n *   Dynamic.\n * @param {Jsx} jsxs\n *   Static.\n * @returns {Create}\n *   Create a production element.\n */\nfunction productionCreate(_, jsx, jsxs) {\n  return create\n  /** @type {Create} */\n  function create(_, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const fn = isStaticChildren ? jsxs : jsx\n    return key ? fn(type, props, key) : fn(type, props)\n  }\n}\n\n/**\n * @param {string | undefined} filePath\n *   Path to file.\n * @param {JsxDev} jsxDEV\n *   Development.\n * @returns {Create}\n *   Create a development element.\n */\nfunction developmentCreate(filePath, jsxDEV) {\n  return create\n  /** @type {Create} */\n  function create(node, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const point = pointStart(node)\n    return jsxDEV(\n      type,\n      props,\n      key,\n      isStaticChildren,\n      {\n        columnNumber: point ? point.column - 1 : undefined,\n        fileName: filePath,\n        lineNumber: point ? point.line : undefined\n      },\n      undefined\n    )\n  }\n}\n\n/**\n * Create props from an element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current element.\n * @returns {Props}\n *   Props.\n */\nfunction createElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n  /** @type {string | undefined} */\n  let alignValue\n  /** @type {string} */\n  let prop\n\n  for (prop in node.properties) {\n    if (prop !== 'children' && own.call(node.properties, prop)) {\n      const result = createProperty(state, prop, node.properties[prop])\n\n      if (result) {\n        const [key, value] = result\n\n        if (\n          state.tableCellAlignToStyle &&\n          key === 'align' &&\n          typeof value === 'string' &&\n          tableCellElement.has(node.tagName)\n        ) {\n          alignValue = value\n        } else {\n          props[key] = value\n        }\n      }\n    }\n  }\n\n  if (alignValue) {\n    // Assume style is an object.\n    const style = /** @type {Style} */ (props.style || (props.style = {}))\n    style[state.stylePropertyNameCase === 'css' ? 'text-align' : 'textAlign'] =\n      alignValue\n  }\n\n  return props\n}\n\n/**\n * Create props from a JSX element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current JSX element.\n * @returns {Props}\n *   Props.\n */\nfunction createJsxElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n\n  for (const attribute of node.attributes) {\n    if (attribute.type === 'mdxJsxExpressionAttribute') {\n      if (attribute.data && attribute.data.estree && state.evaluater) {\n        const program = attribute.data.estree\n        const expression = program.body[0]\n        assert(expression.type === 'ExpressionStatement')\n        const objectExpression = expression.expression\n        assert(objectExpression.type === 'ObjectExpression')\n        const property = objectExpression.properties[0]\n        assert(property.type === 'SpreadElement')\n\n        Object.assign(\n          props,\n          state.evaluater.evaluateExpression(property.argument)\n        )\n      } else {\n        crashEstree(state, node.position)\n      }\n    } else {\n      // For JSX, the author is responsible of passing in the correct values.\n      const name = attribute.name\n      /** @type {unknown} */\n      let value\n\n      if (attribute.value && typeof attribute.value === 'object') {\n        if (\n          attribute.value.data &&\n          attribute.value.data.estree &&\n          state.evaluater\n        ) {\n          const program = attribute.value.data.estree\n          const expression = program.body[0]\n          assert(expression.type === 'ExpressionStatement')\n          value = state.evaluater.evaluateExpression(expression.expression)\n        } else {\n          crashEstree(state, node.position)\n        }\n      } else {\n        value = attribute.value === null ? true : attribute.value\n      }\n\n      // Assume a prop.\n      props[name] = /** @type {Props[keyof Props]} */ (value)\n    }\n  }\n\n  return props\n}\n\n/**\n * Create children.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Parents} node\n *   Current element.\n * @returns {Array<Child>}\n *   Children.\n */\nfunction createChildren(state, node) {\n  /** @type {Array<Child>} */\n  const children = []\n  let index = -1\n  /** @type {Map<string, number>} */\n  // Note: test this when Solid doesn’t want to merge my upcoming PR.\n  /* c8 ignore next */\n  const countsByName = state.passKeys ? new Map() : emptyMap\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n    /** @type {string | undefined} */\n    let key\n\n    if (state.passKeys) {\n      const name =\n        child.type === 'element'\n          ? child.tagName\n          : child.type === 'mdxJsxFlowElement' ||\n              child.type === 'mdxJsxTextElement'\n            ? child.name\n            : undefined\n\n      if (name) {\n        const count = countsByName.get(name) || 0\n        key = name + '-' + count\n        countsByName.set(name, count + 1)\n      }\n    }\n\n    const result = one(state, child, key)\n    if (result !== undefined) children.push(result)\n  }\n\n  return children\n}\n\n/**\n * Handle a property.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Field | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(state, prop, value) {\n  const info = find(state.schema, prop)\n\n  // Ignore nullish and `NaN` values.\n  if (\n    value === null ||\n    value === undefined ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return\n  }\n\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? commas(value) : spaces(value)\n  }\n\n  // React only accepts `style` as object.\n  if (info.property === 'style') {\n    let styleObject =\n      typeof value === 'object' ? value : parseStyle(state, String(value))\n\n    if (state.stylePropertyNameCase === 'css') {\n      styleObject = transformStylesToCssCasing(styleObject)\n    }\n\n    return ['style', styleObject]\n  }\n\n  return [\n    state.elementAttributeNameCase === 'react' && info.space\n      ? hastToReact[info.property] || info.property\n      : info.attribute,\n    value\n  ]\n}\n\n/**\n * Parse a CSS declaration to an object.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} value\n *   CSS declarations.\n * @returns {Style}\n *   Properties.\n * @throws\n *   Throws `VFileMessage` when CSS cannot be parsed.\n */\nfunction parseStyle(state, value) {\n  try {\n    return styleToJs(value, {reactCompat: true})\n  } catch (error) {\n    if (state.ignoreInvalidStyle) {\n      return {}\n    }\n\n    const cause = /** @type {Error} */ (error)\n    const message = new VFileMessage('Cannot parse `style` attribute', {\n      ancestors: state.ancestors,\n      cause,\n      ruleId: 'style',\n      source: 'hast-util-to-jsx-runtime'\n    })\n    message.file = state.filePath || undefined\n    message.url = docs + '#cannot-parse-style-attribute'\n\n    throw message\n  }\n}\n\n/**\n * Create a JSX name from a string.\n *\n * @param {State} state\n *   To do.\n * @param {string} name\n *   Name.\n * @param {boolean} allowExpression\n *   Allow member expressions and identifiers.\n * @returns {unknown}\n *   To do.\n */\nfunction findComponentFromName(state, name, allowExpression) {\n  /** @type {Identifier | Literal | MemberExpression} */\n  let result\n\n  if (!allowExpression) {\n    result = {type: 'Literal', value: name}\n  } else if (name.includes('.')) {\n    const identifiers = name.split('.')\n    let index = -1\n    /** @type {Identifier | Literal | MemberExpression | undefined} */\n    let node\n\n    while (++index < identifiers.length) {\n      /** @type {Identifier | Literal} */\n      const prop = isIdentifierName(identifiers[index])\n        ? {type: 'Identifier', name: identifiers[index]}\n        : {type: 'Literal', value: identifiers[index]}\n      node = node\n        ? {\n            type: 'MemberExpression',\n            object: node,\n            property: prop,\n            computed: Boolean(index && prop.type === 'Literal'),\n            optional: false\n          }\n        : prop\n    }\n\n    assert(node, 'always a result')\n    result = node\n  } else {\n    result =\n      isIdentifierName(name) && !/^[a-z]/.test(name)\n        ? {type: 'Identifier', name}\n        : {type: 'Literal', value: name}\n  }\n\n  // Only literals can be passed in `components` currently.\n  // No identifiers / member expressions.\n  if (result.type === 'Literal') {\n    const name = /** @type {string | number} */ (result.value)\n    return own.call(state.components, name) ? state.components[name] : name\n  }\n\n  // Assume component.\n  if (state.evaluater) {\n    return state.evaluater.evaluateExpression(result)\n  }\n\n  crashEstree(state)\n}\n\n/**\n * @param {State} state\n * @param {Position | undefined} [place]\n * @returns {never}\n */\nfunction crashEstree(state, place) {\n  const message = new VFileMessage(\n    'Cannot handle MDX estrees without `createEvaluater`',\n    {\n      ancestors: state.ancestors,\n      place,\n      ruleId: 'mdx-estree',\n      source: 'hast-util-to-jsx-runtime'\n    }\n  )\n  message.file = state.filePath || undefined\n  message.url = docs + '#cannot-handle-mdx-estrees-without-createevaluater'\n\n  throw message\n}\n\n/**\n * Transform a DOM casing style object to a CSS casing style object.\n *\n * @param {Style} domCasing\n * @returns {Style}\n */\nfunction transformStylesToCssCasing(domCasing) {\n  /** @type {Style} */\n  const cssCasing = {}\n  /** @type {string} */\n  let from\n\n  for (from in domCasing) {\n    if (own.call(domCasing, from)) {\n      cssCasing[transformStyleToCssCasing(from)] = domCasing[from]\n    }\n  }\n\n  return cssCasing\n}\n\n/**\n * Transform a DOM casing style field to a CSS casing style field.\n *\n * @param {string} from\n * @returns {string}\n */\nfunction transformStyleToCssCasing(from) {\n  let to = from.replace(cap, toDash)\n  // Handle `ms-xxx` -> `-ms-xxx`.\n  if (to.slice(0, 3) === 'ms-') to = '-' + to\n  return to\n}\n\n/**\n * Make `$0` dash cased.\n *\n * @param {string} $0\n *   Capitalized ASCII leter.\n * @returns {string}\n *   Dash and lower letter.\n */\nfunction toDash($0) {\n  return '-' + $0.toLowerCase()\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,SAAS,IAAIC,MAAM,QAAO,wBAAwB;AAC1D,SAAQC,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,IAAI,IAAIC,gBAAgB,QAAO,gCAAgC;AACvE,SAAQC,UAAU,QAAO,sBAAsB;AAC/C,SAAQC,IAAI,EAAEC,WAAW,EAAEC,IAAI,EAAEC,GAAG,QAAO,sBAAsB;AACjE,SAAQV,SAAS,IAAIW,MAAM,QAAO,wBAAwB;AAC1D,OAAOC,SAAS,MAAM,aAAa;AACnC,SAAQC,UAAU,QAAO,qBAAqB;AAC9C,SAAQC,YAAY,QAAO,eAAe;;AAE1C;AACA,MAAMC,GAAG,GAAG,CAAC,CAAC,CAACC,cAAc;;AAE7B;AACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;AAE1B,MAAMC,GAAG,GAAG,QAAQ;;AAEpB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAEzE,MAAMC,gBAAgB,GAAG,IAAID,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAE9C,MAAME,IAAI,GAAG,yDAAyD;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC1C,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACC,QAAQ,KAAKC,SAAS,EAAE;IAC9C,MAAM,IAAIC,SAAS,CAAC,gCAAgC,CAAC;EACvD;EAEA,MAAMC,QAAQ,GAAGJ,OAAO,CAACI,QAAQ,IAAIF,SAAS;EAC9C;EACA,IAAIG,MAAM;EAEV,IAAIL,OAAO,CAACM,WAAW,EAAE;IACvB,IAAI,OAAON,OAAO,CAACO,MAAM,KAAK,UAAU,EAAE;MACxC,MAAM,IAAIJ,SAAS,CACjB,uDACF,CAAC;IACH;IAEAE,MAAM,GAAGG,iBAAiB,CAACJ,QAAQ,EAAEJ,OAAO,CAACO,MAAM,CAAC;EACtD,CAAC,MAAM;IACL,IAAI,OAAOP,OAAO,CAACS,GAAG,KAAK,UAAU,EAAE;MACrC,MAAM,IAAIN,SAAS,CAAC,sCAAsC,CAAC;IAC7D;IAEA,IAAI,OAAOH,OAAO,CAACU,IAAI,KAAK,UAAU,EAAE;MACtC,MAAM,IAAIP,SAAS,CAAC,uCAAuC,CAAC;IAC9D;IAEAE,MAAM,GAAGM,gBAAgB,CAACP,QAAQ,EAAEJ,OAAO,CAACS,GAAG,EAAET,OAAO,CAACU,IAAI,CAAC;EAChE;;EAEA;EACA,MAAME,KAAK,GAAG;IACZX,QAAQ,EAAED,OAAO,CAACC,QAAQ;IAC1BY,SAAS,EAAE,EAAE;IACbC,UAAU,EAAEd,OAAO,CAACc,UAAU,IAAI,CAAC,CAAC;IACpCT,MAAM;IACNU,wBAAwB,EAAEf,OAAO,CAACe,wBAAwB,IAAI,OAAO;IACrEC,SAAS,EAAEhB,OAAO,CAACiB,eAAe,GAAGjB,OAAO,CAACiB,eAAe,CAAC,CAAC,GAAGf,SAAS;IAC1EE,QAAQ;IACRc,kBAAkB,EAAElB,OAAO,CAACkB,kBAAkB,IAAI,KAAK;IACvDC,QAAQ,EAAEnB,OAAO,CAACmB,QAAQ,KAAK,KAAK;IACpCC,QAAQ,EAAEpB,OAAO,CAACoB,QAAQ,IAAI,KAAK;IACnCC,MAAM,EAAErB,OAAO,CAACsB,KAAK,KAAK,KAAK,GAAGtC,GAAG,GAAGD,IAAI;IAC5CwC,qBAAqB,EAAEvB,OAAO,CAACuB,qBAAqB,IAAI,KAAK;IAC7DC,qBAAqB,EAAExB,OAAO,CAACwB,qBAAqB,KAAK;EAC3D,CAAC;EAED,MAAMC,MAAM,GAAGC,GAAG,CAACd,KAAK,EAAEb,IAAI,EAAEG,SAAS,CAAC;;EAE1C;EACA,IAAIuB,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IACxC,OAAOA,MAAM;EACf;;EAEA;EACA,OAAOb,KAAK,CAACP,MAAM,CACjBN,IAAI,EACJa,KAAK,CAACX,QAAQ,EACd;IAAC0B,QAAQ,EAAEF,MAAM,IAAIvB;EAAS,CAAC,EAC/BA,SACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwB,GAAGA,CAACd,KAAK,EAAEgB,IAAI,EAAEC,GAAG,EAAE;EAC7B,IAAID,IAAI,CAACE,IAAI,KAAK,SAAS,EAAE;IAC3B,OAAOC,OAAO,CAACnB,KAAK,EAAEgB,IAAI,EAAEC,GAAG,CAAC;EAClC;EAEA,IAAID,IAAI,CAACE,IAAI,KAAK,mBAAmB,IAAIF,IAAI,CAACE,IAAI,KAAK,mBAAmB,EAAE;IAC1E,OAAOE,aAAa,CAACpB,KAAK,EAAEgB,IAAI,CAAC;EACnC;EAEA,IAAIA,IAAI,CAACE,IAAI,KAAK,mBAAmB,IAAIF,IAAI,CAACE,IAAI,KAAK,mBAAmB,EAAE;IAC1E,OAAOG,aAAa,CAACrB,KAAK,EAAEgB,IAAI,EAAEC,GAAG,CAAC;EACxC;EAEA,IAAID,IAAI,CAACE,IAAI,KAAK,UAAU,EAAE;IAC5B,OAAOI,MAAM,CAACtB,KAAK,EAAEgB,IAAI,CAAC;EAC5B;EAEA,IAAIA,IAAI,CAACE,IAAI,KAAK,MAAM,EAAE;IACxB,OAAOK,IAAI,CAACvB,KAAK,EAAEgB,IAAI,EAAEC,GAAG,CAAC;EAC/B;EAEA,IAAID,IAAI,CAACE,IAAI,KAAK,MAAM,EAAE;IACxB,OAAOM,IAAI,CAACxB,KAAK,EAAEgB,IAAI,CAAC;EAC1B;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,OAAOA,CAACnB,KAAK,EAAEgB,IAAI,EAAEC,GAAG,EAAE;EACjC,MAAMQ,YAAY,GAAGzB,KAAK,CAACS,MAAM;EACjC,IAAIA,MAAM,GAAGgB,YAAY;EAEzB,IAAIT,IAAI,CAACU,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,IAAIF,YAAY,CAACf,KAAK,KAAK,MAAM,EAAE;IACzED,MAAM,GAAGrC,GAAG;IACZ4B,KAAK,CAACS,MAAM,GAAGA,MAAM;EACvB;EAEAT,KAAK,CAACC,SAAS,CAAC2B,IAAI,CAACZ,IAAI,CAAC;EAE1B,MAAME,IAAI,GAAGW,qBAAqB,CAAC7B,KAAK,EAAEgB,IAAI,CAACU,OAAO,EAAE,KAAK,CAAC;EAC9D,MAAMI,KAAK,GAAGC,kBAAkB,CAAC/B,KAAK,EAAEgB,IAAI,CAAC;EAC7C,IAAID,QAAQ,GAAGiB,cAAc,CAAChC,KAAK,EAAEgB,IAAI,CAAC;EAE1C,IAAIlC,aAAa,CAACmD,GAAG,CAACjB,IAAI,CAACU,OAAO,CAAC,EAAE;IACnCX,QAAQ,GAAGA,QAAQ,CAACmB,MAAM,CAAC,UAAUC,KAAK,EAAE;MAC1C,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAG,CAACnE,UAAU,CAACmE,KAAK,CAAC,GAAG,IAAI;IAC9D,CAAC,CAAC;EACJ;EAEAC,OAAO,CAACpC,KAAK,EAAE8B,KAAK,EAAEZ,IAAI,EAAEF,IAAI,CAAC;EACjCqB,WAAW,CAACP,KAAK,EAAEf,QAAQ,CAAC;;EAE5B;EACAf,KAAK,CAACC,SAAS,CAACqC,GAAG,CAAC,CAAC;EACrBtC,KAAK,CAACS,MAAM,GAAGgB,YAAY;EAE3B,OAAOzB,KAAK,CAACP,MAAM,CAACuB,IAAI,EAAEE,IAAI,EAAEY,KAAK,EAAEb,GAAG,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,aAAaA,CAACpB,KAAK,EAAEgB,IAAI,EAAE;EAClC,IAAIA,IAAI,CAACuB,IAAI,IAAIvB,IAAI,CAACuB,IAAI,CAACC,MAAM,IAAIxC,KAAK,CAACI,SAAS,EAAE;IACpD,MAAMqC,OAAO,GAAGzB,IAAI,CAACuB,IAAI,CAACC,MAAM;IAChC,MAAME,UAAU,GAAGD,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC;IAClC9E,MAAM,CAAC6E,UAAU,CAACxB,IAAI,KAAK,qBAAqB,CAAC;;IAEjD;IACA,OAAO,gCACLlB,KAAK,CAACI,SAAS,CAACwC,kBAAkB,CAACF,UAAU,CAACA,UAAU,CAAC;EAE7D;EAEAG,WAAW,CAAC7C,KAAK,EAAEgB,IAAI,CAAC8B,QAAQ,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASxB,MAAMA,CAACtB,KAAK,EAAEgB,IAAI,EAAE;EAC3B,IAAIA,IAAI,CAACuB,IAAI,IAAIvB,IAAI,CAACuB,IAAI,CAACC,MAAM,IAAIxC,KAAK,CAACI,SAAS,EAAE;IACpD;IACA,OAAO,gCACLJ,KAAK,CAACI,SAAS,CAAC2C,eAAe,CAAC/B,IAAI,CAACuB,IAAI,CAACC,MAAM,CAAC;EAErD;EAEAK,WAAW,CAAC7C,KAAK,EAAEgB,IAAI,CAAC8B,QAAQ,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASzB,aAAaA,CAACrB,KAAK,EAAEgB,IAAI,EAAEC,GAAG,EAAE;EACvC,MAAMQ,YAAY,GAAGzB,KAAK,CAACS,MAAM;EACjC,IAAIA,MAAM,GAAGgB,YAAY;EAEzB,IAAIT,IAAI,CAAClD,IAAI,KAAK,KAAK,IAAI2D,YAAY,CAACf,KAAK,KAAK,MAAM,EAAE;IACxDD,MAAM,GAAGrC,GAAG;IACZ4B,KAAK,CAACS,MAAM,GAAGA,MAAM;EACvB;EAEAT,KAAK,CAACC,SAAS,CAAC2B,IAAI,CAACZ,IAAI,CAAC;EAE1B,MAAME,IAAI,GACRF,IAAI,CAAClD,IAAI,KAAK,IAAI,GACdkC,KAAK,CAACX,QAAQ,GACdwC,qBAAqB,CAAC7B,KAAK,EAAEgB,IAAI,CAAClD,IAAI,EAAE,IAAI,CAAC;EACnD,MAAMgE,KAAK,GAAGkB,qBAAqB,CAAChD,KAAK,EAAEgB,IAAI,CAAC;EAChD,MAAMD,QAAQ,GAAGiB,cAAc,CAAChC,KAAK,EAAEgB,IAAI,CAAC;EAE5CoB,OAAO,CAACpC,KAAK,EAAE8B,KAAK,EAAEZ,IAAI,EAAEF,IAAI,CAAC;EACjCqB,WAAW,CAACP,KAAK,EAAEf,QAAQ,CAAC;;EAE5B;EACAf,KAAK,CAACC,SAAS,CAACqC,GAAG,CAAC,CAAC;EACrBtC,KAAK,CAACS,MAAM,GAAGgB,YAAY;EAE3B,OAAOzB,KAAK,CAACP,MAAM,CAACuB,IAAI,EAAEE,IAAI,EAAEY,KAAK,EAAEb,GAAG,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,IAAIA,CAACvB,KAAK,EAAEgB,IAAI,EAAEC,GAAG,EAAE;EAC9B;EACA,MAAMa,KAAK,GAAG,CAAC,CAAC;EAEhBO,WAAW,CAACP,KAAK,EAAEE,cAAc,CAAChC,KAAK,EAAEgB,IAAI,CAAC,CAAC;EAE/C,OAAOhB,KAAK,CAACP,MAAM,CAACuB,IAAI,EAAEhB,KAAK,CAACX,QAAQ,EAAEyC,KAAK,EAAEb,GAAG,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,IAAIA,CAACyB,CAAC,EAAEjC,IAAI,EAAE;EACrB,OAAOA,IAAI,CAACkC,KAAK;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASd,OAAOA,CAACpC,KAAK,EAAE8B,KAAK,EAAEZ,IAAI,EAAEF,IAAI,EAAE;EACzC;EACA,IAAI,OAAOE,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAKlB,KAAK,CAACX,QAAQ,IAAIW,KAAK,CAACQ,QAAQ,EAAE;IACzEsB,KAAK,CAACd,IAAI,GAAGA,IAAI;EACnB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqB,WAAWA,CAACP,KAAK,EAAEf,QAAQ,EAAE;EACpC,IAAIA,QAAQ,CAACoC,MAAM,GAAG,CAAC,EAAE;IACvB,MAAMD,KAAK,GAAGnC,QAAQ,CAACoC,MAAM,GAAG,CAAC,GAAGpC,QAAQ,GAAGA,QAAQ,CAAC,CAAC,CAAC;IAE1D,IAAImC,KAAK,EAAE;MACTpB,KAAK,CAACf,QAAQ,GAAGmC,KAAK;IACxB;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnD,gBAAgBA,CAACkD,CAAC,EAAEpD,GAAG,EAAEC,IAAI,EAAE;EACtC,OAAOL,MAAM;EACb;EACA,SAASA,MAAMA,CAACwD,CAAC,EAAE/B,IAAI,EAAEY,KAAK,EAAEb,GAAG,EAAE;IACnC;IACA,MAAMmC,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACxB,KAAK,CAACf,QAAQ,CAAC;IACtD,MAAMwC,EAAE,GAAGH,gBAAgB,GAAGtD,IAAI,GAAGD,GAAG;IACxC,OAAOoB,GAAG,GAAGsC,EAAE,CAACrC,IAAI,EAAEY,KAAK,EAAEb,GAAG,CAAC,GAAGsC,EAAE,CAACrC,IAAI,EAAEY,KAAK,CAAC;EACrD;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASlC,iBAAiBA,CAACJ,QAAQ,EAAEG,MAAM,EAAE;EAC3C,OAAOF,MAAM;EACb;EACA,SAASA,MAAMA,CAACuB,IAAI,EAAEE,IAAI,EAAEY,KAAK,EAAEb,GAAG,EAAE;IACtC;IACA,MAAMmC,gBAAgB,GAAGC,KAAK,CAACC,OAAO,CAACxB,KAAK,CAACf,QAAQ,CAAC;IACtD,MAAMyC,KAAK,GAAGjF,UAAU,CAACyC,IAAI,CAAC;IAC9B,OAAOrB,MAAM,CACXuB,IAAI,EACJY,KAAK,EACLb,GAAG,EACHmC,gBAAgB,EAChB;MACEK,YAAY,EAAED,KAAK,GAAGA,KAAK,CAACE,MAAM,GAAG,CAAC,GAAGpE,SAAS;MAClDqE,QAAQ,EAAEnE,QAAQ;MAClBoE,UAAU,EAAEJ,KAAK,GAAGA,KAAK,CAACK,IAAI,GAAGvE;IACnC,CAAC,EACDA,SACF,CAAC;EACH;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyC,kBAAkBA,CAAC/B,KAAK,EAAEgB,IAAI,EAAE;EACvC;EACA,MAAMc,KAAK,GAAG,CAAC,CAAC;EAChB;EACA,IAAIgC,UAAU;EACd;EACA,IAAIC,IAAI;EAER,KAAKA,IAAI,IAAI/C,IAAI,CAACgD,UAAU,EAAE;IAC5B,IAAID,IAAI,KAAK,UAAU,IAAItF,GAAG,CAACwF,IAAI,CAACjD,IAAI,CAACgD,UAAU,EAAED,IAAI,CAAC,EAAE;MAC1D,MAAMlD,MAAM,GAAGqD,cAAc,CAAClE,KAAK,EAAE+D,IAAI,EAAE/C,IAAI,CAACgD,UAAU,CAACD,IAAI,CAAC,CAAC;MAEjE,IAAIlD,MAAM,EAAE;QACV,MAAM,CAACI,GAAG,EAAEiC,KAAK,CAAC,GAAGrC,MAAM;QAE3B,IACEb,KAAK,CAACY,qBAAqB,IAC3BK,GAAG,KAAK,OAAO,IACf,OAAOiC,KAAK,KAAK,QAAQ,IACzBlE,gBAAgB,CAACiD,GAAG,CAACjB,IAAI,CAACU,OAAO,CAAC,EAClC;UACAoC,UAAU,GAAGZ,KAAK;QACpB,CAAC,MAAM;UACLpB,KAAK,CAACb,GAAG,CAAC,GAAGiC,KAAK;QACpB;MACF;IACF;EACF;EAEA,IAAIY,UAAU,EAAE;IACd;IACA,MAAMK,KAAK,GAAG,oBAAsBrC,KAAK,CAACqC,KAAK,KAAKrC,KAAK,CAACqC,KAAK,GAAG,CAAC,CAAC,CAAE;IACtEA,KAAK,CAACnE,KAAK,CAACW,qBAAqB,KAAK,KAAK,GAAG,YAAY,GAAG,WAAW,CAAC,GACvEmD,UAAU;EACd;EAEA,OAAOhC,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkB,qBAAqBA,CAAChD,KAAK,EAAEgB,IAAI,EAAE;EAC1C;EACA,MAAMc,KAAK,GAAG,CAAC,CAAC;EAEhB,KAAK,MAAMsC,SAAS,IAAIpD,IAAI,CAACqD,UAAU,EAAE;IACvC,IAAID,SAAS,CAAClD,IAAI,KAAK,2BAA2B,EAAE;MAClD,IAAIkD,SAAS,CAAC7B,IAAI,IAAI6B,SAAS,CAAC7B,IAAI,CAACC,MAAM,IAAIxC,KAAK,CAACI,SAAS,EAAE;QAC9D,MAAMqC,OAAO,GAAG2B,SAAS,CAAC7B,IAAI,CAACC,MAAM;QACrC,MAAME,UAAU,GAAGD,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC;QAClC9E,MAAM,CAAC6E,UAAU,CAACxB,IAAI,KAAK,qBAAqB,CAAC;QACjD,MAAMoD,gBAAgB,GAAG5B,UAAU,CAACA,UAAU;QAC9C7E,MAAM,CAACyG,gBAAgB,CAACpD,IAAI,KAAK,kBAAkB,CAAC;QACpD,MAAMqD,QAAQ,GAAGD,gBAAgB,CAACN,UAAU,CAAC,CAAC,CAAC;QAC/CnG,MAAM,CAAC0G,QAAQ,CAACrD,IAAI,KAAK,eAAe,CAAC;QAEzCsD,MAAM,CAACC,MAAM,CACX3C,KAAK,EACL9B,KAAK,CAACI,SAAS,CAACwC,kBAAkB,CAAC2B,QAAQ,CAACG,QAAQ,CACtD,CAAC;MACH,CAAC,MAAM;QACL7B,WAAW,CAAC7C,KAAK,EAAEgB,IAAI,CAAC8B,QAAQ,CAAC;MACnC;IACF,CAAC,MAAM;MACL;MACA,MAAMhF,IAAI,GAAGsG,SAAS,CAACtG,IAAI;MAC3B;MACA,IAAIoF,KAAK;MAET,IAAIkB,SAAS,CAAClB,KAAK,IAAI,OAAOkB,SAAS,CAAClB,KAAK,KAAK,QAAQ,EAAE;QAC1D,IACEkB,SAAS,CAAClB,KAAK,CAACX,IAAI,IACpB6B,SAAS,CAAClB,KAAK,CAACX,IAAI,CAACC,MAAM,IAC3BxC,KAAK,CAACI,SAAS,EACf;UACA,MAAMqC,OAAO,GAAG2B,SAAS,CAAClB,KAAK,CAACX,IAAI,CAACC,MAAM;UAC3C,MAAME,UAAU,GAAGD,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC;UAClC9E,MAAM,CAAC6E,UAAU,CAACxB,IAAI,KAAK,qBAAqB,CAAC;UACjDgC,KAAK,GAAGlD,KAAK,CAACI,SAAS,CAACwC,kBAAkB,CAACF,UAAU,CAACA,UAAU,CAAC;QACnE,CAAC,MAAM;UACLG,WAAW,CAAC7C,KAAK,EAAEgB,IAAI,CAAC8B,QAAQ,CAAC;QACnC;MACF,CAAC,MAAM;QACLI,KAAK,GAAGkB,SAAS,CAAClB,KAAK,KAAK,IAAI,GAAG,IAAI,GAAGkB,SAAS,CAAClB,KAAK;MAC3D;;MAEA;MACApB,KAAK,CAAChE,IAAI,CAAC,GAAG,iCAAmCoF,KAAM;IACzD;EACF;EAEA,OAAOpB,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,cAAcA,CAAChC,KAAK,EAAEgB,IAAI,EAAE;EACnC;EACA,MAAMD,QAAQ,GAAG,EAAE;EACnB,IAAI4D,KAAK,GAAG,CAAC,CAAC;EACd;EACA;EACA;EACA,MAAMC,YAAY,GAAG5E,KAAK,CAACO,QAAQ,GAAG,IAAI3B,GAAG,CAAC,CAAC,GAAGD,QAAQ;EAE1D,OAAO,EAAEgG,KAAK,GAAG3D,IAAI,CAACD,QAAQ,CAACoC,MAAM,EAAE;IACrC,MAAMhB,KAAK,GAAGnB,IAAI,CAACD,QAAQ,CAAC4D,KAAK,CAAC;IAClC;IACA,IAAI1D,GAAG;IAEP,IAAIjB,KAAK,CAACO,QAAQ,EAAE;MAClB,MAAMzC,IAAI,GACRqE,KAAK,CAACjB,IAAI,KAAK,SAAS,GACpBiB,KAAK,CAACT,OAAO,GACbS,KAAK,CAACjB,IAAI,KAAK,mBAAmB,IAChCiB,KAAK,CAACjB,IAAI,KAAK,mBAAmB,GAClCiB,KAAK,CAACrE,IAAI,GACVwB,SAAS;MAEjB,IAAIxB,IAAI,EAAE;QACR,MAAM+G,KAAK,GAAGD,YAAY,CAACE,GAAG,CAAChH,IAAI,CAAC,IAAI,CAAC;QACzCmD,GAAG,GAAGnD,IAAI,GAAG,GAAG,GAAG+G,KAAK;QACxBD,YAAY,CAACG,GAAG,CAACjH,IAAI,EAAE+G,KAAK,GAAG,CAAC,CAAC;MACnC;IACF;IAEA,MAAMhE,MAAM,GAAGC,GAAG,CAACd,KAAK,EAAEmC,KAAK,EAAElB,GAAG,CAAC;IACrC,IAAIJ,MAAM,KAAKvB,SAAS,EAAEyB,QAAQ,CAACa,IAAI,CAACf,MAAM,CAAC;EACjD;EAEA,OAAOE,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmD,cAAcA,CAAClE,KAAK,EAAE+D,IAAI,EAAEb,KAAK,EAAE;EAC1C,MAAM8B,IAAI,GAAG/G,IAAI,CAAC+B,KAAK,CAACS,MAAM,EAAEsD,IAAI,CAAC;;EAErC;EACA,IACEb,KAAK,KAAK,IAAI,IACdA,KAAK,KAAK5D,SAAS,IAClB,OAAO4D,KAAK,KAAK,QAAQ,IAAI+B,MAAM,CAACC,KAAK,CAAChC,KAAK,CAAE,EAClD;IACA;EACF;EAEA,IAAIG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;IACxB;IACA;IACAA,KAAK,GAAG8B,IAAI,CAACG,cAAc,GAAGxH,MAAM,CAACuF,KAAK,CAAC,GAAG7E,MAAM,CAAC6E,KAAK,CAAC;EAC7D;;EAEA;EACA,IAAI8B,IAAI,CAACT,QAAQ,KAAK,OAAO,EAAE;IAC7B,IAAIa,WAAW,GACb,OAAOlC,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGmC,UAAU,CAACrF,KAAK,EAAEsF,MAAM,CAACpC,KAAK,CAAC,CAAC;IAEtE,IAAIlD,KAAK,CAACW,qBAAqB,KAAK,KAAK,EAAE;MACzCyE,WAAW,GAAGG,0BAA0B,CAACH,WAAW,CAAC;IACvD;IAEA,OAAO,CAAC,OAAO,EAAEA,WAAW,CAAC;EAC/B;EAEA,OAAO,CACLpF,KAAK,CAACG,wBAAwB,KAAK,OAAO,IAAI6E,IAAI,CAACtE,KAAK,GACpDxC,WAAW,CAAC8G,IAAI,CAACT,QAAQ,CAAC,IAAIS,IAAI,CAACT,QAAQ,GAC3CS,IAAI,CAACZ,SAAS,EAClBlB,KAAK,CACN;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmC,UAAUA,CAACrF,KAAK,EAAEkD,KAAK,EAAE;EAChC,IAAI;IACF,OAAO5E,SAAS,CAAC4E,KAAK,EAAE;MAACsC,WAAW,EAAE;IAAI,CAAC,CAAC;EAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,IAAIzF,KAAK,CAACM,kBAAkB,EAAE;MAC5B,OAAO,CAAC,CAAC;IACX;IAEA,MAAMoF,KAAK,GAAG,oBAAsBD,KAAM;IAC1C,MAAME,OAAO,GAAG,IAAInH,YAAY,CAAC,gCAAgC,EAAE;MACjEyB,SAAS,EAAED,KAAK,CAACC,SAAS;MAC1ByF,KAAK;MACLE,MAAM,EAAE,OAAO;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;IACFF,OAAO,CAACG,IAAI,GAAG9F,KAAK,CAACR,QAAQ,IAAIF,SAAS;IAC1CqG,OAAO,CAACI,GAAG,GAAG9G,IAAI,GAAG,+BAA+B;IAEpD,MAAM0G,OAAO;EACf;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9D,qBAAqBA,CAAC7B,KAAK,EAAElC,IAAI,EAAEkI,eAAe,EAAE;EAC3D;EACA,IAAInF,MAAM;EAEV,IAAI,CAACmF,eAAe,EAAE;IACpBnF,MAAM,GAAG;MAACK,IAAI,EAAE,SAAS;MAAEgC,KAAK,EAAEpF;IAAI,CAAC;EACzC,CAAC,MAAM,IAAIA,IAAI,CAACmI,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC7B,MAAMC,WAAW,GAAGpI,IAAI,CAACqI,KAAK,CAAC,GAAG,CAAC;IACnC,IAAIxB,KAAK,GAAG,CAAC,CAAC;IACd;IACA,IAAI3D,IAAI;IAER,OAAO,EAAE2D,KAAK,GAAGuB,WAAW,CAAC/C,MAAM,EAAE;MACnC;MACA,MAAMY,IAAI,GAAGhG,gBAAgB,CAACmI,WAAW,CAACvB,KAAK,CAAC,CAAC,GAC7C;QAACzD,IAAI,EAAE,YAAY;QAAEpD,IAAI,EAAEoI,WAAW,CAACvB,KAAK;MAAC,CAAC,GAC9C;QAACzD,IAAI,EAAE,SAAS;QAAEgC,KAAK,EAAEgD,WAAW,CAACvB,KAAK;MAAC,CAAC;MAChD3D,IAAI,GAAGA,IAAI,GACP;QACEE,IAAI,EAAE,kBAAkB;QACxBkF,MAAM,EAAEpF,IAAI;QACZuD,QAAQ,EAAER,IAAI;QACdsC,QAAQ,EAAEC,OAAO,CAAC3B,KAAK,IAAIZ,IAAI,CAAC7C,IAAI,KAAK,SAAS,CAAC;QACnDqF,QAAQ,EAAE;MACZ,CAAC,GACDxC,IAAI;IACV;IAEAlG,MAAM,CAACmD,IAAI,EAAE,iBAAiB,CAAC;IAC/BH,MAAM,GAAGG,IAAI;EACf,CAAC,MAAM;IACLH,MAAM,GACJ9C,gBAAgB,CAACD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC0I,IAAI,CAAC1I,IAAI,CAAC,GAC1C;MAACoD,IAAI,EAAE,YAAY;MAAEpD;IAAI,CAAC,GAC1B;MAACoD,IAAI,EAAE,SAAS;MAAEgC,KAAK,EAAEpF;IAAI,CAAC;EACtC;;EAEA;EACA;EACA,IAAI+C,MAAM,CAACK,IAAI,KAAK,SAAS,EAAE;IAC7B,MAAMpD,IAAI,GAAG,8BAAgC+C,MAAM,CAACqC,KAAM;IAC1D,OAAOzE,GAAG,CAACwF,IAAI,CAACjE,KAAK,CAACE,UAAU,EAAEpC,IAAI,CAAC,GAAGkC,KAAK,CAACE,UAAU,CAACpC,IAAI,CAAC,GAAGA,IAAI;EACzE;;EAEA;EACA,IAAIkC,KAAK,CAACI,SAAS,EAAE;IACnB,OAAOJ,KAAK,CAACI,SAAS,CAACwC,kBAAkB,CAAC/B,MAAM,CAAC;EACnD;EAEAgC,WAAW,CAAC7C,KAAK,CAAC;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS6C,WAAWA,CAAC7C,KAAK,EAAEyG,KAAK,EAAE;EACjC,MAAMd,OAAO,GAAG,IAAInH,YAAY,CAC9B,qDAAqD,EACrD;IACEyB,SAAS,EAAED,KAAK,CAACC,SAAS;IAC1BwG,KAAK;IACLb,MAAM,EAAE,YAAY;IACpBC,MAAM,EAAE;EACV,CACF,CAAC;EACDF,OAAO,CAACG,IAAI,GAAG9F,KAAK,CAACR,QAAQ,IAAIF,SAAS;EAC1CqG,OAAO,CAACI,GAAG,GAAG9G,IAAI,GAAG,oDAAoD;EAEzE,MAAM0G,OAAO;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,0BAA0BA,CAACmB,SAAS,EAAE;EAC7C;EACA,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB;EACA,IAAIC,IAAI;EAER,KAAKA,IAAI,IAAIF,SAAS,EAAE;IACtB,IAAIjI,GAAG,CAACwF,IAAI,CAACyC,SAAS,EAAEE,IAAI,CAAC,EAAE;MAC7BD,SAAS,CAACE,yBAAyB,CAACD,IAAI,CAAC,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;IAC9D;EACF;EAEA,OAAOD,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,yBAAyBA,CAACD,IAAI,EAAE;EACvC,IAAIE,EAAE,GAAGF,IAAI,CAACG,OAAO,CAAClI,GAAG,EAAEmI,MAAM,CAAC;EAClC;EACA,IAAIF,EAAE,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,KAAK,EAAEH,EAAE,GAAG,GAAG,GAAGA,EAAE;EAC3C,OAAOA,EAAE;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAACE,EAAE,EAAE;EAClB,OAAO,GAAG,GAAGA,EAAE,CAACvF,WAAW,CAAC,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}