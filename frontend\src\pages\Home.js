import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { 
  BookOpenIcon, 
  TrendingUpIcon, 
  StarIcon,
  EyeIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

const Home = () => {
  const [featuredArticles, setFeaturedArticles] = useState([]);
  const [trendingArticles, setTrendingArticles] = useState([]);
  const [recentArticles, setRecentArticles] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchHomeData();
  }, []);

  const fetchHomeData = async () => {
    try {
      const [featured, trending, recent] = await Promise.all([
        axios.get('/articles/featured?limit=3'),
        axios.get('/articles/trending?limit=5'),
        axios.get('/articles?limit=5')
      ]);

      setFeaturedArticles(featured.data);
      setTrendingArticles(trending.data);
      setRecentArticles(recent.data);
    } catch (error) {
      console.error('Failed to fetch home data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="text-center py-12 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg">
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          Knowledge Platform
        </h1>
        <p className="text-xl md:text-2xl mb-8 opacity-90">
          Collaborate, Share, and Discover Knowledge
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link 
            to="/articles" 
            className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Browse Articles
          </Link>
          <Link 
            to="/articles/create" 
            className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
          >
            Create Article
          </Link>
        </div>
      </section>

      {/* Featured Articles */}
      {featuredArticles.length > 0 && (
        <section>
          <div className="flex items-center mb-6">
            <StarIcon className="h-6 w-6 text-yellow-500 mr-2" />
            <h2 className="text-2xl font-bold text-gray-900">Featured Articles</h2>
          </div>
          <div className="grid md:grid-cols-3 gap-6">
            {featuredArticles.map((article) => (
              <Link 
                key={article.id} 
                to={`/articles/${article.id}`}
                className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6"
              >
                <h3 className="text-xl font-semibold mb-2 text-gray-900 hover:text-blue-600">
                  {article.title}
                </h3>
                <p className="text-gray-600 mb-4 line-clamp-3">
                  {article.summary || article.content.substring(0, 150) + '...'}
                </p>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>By {article.author.full_name || article.author.username}</span>
                  <div className="flex items-center space-x-2">
                    <EyeIcon className="h-4 w-4" />
                    <span>{article.view_count}</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </section>
      )}

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Trending Articles */}
        <section>
          <div className="flex items-center mb-6">
            <TrendingUpIcon className="h-6 w-6 text-red-500 mr-2" />
            <h2 className="text-2xl font-bold text-gray-900">Trending</h2>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            {trendingArticles.length > 0 ? (
              <div className="space-y-4">
                {trendingArticles.map((article, index) => (
                  <Link 
                    key={article.id} 
                    to={`/articles/${article.id}`}
                    className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <span className="flex-shrink-0 w-6 h-6 bg-red-500 text-white text-sm font-bold rounded-full flex items-center justify-center">
                      {index + 1}
                    </span>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-gray-900 hover:text-blue-600 truncate">
                        {article.title}
                      </h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                        <div className="flex items-center space-x-1">
                          <EyeIcon className="h-4 w-4" />
                          <span>{article.view_count}</span>
                        </div>
                        <span>By {article.author.username}</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No trending articles yet</p>
            )}
          </div>
        </section>

        {/* Recent Articles */}
        <section>
          <div className="flex items-center mb-6">
            <ClockIcon className="h-6 w-6 text-green-500 mr-2" />
            <h2 className="text-2xl font-bold text-gray-900">Recently Updated</h2>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6">
            {recentArticles.length > 0 ? (
              <div className="space-y-4">
                {recentArticles.map((article) => (
                  <Link 
                    key={article.id} 
                    to={`/articles/${article.id}`}
                    className="block p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <h3 className="font-medium text-gray-900 hover:text-blue-600 mb-1">
                      {article.title}
                    </h3>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <span>By {article.author.username}</span>
                      <span>{formatDate(article.updated_at || article.created_at)}</span>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No recent articles</p>
            )}
          </div>
        </section>
      </div>

      {/* Call to Action */}
      <section className="text-center py-12 bg-gray-100 rounded-lg">
        <BookOpenIcon className="h-16 w-16 text-blue-600 mx-auto mb-4" />
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Start Contributing Today
        </h2>
        <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
          Join our community of knowledge contributors. Share your expertise, 
          learn from others, and help build a comprehensive knowledge base.
        </p>
        <Link 
          to="/register" 
          className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
        >
          Get Started
        </Link>
      </section>
    </div>
  );
};

export default Home;
