{"ast": null, "code": "// Note: types exposed from `index.d.ts`.\nexport { default } from './lib/index.js';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/remark-parse/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nexport {default} from './lib/index.js'\n"], "mappings": "AAAA;AACA,SAAQA,OAAO,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}