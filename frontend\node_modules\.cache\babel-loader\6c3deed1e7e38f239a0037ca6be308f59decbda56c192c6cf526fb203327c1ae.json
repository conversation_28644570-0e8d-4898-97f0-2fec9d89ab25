{"ast": null, "code": "export { codes } from './codes.js';\nexport { constants } from './constants.js';\nexport { types } from './types.js';\nexport { values } from './values.js';", "map": {"version": 3, "names": ["codes", "constants", "types", "values"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-util-symbol/lib/default.js"], "sourcesContent": ["export {codes} from './codes.js'\nexport {constants} from './constants.js'\nexport {types} from './types.js'\nexport {values} from './values.js'\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,YAAY;AAChC,SAAQC,SAAS,QAAO,gBAAgB;AACxC,SAAQC,KAAK,QAAO,YAAY;AAChC,SAAQC,MAAM,QAAO,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}