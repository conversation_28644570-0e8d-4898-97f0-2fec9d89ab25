import pytest
from app import crud, schemas

def test_create_article(client, test_user, auth_headers, db):
    """Test creating an article."""
    # First, update user role to editor
    test_user.role = "editor"
    db.commit()
    
    article_data = {
        "title": "Test Article",
        "content": "This is test content",
        "summary": "Test summary",
        "is_published": True,
        "tag_ids": []
    }
    response = client.post("/articles/", json=article_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == article_data["title"]
    assert data["content"] == article_data["content"]
    assert data["author_id"] == test_user.id

def test_create_article_unauthorized(client):
    """Test creating article without authentication."""
    article_data = {
        "title": "Test Article",
        "content": "This is test content"
    }
    response = client.post("/articles/", json=article_data)
    assert response.status_code == 401

def test_create_article_insufficient_permissions(client, test_user, auth_headers):
    """Test creating article with viewer role."""
    article_data = {
        "title": "Test Article",
        "content": "This is test content"
    }
    response = client.post("/articles/", json=article_data, headers=auth_headers)
    assert response.status_code == 403

def test_get_articles(client, test_user, auth_headers, db):
    """Test getting articles list."""
    # Create a test article
    test_user.role = "editor"
    db.commit()
    
    article_data = schemas.ArticleCreate(
        title="Test Article",
        content="Test content",
        is_published=True
    )
    crud.create_article(db, article_data, test_user.id)
    
    response = client.get("/articles/")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["title"] == "Test Article"

def test_get_article_by_id(client, test_user, auth_headers, db):
    """Test getting a specific article."""
    # Create a test article
    test_user.role = "editor"
    db.commit()
    
    article_data = schemas.ArticleCreate(
        title="Test Article",
        content="Test content",
        is_published=True
    )
    article = crud.create_article(db, article_data, test_user.id)
    
    response = client.get(f"/articles/{article.id}")
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "Test Article"
    assert data["id"] == article.id

def test_get_nonexistent_article(client):
    """Test getting a non-existent article."""
    response = client.get("/articles/999")
    assert response.status_code == 404

def test_update_article(client, test_user, auth_headers, db):
    """Test updating an article."""
    # Create a test article
    test_user.role = "editor"
    db.commit()
    
    article_data = schemas.ArticleCreate(
        title="Original Title",
        content="Original content",
        is_published=True
    )
    article = crud.create_article(db, article_data, test_user.id)
    
    update_data = {
        "title": "Updated Title",
        "content": "Updated content"
    }
    response = client.put(f"/articles/{article.id}", json=update_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == "Updated Title"
    assert data["content"] == "Updated content"

def test_update_article_unauthorized(client, test_user, db):
    """Test updating article without proper permissions."""
    # Create article with different user
    other_user_data = schemas.UserCreate(
        username="otheruser",
        email="<EMAIL>",
        password="password"
    )
    other_user = crud.create_user(db, other_user_data)
    other_user.role = "editor"
    db.commit()
    
    article_data = schemas.ArticleCreate(
        title="Other's Article",
        content="Content",
        is_published=True
    )
    article = crud.create_article(db, article_data, other_user.id)
    
    # Try to update with test_user (viewer role)
    login_data = {
        "username": test_user.username,
        "password": "testpassword"
    }
    response = client.post("/auth/login", json=login_data)
    token = response.json()["access_token"]
    headers = {"Authorization": f"Bearer {token}"}
    
    update_data = {"title": "Hacked Title"}
    response = client.put(f"/articles/{article.id}", json=update_data, headers=headers)
    assert response.status_code == 403

def test_delete_article(client, test_user, auth_headers, db):
    """Test deleting an article."""
    # Create a test article
    test_user.role = "editor"
    db.commit()
    
    article_data = schemas.ArticleCreate(
        title="To Delete",
        content="Content to delete",
        is_published=True
    )
    article = crud.create_article(db, article_data, test_user.id)
    
    response = client.delete(f"/articles/{article.id}", headers=auth_headers)
    assert response.status_code == 200
    
    # Verify article is deleted
    response = client.get(f"/articles/{article.id}")
    assert response.status_code == 404
