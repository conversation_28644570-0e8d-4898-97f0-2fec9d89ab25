{"ast": null, "code": "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end');\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start');\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'end' | 'start'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point;\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {Node | NodeLike | null | undefined} [node]\n   * @returns {Point | undefined}\n   */\n  function point(node) {\n    const point = node && node.position && node.position[type] || {};\n    if (typeof point.line === 'number' && point.line > 0 && typeof point.column === 'number' && point.column > 0) {\n      return {\n        line: point.line,\n        column: point.column,\n        offset: typeof point.offset === 'number' && point.offset > -1 ? point.offset : undefined\n      };\n    }\n  }\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {Node | NodeLike | null | undefined} [node]\n *   Node.\n * @returns {Position | undefined}\n *   Position.\n */\nexport function position(node) {\n  const start = pointStart(node);\n  const end = pointEnd(node);\n  if (start && end) {\n    return {\n      start,\n      end\n    };\n  }\n}", "map": {"version": 3, "names": ["pointEnd", "point", "pointStart", "type", "node", "position", "line", "column", "offset", "undefined", "start", "end"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/unist-util-position/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n */\n\n/**\n * Get the ending point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointEnd = point('end')\n\n/**\n * Get the starting point of `node`.\n *\n * @param node\n *   Node.\n * @returns\n *   Point.\n */\nexport const pointStart = point('start')\n\n/**\n * Get the positional info of `node`.\n *\n * @param {'end' | 'start'} type\n *   Side.\n * @returns\n *   Getter.\n */\nfunction point(type) {\n  return point\n\n  /**\n   * Get the point info of `node` at a bound side.\n   *\n   * @param {Node | NodeLike | null | undefined} [node]\n   * @returns {Point | undefined}\n   */\n  function point(node) {\n    const point = (node && node.position && node.position[type]) || {}\n\n    if (\n      typeof point.line === 'number' &&\n      point.line > 0 &&\n      typeof point.column === 'number' &&\n      point.column > 0\n    ) {\n      return {\n        line: point.line,\n        column: point.column,\n        offset:\n          typeof point.offset === 'number' && point.offset > -1\n            ? point.offset\n            : undefined\n      }\n    }\n  }\n}\n\n/**\n * Get the positional info of `node`.\n *\n * @param {Node | NodeLike | null | undefined} [node]\n *   Node.\n * @returns {Position | undefined}\n *   Position.\n */\nexport function position(node) {\n  const start = pointStart(node)\n  const end = pointEnd(node)\n\n  if (start && end) {\n    return {start, end}\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,QAAQ,GAAGC,KAAK,CAAC,KAAK,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAGD,KAAK,CAAC,OAAO,CAAC;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAKA,CAACE,IAAI,EAAE;EACnB,OAAOF,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACG,IAAI,EAAE;IACnB,MAAMH,KAAK,GAAIG,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACF,IAAI,CAAC,IAAK,CAAC,CAAC;IAElE,IACE,OAAOF,KAAK,CAACK,IAAI,KAAK,QAAQ,IAC9BL,KAAK,CAACK,IAAI,GAAG,CAAC,IACd,OAAOL,KAAK,CAACM,MAAM,KAAK,QAAQ,IAChCN,KAAK,CAACM,MAAM,GAAG,CAAC,EAChB;MACA,OAAO;QACLD,IAAI,EAAEL,KAAK,CAACK,IAAI;QAChBC,MAAM,EAAEN,KAAK,CAACM,MAAM;QACpBC,MAAM,EACJ,OAAOP,KAAK,CAACO,MAAM,KAAK,QAAQ,IAAIP,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC,GACjDP,KAAK,CAACO,MAAM,GACZC;MACR,CAAC;IACH;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASJ,QAAQA,CAACD,IAAI,EAAE;EAC7B,MAAMM,KAAK,GAAGR,UAAU,CAACE,IAAI,CAAC;EAC9B,MAAMO,GAAG,GAAGX,QAAQ,CAACI,IAAI,CAAC;EAE1B,IAAIM,KAAK,IAAIC,GAAG,EAAE;IAChB,OAAO;MAACD,KAAK;MAAEC;IAAG,CAAC;EACrB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}