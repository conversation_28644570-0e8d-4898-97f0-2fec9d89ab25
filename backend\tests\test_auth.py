import pytest
from fastapi.testclient import TestClient

def test_register_user(client):
    """Test user registration."""
    user_data = {
        "username": "newuser",
        "email": "<EMAIL>",
        "full_name": "New User",
        "password": "newpassword"
    }
    response = client.post("/auth/register", json=user_data)
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == user_data["username"]
    assert data["email"] == user_data["email"]
    assert data["full_name"] == user_data["full_name"]
    assert "id" in data

def test_register_duplicate_username(client, test_user):
    """Test registration with duplicate username."""
    user_data = {
        "username": test_user.username,
        "email": "<EMAIL>",
        "password": "password"
    }
    response = client.post("/auth/register", json=user_data)
    assert response.status_code == 400
    assert "Username already registered" in response.json()["detail"]

def test_login_success(client, test_user):
    """Test successful login."""
    login_data = {
        "username": test_user.username,
        "password": "testpassword"
    }
    response = client.post("/auth/login", json=login_data)
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert data["token_type"] == "bearer"

def test_login_invalid_credentials(client, test_user):
    """Test login with invalid credentials."""
    login_data = {
        "username": test_user.username,
        "password": "wrongpassword"
    }
    response = client.post("/auth/login", json=login_data)
    assert response.status_code == 401
    assert "Incorrect username or password" in response.json()["detail"]

def test_get_current_user(client, test_user, auth_headers):
    """Test getting current user information."""
    response = client.get("/auth/me", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == test_user.username
    assert data["email"] == test_user.email

def test_get_current_user_unauthorized(client):
    """Test getting current user without authentication."""
    response = client.get("/auth/me")
    assert response.status_code == 401

def test_update_user_profile(client, test_user, auth_headers):
    """Test updating user profile."""
    update_data = {
        "full_name": "Updated Name",
        "email": "<EMAIL>"
    }
    response = client.put("/auth/me", json=update_data, headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["full_name"] == update_data["full_name"]
    assert data["email"] == update_data["email"]
