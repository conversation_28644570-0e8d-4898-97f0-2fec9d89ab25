{"ast": null, "code": "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n// Note: overloads in JSDoc can’t yet use different `@template`s.\n/**\n * @type {(\n *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n *   ((node?: null | undefined) => false) &\n *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n * )}\n */\n\n/**\n * @param {unknown} [node]\n * @param {Test} [test]\n * @param {number | null | undefined} [index]\n * @param {Parent | null | undefined} [parent]\n * @param {unknown} [context]\n * @returns {boolean}\n */\n// eslint-disable-next-line max-params\nfunction (node, test, index, parent, context) {\n  const check = convert(test);\n  if (index !== undefined && index !== null && (typeof index !== 'number' || index < 0 || index === Number.POSITIVE_INFINITY)) {\n    throw new Error('Expected positive finite index');\n  }\n  if (parent !== undefined && parent !== null && (!is(parent) || !parent.children)) {\n    throw new Error('Expected parent node');\n  }\n  if ((parent === undefined || parent === null) !== (index === undefined || index === null)) {\n    throw new Error('Expected both parent and index');\n  }\n  return looksLikeANode(node) ? check.call(context, node, index, parent) : false;\n};\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n// Note: overloads in JSDoc can’t yet use different `@template`s.\n/**\n * @type {(\n *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n *   ((test?: Test) => Check)\n * )}\n */\n\n/**\n * @param {Test} [test]\n * @returns {Check}\n */\nfunction (test) {\n  if (test === null || test === undefined) {\n    return ok;\n  }\n  if (typeof test === 'function') {\n    return castFactory(test);\n  }\n  if (typeof test === 'object') {\n    return Array.isArray(test) ? anyFactory(test) : propsFactory(test);\n  }\n  if (typeof test === 'string') {\n    return typeFactory(test);\n  }\n  throw new Error('Expected function, string, or object as test');\n};\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = [];\n  let index = -1;\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index]);\n  }\n  return castFactory(any);\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1;\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true;\n    }\n    return false;\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */check;\n  return castFactory(all);\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */\n    /** @type {unknown} */node;\n\n    /** @type {string} */\n    let key;\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false;\n    }\n    return true;\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type);\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check;\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check;\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(looksLikeANode(value) && testFunction.call(this, value, typeof index === 'number' ? index : undefined, parent || undefined));\n  }\n}\nfunction ok() {\n  return true;\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value;\n}", "map": {"version": 3, "names": ["is", "node", "test", "index", "parent", "context", "check", "convert", "undefined", "Number", "POSITIVE_INFINITY", "Error", "children", "looksLikeANode", "call", "ok", "castFactory", "Array", "isArray", "anyFactory", "propsFactory", "typeFactory", "tests", "checks", "length", "any", "parameters", "apply", "checkAsRecord", "all", "nodeAsRecord", "key", "type", "testFunction", "value", "Boolean"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/unist-util-is/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n */\n\n/**\n * @template Fn\n * @template Fallback\n * @typedef {Fn extends (value: any) => value is infer Thing ? Thing : Fallback} Predicate\n */\n\n/**\n * @callback Check\n *   Check that an arbitrary value is a node.\n * @param {unknown} this\n *   The given context.\n * @param {unknown} [node]\n *   Anything (typically a node).\n * @param {number | null | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean}\n *   Whether this is a node and passes a test.\n *\n * @typedef {Record<string, unknown> | Node} Props\n *   Object to check for equivalence.\n *\n *   Note: `Node` is included as it is common but is not indexable.\n *\n * @typedef {Array<Props | TestFunction | string> | Props | TestFunction | string | null | undefined} Test\n *   Check for an arbitrary node.\n *\n * @callback TestFunction\n *   Check if a node passes a test.\n * @param {unknown} this\n *   The given context.\n * @param {Node} node\n *   A node.\n * @param {number | undefined} [index]\n *   The node’s position in its parent.\n * @param {Parent | undefined} [parent]\n *   The node’s parent.\n * @returns {boolean | undefined | void}\n *   Whether this node passes the test.\n *\n *   Note: `void` is included until TS sees no return as `undefined`.\n */\n\n/**\n * Check if `node` is a `Node` and whether it passes the given test.\n *\n * @param {unknown} node\n *   Thing to check, typically `Node`.\n * @param {Test} test\n *   A check for a specific node.\n * @param {number | null | undefined} index\n *   The node’s position in its parent.\n * @param {Parent | null | undefined} parent\n *   The node’s parent.\n * @param {unknown} context\n *   Context object (`this`) to pass to `test` functions.\n * @returns {boolean}\n *   Whether `node` is a node and passes a test.\n */\nexport const is =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(node: unknown, test: Condition, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((node?: null | undefined) => false) &\n   *   ((node: unknown, test?: null | undefined, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((node: unknown, test?: Test, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => boolean)\n   * )}\n   */\n  (\n    /**\n     * @param {unknown} [node]\n     * @param {Test} [test]\n     * @param {number | null | undefined} [index]\n     * @param {Parent | null | undefined} [parent]\n     * @param {unknown} [context]\n     * @returns {boolean}\n     */\n    // eslint-disable-next-line max-params\n    function (node, test, index, parent, context) {\n      const check = convert(test)\n\n      if (\n        index !== undefined &&\n        index !== null &&\n        (typeof index !== 'number' ||\n          index < 0 ||\n          index === Number.POSITIVE_INFINITY)\n      ) {\n        throw new Error('Expected positive finite index')\n      }\n\n      if (\n        parent !== undefined &&\n        parent !== null &&\n        (!is(parent) || !parent.children)\n      ) {\n        throw new Error('Expected parent node')\n      }\n\n      if (\n        (parent === undefined || parent === null) !==\n        (index === undefined || index === null)\n      ) {\n        throw new Error('Expected both parent and index')\n      }\n\n      return looksLikeANode(node)\n        ? check.call(context, node, index, parent)\n        : false\n    }\n  )\n\n/**\n * Generate an assertion from a test.\n *\n * Useful if you’re going to test many nodes, for example when creating a\n * utility where something else passes a compatible test.\n *\n * The created function is a bit faster because it expects valid input only:\n * a `node`, `index`, and `parent`.\n *\n * @param {Test} test\n *   *   when nullish, checks if `node` is a `Node`.\n *   *   when `string`, works like passing `(node) => node.type === test`.\n *   *   when `function` checks if function passed the node is true.\n *   *   when `object`, checks that all keys in test are in node, and that they have (strictly) equal values.\n *   *   when `array`, checks if any one of the subtests pass.\n * @returns {Check}\n *   An assertion.\n */\nexport const convert =\n  // Note: overloads in JSDoc can’t yet use different `@template`s.\n  /**\n   * @type {(\n   *   (<Condition extends string>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & {type: Condition}) &\n   *   (<Condition extends Props>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Condition) &\n   *   (<Condition extends TestFunction>(test: Condition) => (node: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node & Predicate<Condition, Node>) &\n   *   ((test?: null | undefined) => (node?: unknown, index?: number | null | undefined, parent?: Parent | null | undefined, context?: unknown) => node is Node) &\n   *   ((test?: Test) => Check)\n   * )}\n   */\n  (\n    /**\n     * @param {Test} [test]\n     * @returns {Check}\n     */\n    function (test) {\n      if (test === null || test === undefined) {\n        return ok\n      }\n\n      if (typeof test === 'function') {\n        return castFactory(test)\n      }\n\n      if (typeof test === 'object') {\n        return Array.isArray(test) ? anyFactory(test) : propsFactory(test)\n      }\n\n      if (typeof test === 'string') {\n        return typeFactory(test)\n      }\n\n      throw new Error('Expected function, string, or object as test')\n    }\n  )\n\n/**\n * @param {Array<Props | TestFunction | string>} tests\n * @returns {Check}\n */\nfunction anyFactory(tests) {\n  /** @type {Array<Check>} */\n  const checks = []\n  let index = -1\n\n  while (++index < tests.length) {\n    checks[index] = convert(tests[index])\n  }\n\n  return castFactory(any)\n\n  /**\n   * @this {unknown}\n   * @type {TestFunction}\n   */\n  function any(...parameters) {\n    let index = -1\n\n    while (++index < checks.length) {\n      if (checks[index].apply(this, parameters)) return true\n    }\n\n    return false\n  }\n}\n\n/**\n * Turn an object into a test for a node with a certain fields.\n *\n * @param {Props} check\n * @returns {Check}\n */\nfunction propsFactory(check) {\n  const checkAsRecord = /** @type {Record<string, unknown>} */ (check)\n\n  return castFactory(all)\n\n  /**\n   * @param {Node} node\n   * @returns {boolean}\n   */\n  function all(node) {\n    const nodeAsRecord = /** @type {Record<string, unknown>} */ (\n      /** @type {unknown} */ (node)\n    )\n\n    /** @type {string} */\n    let key\n\n    for (key in check) {\n      if (nodeAsRecord[key] !== checkAsRecord[key]) return false\n    }\n\n    return true\n  }\n}\n\n/**\n * Turn a string into a test for a node with a certain type.\n *\n * @param {string} check\n * @returns {Check}\n */\nfunction typeFactory(check) {\n  return castFactory(type)\n\n  /**\n   * @param {Node} node\n   */\n  function type(node) {\n    return node && node.type === check\n  }\n}\n\n/**\n * Turn a custom test into a test for a node that passes that test.\n *\n * @param {TestFunction} testFunction\n * @returns {Check}\n */\nfunction castFactory(testFunction) {\n  return check\n\n  /**\n   * @this {unknown}\n   * @type {Check}\n   */\n  function check(value, index, parent) {\n    return Boolean(\n      looksLikeANode(value) &&\n        testFunction.call(\n          this,\n          value,\n          typeof index === 'number' ? index : undefined,\n          parent || undefined\n        )\n    )\n  }\n}\n\nfunction ok() {\n  return true\n}\n\n/**\n * @param {unknown} value\n * @returns {value is Node}\n */\nfunction looksLikeANode(value) {\n  return value !== null && typeof value === 'object' && 'type' in value\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,EAAE;AACb;AACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACI;AACA,SAAAA,CAAUC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC5C,MAAMC,KAAK,GAAGC,OAAO,CAACL,IAAI,CAAC;EAE3B,IACEC,KAAK,KAAKK,SAAS,IACnBL,KAAK,KAAK,IAAI,KACb,OAAOA,KAAK,KAAK,QAAQ,IACxBA,KAAK,GAAG,CAAC,IACTA,KAAK,KAAKM,MAAM,CAACC,iBAAiB,CAAC,EACrC;IACA,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;EACnD;EAEA,IACEP,MAAM,KAAKI,SAAS,IACpBJ,MAAM,KAAK,IAAI,KACd,CAACJ,EAAE,CAACI,MAAM,CAAC,IAAI,CAACA,MAAM,CAACQ,QAAQ,CAAC,EACjC;IACA,MAAM,IAAID,KAAK,CAAC,sBAAsB,CAAC;EACzC;EAEA,IACE,CAACP,MAAM,KAAKI,SAAS,IAAIJ,MAAM,KAAK,IAAI,OACvCD,KAAK,KAAKK,SAAS,IAAIL,KAAK,KAAK,IAAI,CAAC,EACvC;IACA,MAAM,IAAIQ,KAAK,CAAC,gCAAgC,CAAC;EACnD;EAEA,OAAOE,cAAc,CAACZ,IAAI,CAAC,GACvBK,KAAK,CAACQ,IAAI,CAACT,OAAO,EAAEJ,IAAI,EAAEE,KAAK,EAAEC,MAAM,CAAC,GACxC,KAAK;AACX,CACD;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,OAAO;AAClB;AACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEI;AACJ;AACA;AACA;AACI,SAAAA,CAAUL,IAAI,EAAE;EACd,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKM,SAAS,EAAE;IACvC,OAAOO,EAAE;EACX;EAEA,IAAI,OAAOb,IAAI,KAAK,UAAU,EAAE;IAC9B,OAAOc,WAAW,CAACd,IAAI,CAAC;EAC1B;EAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOe,KAAK,CAACC,OAAO,CAAChB,IAAI,CAAC,GAAGiB,UAAU,CAACjB,IAAI,CAAC,GAAGkB,YAAY,CAAClB,IAAI,CAAC;EACpE;EAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOmB,WAAW,CAACnB,IAAI,CAAC;EAC1B;EAEA,MAAM,IAAIS,KAAK,CAAC,8CAA8C,CAAC;AACjE,CACD;;AAEH;AACA;AACA;AACA;AACA,SAASQ,UAAUA,CAACG,KAAK,EAAE;EACzB;EACA,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIpB,KAAK,GAAG,CAAC,CAAC;EAEd,OAAO,EAAEA,KAAK,GAAGmB,KAAK,CAACE,MAAM,EAAE;IAC7BD,MAAM,CAACpB,KAAK,CAAC,GAAGI,OAAO,CAACe,KAAK,CAACnB,KAAK,CAAC,CAAC;EACvC;EAEA,OAAOa,WAAW,CAACS,GAAG,CAAC;;EAEvB;AACF;AACA;AACA;EACE,SAASA,GAAGA,CAAC,GAAGC,UAAU,EAAE;IAC1B,IAAIvB,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,EAAEA,KAAK,GAAGoB,MAAM,CAACC,MAAM,EAAE;MAC9B,IAAID,MAAM,CAACpB,KAAK,CAAC,CAACwB,KAAK,CAAC,IAAI,EAAED,UAAU,CAAC,EAAE,OAAO,IAAI;IACxD;IAEA,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,YAAYA,CAACd,KAAK,EAAE;EAC3B,MAAMsB,aAAa,GAAG,sCAAwCtB,KAAM;EAEpE,OAAOU,WAAW,CAACa,GAAG,CAAC;;EAEvB;AACF;AACA;AACA;EACE,SAASA,GAAGA,CAAC5B,IAAI,EAAE;IACjB,MAAM6B,YAAY,GAAG;IACnB,sBAAwB7B,IACzB;;IAED;IACA,IAAI8B,GAAG;IAEP,KAAKA,GAAG,IAAIzB,KAAK,EAAE;MACjB,IAAIwB,YAAY,CAACC,GAAG,CAAC,KAAKH,aAAa,CAACG,GAAG,CAAC,EAAE,OAAO,KAAK;IAC5D;IAEA,OAAO,IAAI;EACb;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASV,WAAWA,CAACf,KAAK,EAAE;EAC1B,OAAOU,WAAW,CAACgB,IAAI,CAAC;;EAExB;AACF;AACA;EACE,SAASA,IAAIA,CAAC/B,IAAI,EAAE;IAClB,OAAOA,IAAI,IAAIA,IAAI,CAAC+B,IAAI,KAAK1B,KAAK;EACpC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,WAAWA,CAACiB,YAAY,EAAE;EACjC,OAAO3B,KAAK;;EAEZ;AACF;AACA;AACA;EACE,SAASA,KAAKA,CAAC4B,KAAK,EAAE/B,KAAK,EAAEC,MAAM,EAAE;IACnC,OAAO+B,OAAO,CACZtB,cAAc,CAACqB,KAAK,CAAC,IACnBD,YAAY,CAACnB,IAAI,CACf,IAAI,EACJoB,KAAK,EACL,OAAO/B,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGK,SAAS,EAC7CJ,MAAM,IAAII,SACZ,CACJ,CAAC;EACH;AACF;AAEA,SAASO,EAAEA,CAAA,EAAG;EACZ,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA,SAASF,cAAcA,CAACqB,KAAK,EAAE;EAC7B,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAIA,KAAK;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}