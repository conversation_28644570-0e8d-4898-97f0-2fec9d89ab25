{"ast": null, "code": "/**\n * Get the cleaned case insensitive form of an attribute or property.\n *\n * @param {string} value\n *   An attribute-like or property-like name.\n * @returns {string}\n *   Value that can be used to look up the properly cased property on a\n *   `Schema`.\n */\nexport function normalize(value) {\n  return value.toLowerCase();\n}", "map": {"version": 3, "names": ["normalize", "value", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/property-information/lib/normalize.js"], "sourcesContent": ["/**\n * Get the cleaned case insensitive form of an attribute or property.\n *\n * @param {string} value\n *   An attribute-like or property-like name.\n * @returns {string}\n *   Value that can be used to look up the properly cased property on a\n *   `Schema`.\n */\nexport function normalize(value) {\n  return value.toLowerCase()\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAOA,KAAK,CAACC,WAAW,CAAC,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}