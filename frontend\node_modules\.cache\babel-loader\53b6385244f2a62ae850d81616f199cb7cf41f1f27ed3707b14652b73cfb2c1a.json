{"ast": null, "code": "/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n// HTML whitespace expression.\n// See <https://infra.spec.whatwg.org/#ascii-whitespace>.\nconst re = /[ \\t\\n\\f\\r]/g;\n\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {Nodes | string} thing\n *   Thing to check (`Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`); if a node is passed it must be a `Text` node,\n *   whose `value` field is checked.\n */\nexport function whitespace(thing) {\n  return typeof thing === 'object' ? thing.type === 'text' ? empty(thing.value) : false : empty(thing);\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return value.replace(re, '') === '';\n}", "map": {"version": 3, "names": ["re", "whitespace", "thing", "type", "empty", "value", "replace"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/hast-util-whitespace/lib/index.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n// HTML whitespace expression.\n// See <https://infra.spec.whatwg.org/#ascii-whitespace>.\nconst re = /[ \\t\\n\\f\\r]/g\n\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {Nodes | string} thing\n *   Thing to check (`Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`); if a node is passed it must be a `Text` node,\n *   whose `value` field is checked.\n */\nexport function whitespace(thing) {\n  return typeof thing === 'object'\n    ? thing.type === 'text'\n      ? empty(thing.value)\n      : false\n    : empty(thing)\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return value.replace(re, '') === ''\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA,MAAMA,EAAE,GAAG,cAAc;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAC5BA,KAAK,CAACC,IAAI,KAAK,MAAM,GACnBC,KAAK,CAACF,KAAK,CAACG,KAAK,CAAC,GAClB,KAAK,GACPD,KAAK,CAACF,KAAK,CAAC;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,KAAKA,CAACC,KAAK,EAAE;EACpB,OAAOA,KAAK,CAACC,OAAO,CAACN,EAAE,EAAE,EAAE,CAAC,KAAK,EAAE;AACrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}