from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, desc
from typing import List, Optional
from . import models, schemas
from .auth import get_password_hash

# User CRUD operations
def create_user(db: Session, user: schemas.UserCreate) -> models.User:
    """Create a new user."""
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        hashed_password=hashed_password,
        role="viewer"  # Default role
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def get_user(db: Session, user_id: int) -> Optional[models.User]:
    """Get a user by ID."""
    return db.query(models.User).filter(models.User.id == user_id).first()

def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[models.User]:
    """Get all users with pagination."""
    return db.query(models.User).offset(skip).limit(limit).all()

def update_user(db: Session, user_id: int, user_update: schemas.UserUpdate) -> Optional[models.User]:
    """Update a user."""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    update_data = user_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

# Category CRUD operations
def create_category(db: Session, category: schemas.CategoryCreate) -> models.Category:
    """Create a new category."""
    db_category = models.Category(**category.model_dump())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category

def get_categories(db: Session) -> List[models.Category]:
    """Get all categories."""
    return db.query(models.Category).all()

def get_category(db: Session, category_id: int) -> Optional[models.Category]:
    """Get a category by ID."""
    return db.query(models.Category).filter(models.Category.id == category_id).first()

# Tag CRUD operations
def create_tag(db: Session, tag: schemas.TagCreate) -> models.Tag:
    """Create a new tag."""
    db_tag = models.Tag(**tag.model_dump())
    db.add(db_tag)
    db.commit()
    db.refresh(db_tag)
    return db_tag

def get_tags(db: Session) -> List[models.Tag]:
    """Get all tags."""
    return db.query(models.Tag).all()

def get_tag(db: Session, tag_id: int) -> Optional[models.Tag]:
    """Get a tag by ID."""
    return db.query(models.Tag).filter(models.Tag.id == tag_id).first()

# Article CRUD operations
def create_article(db: Session, article: schemas.ArticleCreate, author_id: int) -> models.Article:
    """Create a new article."""
    # Create article without tags first
    article_data = article.model_dump(exclude={'tag_ids'})
    db_article = models.Article(**article_data, author_id=author_id)
    db.add(db_article)
    db.commit()
    db.refresh(db_article)
    
    # Add tags if provided
    if article.tag_ids:
        tags = db.query(models.Tag).filter(models.Tag.id.in_(article.tag_ids)).all()
        db_article.tags = tags
        db.commit()
        db.refresh(db_article)
    
    # Create initial version
    create_article_version(db, db_article.id, author_id)
    
    return db_article

def get_articles(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    category_id: Optional[int] = None,
    tag_ids: Optional[List[int]] = None,
    search: Optional[str] = None,
    published_only: bool = True
) -> List[models.Article]:
    """Get articles with filtering and pagination."""
    query = db.query(models.Article)
    
    if published_only:
        query = query.filter(models.Article.is_published == True)
    
    if category_id:
        query = query.filter(models.Article.category_id == category_id)
    
    if tag_ids:
        query = query.join(models.Article.tags).filter(models.Tag.id.in_(tag_ids))
    
    if search:
        query = query.filter(
            or_(
                models.Article.title.contains(search),
                models.Article.content.contains(search),
                models.Article.summary.contains(search)
            )
        )
    
    return query.order_by(desc(models.Article.created_at)).offset(skip).limit(limit).all()

def get_article(db: Session, article_id: int) -> Optional[models.Article]:
    """Get an article by ID."""
    return db.query(models.Article).filter(models.Article.id == article_id).first()

def update_article(
    db: Session, 
    article_id: int, 
    article_update: schemas.ArticleUpdate,
    user_id: int
) -> Optional[models.Article]:
    """Update an article."""
    db_article = get_article(db, article_id)
    if not db_article:
        return None
    
    # Create version before updating
    create_article_version(db, article_id, user_id)
    
    update_data = article_update.model_dump(exclude_unset=True, exclude={'tag_ids'})
    for field, value in update_data.items():
        setattr(db_article, field, value)
    
    # Update tags if provided
    if article_update.tag_ids is not None:
        tags = db.query(models.Tag).filter(models.Tag.id.in_(article_update.tag_ids)).all()
        db_article.tags = tags
    
    db.commit()
    db.refresh(db_article)
    return db_article

def increment_article_views(db: Session, article_id: int):
    """Increment article view count."""
    db_article = get_article(db, article_id)
    if db_article:
        db_article.view_count += 1
        db.commit()

# Article Version CRUD operations
def create_article_version(db: Session, article_id: int, user_id: int) -> models.ArticleVersion:
    """Create a new article version."""
    article = get_article(db, article_id)
    if not article:
        return None
    
    # Get the next version number
    last_version = db.query(models.ArticleVersion).filter(
        models.ArticleVersion.article_id == article_id
    ).order_by(desc(models.ArticleVersion.version_number)).first()
    
    version_number = (last_version.version_number + 1) if last_version else 1
    
    db_version = models.ArticleVersion(
        article_id=article_id,
        title=article.title,
        content=article.content,
        version_number=version_number,
        created_by=user_id
    )
    db.add(db_version)
    db.commit()
    db.refresh(db_version)
    return db_version

def get_article_versions(db: Session, article_id: int) -> List[models.ArticleVersion]:
    """Get all versions of an article."""
    return db.query(models.ArticleVersion).filter(
        models.ArticleVersion.article_id == article_id
    ).order_by(desc(models.ArticleVersion.version_number)).all()

# Comment CRUD operations
def create_comment(db: Session, comment: schemas.CommentCreate, author_id: int) -> models.Comment:
    """Create a new comment."""
    db_comment = models.Comment(**comment.model_dump(), author_id=author_id)
    db.add(db_comment)
    db.commit()
    db.refresh(db_comment)
    return db_comment

def get_article_comments(db: Session, article_id: int) -> List[models.Comment]:
    """Get all comments for an article."""
    return db.query(models.Comment).filter(
        models.Comment.article_id == article_id,
        models.Comment.parent_id == None  # Only top-level comments
    ).order_by(models.Comment.created_at).all()
