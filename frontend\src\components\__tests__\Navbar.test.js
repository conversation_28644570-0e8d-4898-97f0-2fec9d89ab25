import { render, screen } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import Navbar from '../Navbar';

// Mock the AuthContext
const mockAuthContext = {
  user: null,
  logout: jest.fn(),
  loading: false
};

jest.mock('../../contexts/AuthContext', () => ({
  useAuth: () => mockAuthContext
}));

const renderNavbar = () => {
  return render(
    <BrowserRouter>
      <Navbar />
    </BrowserRouter>
  );
};

describe('Navbar', () => {
  test('renders knowledge platform title', () => {
    renderNavbar();
    expect(screen.getByText('Knowledge Platform')).toBeInTheDocument();
  });

  test('shows login and register links when not authenticated', () => {
    renderNavbar();
    expect(screen.getByText('Login')).toBeInTheDocument();
    expect(screen.getByText('Register')).toBeInTheDocument();
  });

  test('shows user menu when authenticated', () => {
    mockAuthContext.user = {
      id: 1,
      username: 'testuser',
      role: 'editor'
    };
    
    renderNavbar();
    expect(screen.getByText('testuser')).toBeInTheDocument();
    expect(screen.getByText('Create')).toBeInTheDocument();
  });

  test('hides create button for viewers', () => {
    mockAuthContext.user = {
      id: 1,
      username: 'viewer',
      role: 'viewer'
    };
    
    renderNavbar();
    expect(screen.queryByText('Create')).not.toBeInTheDocument();
  });
});
