{"ast": null, "code": "/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { factorySpace } from 'micromark-factory-space';\nimport { markdownLineEnding, markdownSpace } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  let size = 0;\n  /** @type {NonNullable<Code>} */\n  let marker;\n  return start;\n\n  /**\n   * Start of thematic break.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    effects.enter(types.thematicBreak);\n    // To do: parse indent like `markdown-rs`.\n    return before(code);\n  }\n\n  /**\n   * After optional whitespace, at marker.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.asterisk || code === codes.dash || code === codes.underscore, 'expected `*`, `-`, or `_`');\n    marker = code;\n    return atBreak(code);\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter(types.thematicBreakSequence);\n      return sequence(code);\n    }\n    if (size >= constants.thematicBreakMarkerCountMin && (code === codes.eof || markdownLineEnding(code))) {\n      effects.exit(types.thematicBreak);\n      return ok(code);\n    }\n    return nok(code);\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code);\n      size++;\n      return sequence;\n    }\n    effects.exit(types.thematicBreakSequence);\n    return markdownSpace(code) ? factorySpace(effects, atBreak, types.whitespace)(code) : atBreak(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "factorySpace", "markdownLineEnding", "markdownSpace", "codes", "constants", "types", "thematicBreak", "name", "tokenize", "tokenizeThematicBreak", "effects", "nok", "size", "marker", "start", "code", "enter", "before", "asterisk", "dash", "underscore", "atBreak", "thematicBreakSequence", "sequence", "thematicBreakMarkerCountMin", "eof", "exit", "consume", "whitespace"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-core-commonmark/dev/lib/thematic-break.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  let size = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of thematic break.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    effects.enter(types.thematicBreak)\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at marker.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(\n      code === codes.asterisk ||\n        code === codes.dash ||\n        code === codes.underscore,\n      'expected `*`, `-`, or `_`'\n    )\n    marker = code\n    return atBreak(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter(types.thematicBreakSequence)\n      return sequence(code)\n    }\n\n    if (\n      size >= constants.thematicBreakMarkerCountMin &&\n      (code === codes.eof || markdownLineEnding(code))\n    ) {\n      effects.exit(types.thematicBreak)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code)\n      size++\n      return sequence\n    }\n\n    effects.exit(types.thematicBreakSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, atBreak, types.whitespace)(code)\n      : atBreak(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,YAAY,QAAO,yBAAyB;AACpD,SAAQC,kBAAkB,EAAEC,aAAa,QAAO,0BAA0B;AAC1E,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,IAAI,EAAE,eAAe;EACrBC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASA,qBAAqBA,CAACC,OAAO,EAAEZ,EAAE,EAAEa,GAAG,EAAE;EAC/C,IAAIC,IAAI,GAAG,CAAC;EACZ;EACA,IAAIC,MAAM;EAEV,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBL,OAAO,CAACM,KAAK,CAACX,KAAK,CAACC,aAAa,CAAC;IAClC;IACA,OAAOW,MAAM,CAACF,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,MAAMA,CAACF,IAAI,EAAE;IACpBhB,MAAM,CACJgB,IAAI,KAAKZ,KAAK,CAACe,QAAQ,IACrBH,IAAI,KAAKZ,KAAK,CAACgB,IAAI,IACnBJ,IAAI,KAAKZ,KAAK,CAACiB,UAAU,EAC3B,2BACF,CAAC;IACDP,MAAM,GAAGE,IAAI;IACb,OAAOM,OAAO,CAACN,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASM,OAAOA,CAACN,IAAI,EAAE;IACrB,IAAIA,IAAI,KAAKF,MAAM,EAAE;MACnBH,OAAO,CAACM,KAAK,CAACX,KAAK,CAACiB,qBAAqB,CAAC;MAC1C,OAAOC,QAAQ,CAACR,IAAI,CAAC;IACvB;IAEA,IACEH,IAAI,IAAIR,SAAS,CAACoB,2BAA2B,KAC5CT,IAAI,KAAKZ,KAAK,CAACsB,GAAG,IAAIxB,kBAAkB,CAACc,IAAI,CAAC,CAAC,EAChD;MACAL,OAAO,CAACgB,IAAI,CAACrB,KAAK,CAACC,aAAa,CAAC;MACjC,OAAOR,EAAE,CAACiB,IAAI,CAAC;IACjB;IAEA,OAAOJ,GAAG,CAACI,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASQ,QAAQA,CAACR,IAAI,EAAE;IACtB,IAAIA,IAAI,KAAKF,MAAM,EAAE;MACnBH,OAAO,CAACiB,OAAO,CAACZ,IAAI,CAAC;MACrBH,IAAI,EAAE;MACN,OAAOW,QAAQ;IACjB;IAEAb,OAAO,CAACgB,IAAI,CAACrB,KAAK,CAACiB,qBAAqB,CAAC;IACzC,OAAOpB,aAAa,CAACa,IAAI,CAAC,GACtBf,YAAY,CAACU,OAAO,EAAEW,OAAO,EAAEhB,KAAK,CAACuB,UAAU,CAAC,CAACb,IAAI,CAAC,GACtDM,OAAO,CAACN,IAAI,CAAC;EACnB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}