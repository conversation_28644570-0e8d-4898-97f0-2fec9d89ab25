{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { codes, types } from 'micromark-util-symbol';\nimport { labelEnd } from './label-end.js';\n\n/** @type {Construct} */\nexport const labelStartLink = {\n  name: 'labelStartLink',\n  resolveAll: labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartLink\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  const self = this;\n  return start;\n\n  /**\n   * Start of label (link) start.\n   *\n   * ```markdown\n   * > | a [b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`');\n    effects.enter(types.labelLink);\n    effects.enter(types.labelMarker);\n    effects.consume(code);\n    effects.exit(types.labelMarker);\n    effects.exit(types.labelLink);\n    return after;\n  }\n\n  /** @type {State} */\n  function after(code) {\n    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,\n    // remove.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret && '_hiddenFootnoteSupport' in self.parser.constructs ? nok(code) : ok(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "codes", "types", "labelEnd", "labelStartLink", "name", "resolveAll", "tokenize", "tokenizeLabelStartLink", "effects", "nok", "self", "start", "code", "leftSquareBracket", "enter", "labelLink", "labelMarker", "consume", "exit", "after", "caret", "parser", "constructs"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-core-commonmark/dev/lib/label-start-link.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, types} from 'micromark-util-symbol'\nimport {labelEnd} from './label-end.js'\n\n/** @type {Construct} */\nexport const labelStartLink = {\n  name: 'labelStartLink',\n  resolveAll: labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartLink\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (link) start.\n   *\n   * ```markdown\n   * > | a [b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter(types.labelLink)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelLink)\n    return after\n  }\n\n  /** @type {State} */\n  function after(code) {\n    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,\n    // remove.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,KAAK,EAAEC,KAAK,QAAO,uBAAuB;AAClD,SAAQC,QAAQ,QAAO,gBAAgB;;AAEvC;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAEH,QAAQ,CAACG,UAAU;EAC/BC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASA,sBAAsBA,CAACC,OAAO,EAAEV,EAAE,EAAEW,GAAG,EAAE;EAChD,MAAMC,IAAI,GAAG,IAAI;EAEjB,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBb,MAAM,CAACa,IAAI,KAAKZ,KAAK,CAACa,iBAAiB,EAAE,cAAc,CAAC;IACxDL,OAAO,CAACM,KAAK,CAACb,KAAK,CAACc,SAAS,CAAC;IAC9BP,OAAO,CAACM,KAAK,CAACb,KAAK,CAACe,WAAW,CAAC;IAChCR,OAAO,CAACS,OAAO,CAACL,IAAI,CAAC;IACrBJ,OAAO,CAACU,IAAI,CAACjB,KAAK,CAACe,WAAW,CAAC;IAC/BR,OAAO,CAACU,IAAI,CAACjB,KAAK,CAACc,SAAS,CAAC;IAC7B,OAAOI,KAAK;EACd;;EAEA;EACA,SAASA,KAAKA,CAACP,IAAI,EAAE;IACnB;IACA;IACA;IACA;IACA,OAAOA,IAAI,KAAKZ,KAAK,CAACoB,KAAK,IACzB,wBAAwB,IAAIV,IAAI,CAACW,MAAM,CAACC,UAAU,GAChDb,GAAG,CAACG,IAAI,CAAC,GACTd,EAAE,CAACc,IAAI,CAAC;EACd;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}