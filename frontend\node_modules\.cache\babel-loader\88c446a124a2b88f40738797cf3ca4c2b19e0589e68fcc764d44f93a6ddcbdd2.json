{"ast": null, "code": "/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nexport const codes = /** @type {const} */{\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9,\n  // `\\t`\n  lf: 10,\n  // `\\n`\n  vt: 11,\n  // `\\v`\n  ff: 12,\n  // `\\f`\n  cr: 13,\n  // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33,\n  // `!`\n  quotationMark: 34,\n  // `\"`\n  numberSign: 35,\n  // `#`\n  dollarSign: 36,\n  // `$`\n  percentSign: 37,\n  // `%`\n  ampersand: 38,\n  // `&`\n  apostrophe: 39,\n  // `'`\n  leftParenthesis: 40,\n  // `(`\n  rightParenthesis: 41,\n  // `)`\n  asterisk: 42,\n  // `*`\n  plusSign: 43,\n  // `+`\n  comma: 44,\n  // `,`\n  dash: 45,\n  // `-`\n  dot: 46,\n  // `.`\n  slash: 47,\n  // `/`\n  digit0: 48,\n  // `0`\n  digit1: 49,\n  // `1`\n  digit2: 50,\n  // `2`\n  digit3: 51,\n  // `3`\n  digit4: 52,\n  // `4`\n  digit5: 53,\n  // `5`\n  digit6: 54,\n  // `6`\n  digit7: 55,\n  // `7`\n  digit8: 56,\n  // `8`\n  digit9: 57,\n  // `9`\n  colon: 58,\n  // `:`\n  semicolon: 59,\n  // `;`\n  lessThan: 60,\n  // `<`\n  equalsTo: 61,\n  // `=`\n  greaterThan: 62,\n  // `>`\n  questionMark: 63,\n  // `?`\n  atSign: 64,\n  // `@`\n  uppercaseA: 65,\n  // `A`\n  uppercaseB: 66,\n  // `B`\n  uppercaseC: 67,\n  // `C`\n  uppercaseD: 68,\n  // `D`\n  uppercaseE: 69,\n  // `E`\n  uppercaseF: 70,\n  // `F`\n  uppercaseG: 71,\n  // `G`\n  uppercaseH: 72,\n  // `H`\n  uppercaseI: 73,\n  // `I`\n  uppercaseJ: 74,\n  // `J`\n  uppercaseK: 75,\n  // `K`\n  uppercaseL: 76,\n  // `L`\n  uppercaseM: 77,\n  // `M`\n  uppercaseN: 78,\n  // `N`\n  uppercaseO: 79,\n  // `O`\n  uppercaseP: 80,\n  // `P`\n  uppercaseQ: 81,\n  // `Q`\n  uppercaseR: 82,\n  // `R`\n  uppercaseS: 83,\n  // `S`\n  uppercaseT: 84,\n  // `T`\n  uppercaseU: 85,\n  // `U`\n  uppercaseV: 86,\n  // `V`\n  uppercaseW: 87,\n  // `W`\n  uppercaseX: 88,\n  // `X`\n  uppercaseY: 89,\n  // `Y`\n  uppercaseZ: 90,\n  // `Z`\n  leftSquareBracket: 91,\n  // `[`\n  backslash: 92,\n  // `\\`\n  rightSquareBracket: 93,\n  // `]`\n  caret: 94,\n  // `^`\n  underscore: 95,\n  // `_`\n  graveAccent: 96,\n  // `` ` ``\n  lowercaseA: 97,\n  // `a`\n  lowercaseB: 98,\n  // `b`\n  lowercaseC: 99,\n  // `c`\n  lowercaseD: 100,\n  // `d`\n  lowercaseE: 101,\n  // `e`\n  lowercaseF: 102,\n  // `f`\n  lowercaseG: 103,\n  // `g`\n  lowercaseH: 104,\n  // `h`\n  lowercaseI: 105,\n  // `i`\n  lowercaseJ: 106,\n  // `j`\n  lowercaseK: 107,\n  // `k`\n  lowercaseL: 108,\n  // `l`\n  lowercaseM: 109,\n  // `m`\n  lowercaseN: 110,\n  // `n`\n  lowercaseO: 111,\n  // `o`\n  lowercaseP: 112,\n  // `p`\n  lowercaseQ: 113,\n  // `q`\n  lowercaseR: 114,\n  // `r`\n  lowercaseS: 115,\n  // `s`\n  lowercaseT: 116,\n  // `t`\n  lowercaseU: 117,\n  // `u`\n  lowercaseV: 118,\n  // `v`\n  lowercaseW: 119,\n  // `w`\n  lowercaseX: 120,\n  // `x`\n  lowercaseY: 121,\n  // `y`\n  lowercaseZ: 122,\n  // `z`\n  leftCurlyBrace: 123,\n  // `{`\n  verticalBar: 124,\n  // `|`\n  rightCurlyBrace: 125,\n  // `}`\n  tilde: 126,\n  // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n};", "map": {"version": 3, "names": ["codes", "carriageReturn", "lineFeed", "carriageReturnLineFeed", "horizontalTab", "virtualSpace", "eof", "nul", "soh", "stx", "etx", "eot", "enq", "ack", "bel", "bs", "ht", "lf", "vt", "ff", "cr", "so", "si", "dle", "dc1", "dc2", "dc3", "dc4", "nak", "syn", "etb", "can", "em", "sub", "esc", "fs", "gs", "rs", "us", "space", "exclamationMark", "quotationMark", "numberSign", "dollarSign", "percentSign", "ampersand", "apostrophe", "leftParenthesis", "rightParenthesis", "asterisk", "plusSign", "comma", "dash", "dot", "slash", "digit0", "digit1", "digit2", "digit3", "digit4", "digit5", "digit6", "digit7", "digit8", "digit9", "colon", "semicolon", "lessThan", "equalsTo", "greaterThan", "questionMark", "atSign", "uppercaseA", "uppercaseB", "uppercaseC", "uppercaseD", "uppercaseE", "uppercaseF", "uppercaseG", "uppercaseH", "uppercaseI", "uppercaseJ", "uppercaseK", "uppercaseL", "uppercaseM", "uppercaseN", "uppercaseO", "uppercaseP", "uppercaseQ", "uppercaseR", "uppercaseS", "uppercaseT", "uppercaseU", "uppercaseV", "uppercaseW", "uppercaseX", "uppercaseY", "uppercaseZ", "leftSquareBracket", "backslash", "rightSquareBracket", "caret", "underscore", "graveAccent", "lowercaseA", "lowercaseB", "lowercaseC", "lowercaseD", "lowercaseE", "lowercaseF", "lowercaseG", "lowercaseH", "lowercaseI", "lowercaseJ", "lowercaseK", "lowercaseL", "lowercaseM", "lowercaseN", "lowercaseO", "lowercaseP", "lowercaseQ", "lowercaseR", "lowercaseS", "lowercaseT", "lowercaseU", "lowercaseV", "lowercaseW", "lowercaseX", "lowercaseY", "lowercaseZ", "leftCurlyBrace", "verticalBar", "right<PERSON>urly<PERSON><PERSON>", "tilde", "del", "byteOrderMarker", "replacementCharacter"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-util-symbol/lib/codes.js"], "sourcesContent": ["/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nexport const codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,KAAK,GAAG,oBAAsB;EACzCC,cAAc,EAAE,CAAC,CAAC;EAClBC,QAAQ,EAAE,CAAC,CAAC;EACZC,sBAAsB,EAAE,CAAC,CAAC;EAC1BC,aAAa,EAAE,CAAC,CAAC;EACjBC,YAAY,EAAE,CAAC,CAAC;EAChBC,GAAG,EAAE,IAAI;EACTC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,GAAG,EAAE,CAAC;EACNC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,CAAC;EAAE;EACPC,EAAE,EAAE,EAAE;EAAE;EACRC,EAAE,EAAE,EAAE;EAAE;EACRC,EAAE,EAAE,EAAE;EAAE;EACRC,EAAE,EAAE,EAAE;EAAE;EACRC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,EAAE,EAAE,EAAE;EACNC,GAAG,EAAE,EAAE;EACPC,GAAG,EAAE,EAAE;EACPC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNC,EAAE,EAAE,EAAE;EACNC,KAAK,EAAE,EAAE;EACTC,eAAe,EAAE,EAAE;EAAE;EACrBC,aAAa,EAAE,EAAE;EAAE;EACnBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,WAAW,EAAE,EAAE;EAAE;EACjBC,SAAS,EAAE,EAAE;EAAE;EACfC,UAAU,EAAE,EAAE;EAAE;EAChBC,eAAe,EAAE,EAAE;EAAE;EACrBC,gBAAgB,EAAE,EAAE;EAAE;EACtBC,QAAQ,EAAE,EAAE;EAAE;EACdC,QAAQ,EAAE,EAAE;EAAE;EACdC,KAAK,EAAE,EAAE;EAAE;EACXC,IAAI,EAAE,EAAE;EAAE;EACVC,GAAG,EAAE,EAAE;EAAE;EACTC,KAAK,EAAE,EAAE;EAAE;EACXC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,MAAM,EAAE,EAAE;EAAE;EACZC,KAAK,EAAE,EAAE;EAAE;EACXC,SAAS,EAAE,EAAE;EAAE;EACfC,QAAQ,EAAE,EAAE;EAAE;EACdC,QAAQ,EAAE,EAAE;EAAE;EACdC,WAAW,EAAE,EAAE;EAAE;EACjBC,YAAY,EAAE,EAAE;EAAE;EAClBC,MAAM,EAAE,EAAE;EAAE;EACZC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,iBAAiB,EAAE,EAAE;EAAE;EACvBC,SAAS,EAAE,EAAE;EAAE;EACfC,kBAAkB,EAAE,EAAE;EAAE;EACxBC,KAAK,EAAE,EAAE;EAAE;EACXC,UAAU,EAAE,EAAE;EAAE;EAChBC,WAAW,EAAE,EAAE;EAAE;EACjBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,EAAE;EAAE;EAChBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,UAAU,EAAE,GAAG;EAAE;EACjBC,cAAc,EAAE,GAAG;EAAE;EACrBC,WAAW,EAAE,GAAG;EAAE;EAClBC,eAAe,EAAE,GAAG;EAAE;EACtBC,KAAK,EAAE,GAAG;EAAE;EACZC,GAAG,EAAE,GAAG;EACR;EACAC,eAAe,EAAE,MAAM;EACvB;EACAC,oBAAoB,EAAE,MAAM,CAAC;AAC/B,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}