{"ast": null, "code": "import { decodeNamedCharacterReference } from 'decode-named-character-reference';\nimport { decodeNumericCharacterReference } from 'micromark-util-decode-numeric-character-reference';\nimport { codes, constants } from 'micromark-util-symbol';\nconst characterEscapeOrReference = /\\\\([!-/:-@[-`{-~])|&(#(?:\\d{1,7}|x[\\da-f]{1,6})|[\\da-z]{1,31});/gi;\n\n/**\n * Decode markdown strings (which occur in places such as fenced code info\n * strings, destinations, labels, and titles).\n *\n * The “string” content type allows character escapes and -references.\n * This decodes those.\n *\n * @param {string} value\n *   Value to decode.\n * @returns {string}\n *   Decoded value.\n */\nexport function decodeString(value) {\n  return value.replace(characterEscapeOrReference, decode);\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @param {string} $1\n *   Character escape.\n * @param {string} $2\n *   Character reference.\n * @returns {string}\n *   Decoded value\n */\nfunction decode($0, $1, $2) {\n  if ($1) {\n    // Escape.\n    return $1;\n  }\n\n  // Reference.\n  const head = $2.charCodeAt(0);\n  if (head === codes.numberSign) {\n    const head = $2.charCodeAt(1);\n    const hex = head === codes.lowercaseX || head === codes.uppercaseX;\n    return decodeNumericCharacterReference($2.slice(hex ? 2 : 1), hex ? constants.numericBaseHexadecimal : constants.numericBaseDecimal);\n  }\n  return decodeNamedCharacterReference($2) || $0;\n}", "map": {"version": 3, "names": ["decodeNamedCharacterReference", "decodeNumericCharacterReference", "codes", "constants", "characterEscapeOrReference", "decodeString", "value", "replace", "decode", "$0", "$1", "$2", "head", "charCodeAt", "numberSign", "hex", "lowercaseX", "uppercaseX", "slice", "numericBaseHexadecimal", "numericBaseDecimal"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-util-decode-string/dev/index.js"], "sourcesContent": ["import {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {codes, constants} from 'micromark-util-symbol'\n\nconst characterEscapeOrReference =\n  /\\\\([!-/:-@[-`{-~])|&(#(?:\\d{1,7}|x[\\da-f]{1,6})|[\\da-z]{1,31});/gi\n\n/**\n * Decode markdown strings (which occur in places such as fenced code info\n * strings, destinations, labels, and titles).\n *\n * The “string” content type allows character escapes and -references.\n * This decodes those.\n *\n * @param {string} value\n *   Value to decode.\n * @returns {string}\n *   Decoded value.\n */\nexport function decodeString(value) {\n  return value.replace(characterEscapeOrReference, decode)\n}\n\n/**\n * @param {string} $0\n *   Match.\n * @param {string} $1\n *   Character escape.\n * @param {string} $2\n *   Character reference.\n * @returns {string}\n *   Decoded value\n */\nfunction decode($0, $1, $2) {\n  if ($1) {\n    // Escape.\n    return $1\n  }\n\n  // Reference.\n  const head = $2.charCodeAt(0)\n\n  if (head === codes.numberSign) {\n    const head = $2.charCodeAt(1)\n    const hex = head === codes.lowercaseX || head === codes.uppercaseX\n    return decodeNumericCharacterReference(\n      $2.slice(hex ? 2 : 1),\n      hex ? constants.numericBaseHexadecimal : constants.numericBaseDecimal\n    )\n  }\n\n  return decodeNamedCharacterReference($2) || $0\n}\n"], "mappings": "AAAA,SAAQA,6BAA6B,QAAO,kCAAkC;AAC9E,SAAQC,+BAA+B,QAAO,mDAAmD;AACjG,SAAQC,KAAK,EAAEC,SAAS,QAAO,uBAAuB;AAEtD,MAAMC,0BAA0B,GAC9B,mEAAmE;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACC,OAAO,CAACH,0BAA0B,EAAEI,MAAM,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC1B,IAAID,EAAE,EAAE;IACN;IACA,OAAOA,EAAE;EACX;;EAEA;EACA,MAAME,IAAI,GAAGD,EAAE,CAACE,UAAU,CAAC,CAAC,CAAC;EAE7B,IAAID,IAAI,KAAKV,KAAK,CAACY,UAAU,EAAE;IAC7B,MAAMF,IAAI,GAAGD,EAAE,CAACE,UAAU,CAAC,CAAC,CAAC;IAC7B,MAAME,GAAG,GAAGH,IAAI,KAAKV,KAAK,CAACc,UAAU,IAAIJ,IAAI,KAAKV,KAAK,CAACe,UAAU;IAClE,OAAOhB,+BAA+B,CACpCU,EAAE,CAACO,KAAK,CAACH,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EACrBA,GAAG,GAAGZ,SAAS,CAACgB,sBAAsB,GAAGhB,SAAS,CAACiB,kBACrD,CAAC;EACH;EAEA,OAAOpB,6BAA6B,CAACW,EAAE,CAAC,IAAIF,EAAE;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}