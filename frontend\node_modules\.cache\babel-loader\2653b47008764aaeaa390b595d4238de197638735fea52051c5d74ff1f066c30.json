{"ast": null, "code": "/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { asciiPunctuation } from 'micromark-util-character';\nimport { codes, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n};\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start;\n\n  /**\n   * Start of character escape.\n   *\n   * ```markdown\n   * > | a\\*b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`');\n    effects.enter(types.characterEscape);\n    effects.enter(types.escapeMarker);\n    effects.consume(code);\n    effects.exit(types.escapeMarker);\n    return inside;\n  }\n\n  /**\n   * After `\\`, at punctuation.\n   *\n   * ```markdown\n   * > | a\\*b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // ASCII punctuation.\n    if (asciiPunctuation(code)) {\n      effects.enter(types.characterEscapeValue);\n      effects.consume(code);\n      effects.exit(types.characterEscapeValue);\n      effects.exit(types.characterEscape);\n      return ok;\n    }\n    return nok(code);\n  }\n}", "map": {"version": 3, "names": ["ok", "assert", "asciiPunctuation", "codes", "types", "characterEscape", "name", "tokenize", "tokenizeCharacterEscape", "effects", "nok", "start", "code", "backslash", "enter", "<PERSON><PERSON><PERSON><PERSON>", "consume", "exit", "inside", "characterEscapeValue"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-core-commonmark/dev/lib/character-escape.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {asciiPunctuation} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of character escape.\n   *\n   * ```markdown\n   * > | a\\*b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.characterEscape)\n    effects.enter(types.escapeMarker)\n    effects.consume(code)\n    effects.exit(types.escapeMarker)\n    return inside\n  }\n\n  /**\n   * After `\\`, at punctuation.\n   *\n   * ```markdown\n   * > | a\\*b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // ASCII punctuation.\n    if (asciiPunctuation(code)) {\n      effects.enter(types.characterEscapeValue)\n      effects.consume(code)\n      effects.exit(types.characterEscapeValue)\n      effects.exit(types.characterEscape)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,gBAAgB,QAAO,0BAA0B;AACzD,SAAQC,KAAK,EAAEC,KAAK,QAAO,uBAAuB;;AAElD;AACA,OAAO,MAAMC,eAAe,GAAG;EAC7BC,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAASA,uBAAuBA,CAACC,OAAO,EAAET,EAAE,EAAEU,GAAG,EAAE;EACjD,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnBX,MAAM,CAACW,IAAI,KAAKT,KAAK,CAACU,SAAS,EAAE,eAAe,CAAC;IACjDJ,OAAO,CAACK,KAAK,CAACV,KAAK,CAACC,eAAe,CAAC;IACpCI,OAAO,CAACK,KAAK,CAACV,KAAK,CAACW,YAAY,CAAC;IACjCN,OAAO,CAACO,OAAO,CAACJ,IAAI,CAAC;IACrBH,OAAO,CAACQ,IAAI,CAACb,KAAK,CAACW,YAAY,CAAC;IAChC,OAAOG,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,MAAMA,CAACN,IAAI,EAAE;IACpB;IACA,IAAIV,gBAAgB,CAACU,IAAI,CAAC,EAAE;MAC1BH,OAAO,CAACK,KAAK,CAACV,KAAK,CAACe,oBAAoB,CAAC;MACzCV,OAAO,CAACO,OAAO,CAACJ,IAAI,CAAC;MACrBH,OAAO,CAACQ,IAAI,CAACb,KAAK,CAACe,oBAAoB,CAAC;MACxCV,OAAO,CAACQ,IAAI,CAACb,KAAK,CAACC,eAAe,CAAC;MACnC,OAAOL,EAAE;IACX;IAEA,OAAOU,GAAG,CAACE,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}