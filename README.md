# Knowledge Platform

A collaborative knowledge platform where teams can create, edit, and organize knowledge articles with rich features.

## Features

- **Knowledge Articles**: Rich text editor with Markdown support, version history, attachments
- **Organization**: Nested categories, tagging system, customizable knowledge trees
- **Search & Discovery**: Full-text search, filtering, trending content
- **Collaboration**: Multi-user editing, comments, contribution tracking
- **Access Control**: Role-based permissions (<PERSON><PERSON>, Editor, Viewer)

## Tech Stack

- **Backend**: FastAPI with SQLAlchemy and SQLite
- **Frontend**: React with modern UI components
- **Authentication**: JWT-based with role-based access control

## Project Structure

```
knowledge-platform/
├── backend/          # FastAPI backend
│   ├── app/
│   ├── tests/
│   └── requirements.txt
├── frontend/         # React frontend
│   ├── src/
│   ├── public/
│   └── package.json
└── README.md
```

## Getting Started

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Frontend Setup
```bash
cd frontend
npm install
npm start
```

## API Documentation

Once the backend is running, visit `http://localhost:8000/docs` for interactive API documentation.
