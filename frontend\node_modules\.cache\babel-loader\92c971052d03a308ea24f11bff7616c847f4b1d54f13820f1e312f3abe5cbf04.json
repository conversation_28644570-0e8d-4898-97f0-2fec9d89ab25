{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */\n\nimport { normalizeUri } from 'micromark-util-sanitize-uri';\n\n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function link(state, node) {\n  /** @type {Properties} */\n  const properties = {\n    href: normalizeUri(node.url)\n  };\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title;\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["normalizeUri", "link", "state", "node", "properties", "href", "url", "title", "undefined", "result", "type", "tagName", "children", "all", "patch", "applyData"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/mdast-util-to-hast/lib/handlers/link.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function link(state, node) {\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(node.url)}\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,YAAY,QAAO,6BAA6B;;AAExD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAChC;EACA,MAAMC,UAAU,GAAG;IAACC,IAAI,EAAEL,YAAY,CAACG,IAAI,CAACG,GAAG;EAAC,CAAC;EAEjD,IAAIH,IAAI,CAACI,KAAK,KAAK,IAAI,IAAIJ,IAAI,CAACI,KAAK,KAAKC,SAAS,EAAE;IACnDJ,UAAU,CAACG,KAAK,GAAGJ,IAAI,CAACI,KAAK;EAC/B;;EAEA;EACA,MAAME,MAAM,GAAG;IACbC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,GAAG;IACZP,UAAU;IACVQ,QAAQ,EAAEV,KAAK,CAACW,GAAG,CAACV,IAAI;EAC1B,CAAC;EACDD,KAAK,CAACY,KAAK,CAACX,IAAI,EAAEM,MAAM,CAAC;EACzB,OAAOP,KAAK,CAACa,SAAS,CAACZ,IAAI,EAAEM,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}