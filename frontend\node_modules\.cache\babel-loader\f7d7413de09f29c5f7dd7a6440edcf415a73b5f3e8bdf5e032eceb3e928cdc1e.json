{"ast": null, "code": "/**\n * @import {Effects, State, TokenType} from 'micromark-util-types'\n */\n\nimport { asciiControl, markdownLineEndingOrSpace, markdownLineEnding } from 'micromark-util-character';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/**\n * Parse destinations.\n *\n * ###### Examples\n *\n * ```markdown\n * <a>\n * <a\\>b>\n * <a b>\n * <a)>\n * a\n * a\\)b\n * a(b)c\n * a(b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type for whole (`<a>` or `b`).\n * @param {TokenType} literalType\n *   Type when enclosed (`<a>`).\n * @param {TokenType} literalMarkerType\n *   Type for enclosing (`<` and `>`).\n * @param {TokenType} rawType\n *   Type when not enclosed (`b`).\n * @param {TokenType} stringType\n *   Type for the value (`a` or `b`).\n * @param {number | undefined} [max=Infinity]\n *   Depth of nested parens (inclusive).\n * @returns {State}\n *   Start state.\n */\nexport function factoryDestination(effects, ok, nok, type, literalType, literalMarkerType, rawType, stringType, max) {\n  const limit = max || Number.POSITIVE_INFINITY;\n  let balance = 0;\n  return start;\n\n  /**\n   * Start of destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *     ^\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.lessThan) {\n      effects.enter(type);\n      effects.enter(literalType);\n      effects.enter(literalMarkerType);\n      effects.consume(code);\n      effects.exit(literalMarkerType);\n      return enclosedBefore;\n    }\n\n    // ASCII control, space, closing paren.\n    if (code === codes.eof || code === codes.space || code === codes.rightParenthesis || asciiControl(code)) {\n      return nok(code);\n    }\n    effects.enter(type);\n    effects.enter(rawType);\n    effects.enter(stringType);\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return raw(code);\n  }\n\n  /**\n   * After `<`, at an enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedBefore(code) {\n    if (code === codes.greaterThan) {\n      effects.enter(literalMarkerType);\n      effects.consume(code);\n      effects.exit(literalMarkerType);\n      effects.exit(literalType);\n      effects.exit(type);\n      return ok;\n    }\n    effects.enter(stringType);\n    effects.enter(types.chunkString, {\n      contentType: constants.contentTypeString\n    });\n    return enclosed(code);\n  }\n\n  /**\n   * In enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosed(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.chunkString);\n      effects.exit(stringType);\n      return enclosedBefore(code);\n    }\n    if (code === codes.eof || code === codes.lessThan || markdownLineEnding(code)) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return code === codes.backslash ? enclosedEscape : enclosed;\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | <a\\*a>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedEscape(code) {\n    if (code === codes.lessThan || code === codes.greaterThan || code === codes.backslash) {\n      effects.consume(code);\n      return enclosed;\n    }\n    return enclosed(code);\n  }\n\n  /**\n   * In raw destination.\n   *\n   * ```markdown\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function raw(code) {\n    if (!balance && (code === codes.eof || code === codes.rightParenthesis || markdownLineEndingOrSpace(code))) {\n      effects.exit(types.chunkString);\n      effects.exit(stringType);\n      effects.exit(rawType);\n      effects.exit(type);\n      return ok(code);\n    }\n    if (balance < limit && code === codes.leftParenthesis) {\n      effects.consume(code);\n      balance++;\n      return raw;\n    }\n    if (code === codes.rightParenthesis) {\n      effects.consume(code);\n      balance--;\n      return raw;\n    }\n\n    // ASCII control (but *not* `\\0`) and space and `(`.\n    // Note: in `markdown-rs`, `\\0` exists in codes, in `micromark-js` it\n    // doesn’t.\n    if (code === codes.eof || code === codes.space || code === codes.leftParenthesis || asciiControl(code)) {\n      return nok(code);\n    }\n    effects.consume(code);\n    return code === codes.backslash ? rawEscape : raw;\n  }\n\n  /**\n   * After `\\`, at special character.\n   *\n   * ```markdown\n   * > | a\\*a\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function rawEscape(code) {\n    if (code === codes.leftParenthesis || code === codes.rightParenthesis || code === codes.backslash) {\n      effects.consume(code);\n      return raw;\n    }\n    return raw(code);\n  }\n}", "map": {"version": 3, "names": ["asciiControl", "markdownLineEndingOrSpace", "markdownLineEnding", "codes", "constants", "types", "factoryDestination", "effects", "ok", "nok", "type", "literalType", "literalMarkerType", "rawType", "stringType", "max", "limit", "Number", "POSITIVE_INFINITY", "balance", "start", "code", "lessThan", "enter", "consume", "exit", "enclosedBefore", "eof", "space", "rightParenthesis", "chunkString", "contentType", "contentTypeString", "raw", "greaterThan", "enclosed", "backslash", "enclosedEscape", "leftParenthesis", "rawEscape"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-factory-destination/dev/index.js"], "sourcesContent": ["/**\n * @import {Effects, State, TokenType} from 'micromark-util-types'\n */\n\nimport {\n  asciiControl,\n  markdownLineEndingOrSpace,\n  markdownLineEnding\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * Parse destinations.\n *\n * ###### Examples\n *\n * ```markdown\n * <a>\n * <a\\>b>\n * <a b>\n * <a)>\n * a\n * a\\)b\n * a(b)c\n * a(b)\n * ```\n *\n * @param {Effects} effects\n *   Context.\n * @param {State} ok\n *   State switched to when successful.\n * @param {State} nok\n *   State switched to when unsuccessful.\n * @param {TokenType} type\n *   Type for whole (`<a>` or `b`).\n * @param {TokenType} literalType\n *   Type when enclosed (`<a>`).\n * @param {TokenType} literalMarkerType\n *   Type for enclosing (`<` and `>`).\n * @param {TokenType} rawType\n *   Type when not enclosed (`b`).\n * @param {TokenType} stringType\n *   Type for the value (`a` or `b`).\n * @param {number | undefined} [max=Infinity]\n *   Depth of nested parens (inclusive).\n * @returns {State}\n *   Start state.\n */\nexport function factoryDestination(\n  effects,\n  ok,\n  nok,\n  type,\n  literalType,\n  literalMarkerType,\n  rawType,\n  stringType,\n  max\n) {\n  const limit = max || Number.POSITIVE_INFINITY\n  let balance = 0\n\n  return start\n\n  /**\n   * Start of destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *     ^\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.lessThan) {\n      effects.enter(type)\n      effects.enter(literalType)\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      return enclosedBefore\n    }\n\n    // ASCII control, space, closing paren.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.rightParenthesis ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.enter(type)\n    effects.enter(rawType)\n    effects.enter(stringType)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return raw(code)\n  }\n\n  /**\n   * After `<`, at an enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedBefore(code) {\n    if (code === codes.greaterThan) {\n      effects.enter(literalMarkerType)\n      effects.consume(code)\n      effects.exit(literalMarkerType)\n      effects.exit(literalType)\n      effects.exit(type)\n      return ok\n    }\n\n    effects.enter(stringType)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return enclosed(code)\n  }\n\n  /**\n   * In enclosed destination.\n   *\n   * ```markdown\n   * > | <aa>\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosed(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.chunkString)\n      effects.exit(stringType)\n      return enclosedBefore(code)\n    }\n\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      markdownLineEnding(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? enclosedEscape : enclosed\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * ```markdown\n   * > | <a\\*a>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function enclosedEscape(code) {\n    if (\n      code === codes.lessThan ||\n      code === codes.greaterThan ||\n      code === codes.backslash\n    ) {\n      effects.consume(code)\n      return enclosed\n    }\n\n    return enclosed(code)\n  }\n\n  /**\n   * In raw destination.\n   *\n   * ```markdown\n   * > | aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function raw(code) {\n    if (\n      !balance &&\n      (code === codes.eof ||\n        code === codes.rightParenthesis ||\n        markdownLineEndingOrSpace(code))\n    ) {\n      effects.exit(types.chunkString)\n      effects.exit(stringType)\n      effects.exit(rawType)\n      effects.exit(type)\n      return ok(code)\n    }\n\n    if (balance < limit && code === codes.leftParenthesis) {\n      effects.consume(code)\n      balance++\n      return raw\n    }\n\n    if (code === codes.rightParenthesis) {\n      effects.consume(code)\n      balance--\n      return raw\n    }\n\n    // ASCII control (but *not* `\\0`) and space and `(`.\n    // Note: in `markdown-rs`, `\\0` exists in codes, in `micromark-js` it\n    // doesn’t.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.leftParenthesis ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return code === codes.backslash ? rawEscape : raw\n  }\n\n  /**\n   * After `\\`, at special character.\n   *\n   * ```markdown\n   * > | a\\*a\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function rawEscape(code) {\n    if (\n      code === codes.leftParenthesis ||\n      code === codes.rightParenthesis ||\n      code === codes.backslash\n    ) {\n      effects.consume(code)\n      return raw\n    }\n\n    return raw(code)\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SACEA,YAAY,EACZC,yBAAyB,EACzBC,kBAAkB,QACb,0BAA0B;AACjC,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAChCC,OAAO,EACPC,EAAE,EACFC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,iBAAiB,EACjBC,OAAO,EACPC,UAAU,EACVC,GAAG,EACH;EACA,MAAMC,KAAK,GAAGD,GAAG,IAAIE,MAAM,CAACC,iBAAiB;EAC7C,IAAIC,OAAO,GAAG,CAAC;EAEf,OAAOC,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIA,IAAI,KAAKlB,KAAK,CAACmB,QAAQ,EAAE;MAC3Bf,OAAO,CAACgB,KAAK,CAACb,IAAI,CAAC;MACnBH,OAAO,CAACgB,KAAK,CAACZ,WAAW,CAAC;MAC1BJ,OAAO,CAACgB,KAAK,CAACX,iBAAiB,CAAC;MAChCL,OAAO,CAACiB,OAAO,CAACH,IAAI,CAAC;MACrBd,OAAO,CAACkB,IAAI,CAACb,iBAAiB,CAAC;MAC/B,OAAOc,cAAc;IACvB;;IAEA;IACA,IACEL,IAAI,KAAKlB,KAAK,CAACwB,GAAG,IAClBN,IAAI,KAAKlB,KAAK,CAACyB,KAAK,IACpBP,IAAI,KAAKlB,KAAK,CAAC0B,gBAAgB,IAC/B7B,YAAY,CAACqB,IAAI,CAAC,EAClB;MACA,OAAOZ,GAAG,CAACY,IAAI,CAAC;IAClB;IAEAd,OAAO,CAACgB,KAAK,CAACb,IAAI,CAAC;IACnBH,OAAO,CAACgB,KAAK,CAACV,OAAO,CAAC;IACtBN,OAAO,CAACgB,KAAK,CAACT,UAAU,CAAC;IACzBP,OAAO,CAACgB,KAAK,CAAClB,KAAK,CAACyB,WAAW,EAAE;MAACC,WAAW,EAAE3B,SAAS,CAAC4B;IAAiB,CAAC,CAAC;IAC5E,OAAOC,GAAG,CAACZ,IAAI,CAAC;EAClB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASK,cAAcA,CAACL,IAAI,EAAE;IAC5B,IAAIA,IAAI,KAAKlB,KAAK,CAAC+B,WAAW,EAAE;MAC9B3B,OAAO,CAACgB,KAAK,CAACX,iBAAiB,CAAC;MAChCL,OAAO,CAACiB,OAAO,CAACH,IAAI,CAAC;MACrBd,OAAO,CAACkB,IAAI,CAACb,iBAAiB,CAAC;MAC/BL,OAAO,CAACkB,IAAI,CAACd,WAAW,CAAC;MACzBJ,OAAO,CAACkB,IAAI,CAACf,IAAI,CAAC;MAClB,OAAOF,EAAE;IACX;IAEAD,OAAO,CAACgB,KAAK,CAACT,UAAU,CAAC;IACzBP,OAAO,CAACgB,KAAK,CAAClB,KAAK,CAACyB,WAAW,EAAE;MAACC,WAAW,EAAE3B,SAAS,CAAC4B;IAAiB,CAAC,CAAC;IAC5E,OAAOG,QAAQ,CAACd,IAAI,CAAC;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASc,QAAQA,CAACd,IAAI,EAAE;IACtB,IAAIA,IAAI,KAAKlB,KAAK,CAAC+B,WAAW,EAAE;MAC9B3B,OAAO,CAACkB,IAAI,CAACpB,KAAK,CAACyB,WAAW,CAAC;MAC/BvB,OAAO,CAACkB,IAAI,CAACX,UAAU,CAAC;MACxB,OAAOY,cAAc,CAACL,IAAI,CAAC;IAC7B;IAEA,IACEA,IAAI,KAAKlB,KAAK,CAACwB,GAAG,IAClBN,IAAI,KAAKlB,KAAK,CAACmB,QAAQ,IACvBpB,kBAAkB,CAACmB,IAAI,CAAC,EACxB;MACA,OAAOZ,GAAG,CAACY,IAAI,CAAC;IAClB;IAEAd,OAAO,CAACiB,OAAO,CAACH,IAAI,CAAC;IACrB,OAAOA,IAAI,KAAKlB,KAAK,CAACiC,SAAS,GAAGC,cAAc,GAAGF,QAAQ;EAC7D;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASE,cAAcA,CAAChB,IAAI,EAAE;IAC5B,IACEA,IAAI,KAAKlB,KAAK,CAACmB,QAAQ,IACvBD,IAAI,KAAKlB,KAAK,CAAC+B,WAAW,IAC1Bb,IAAI,KAAKlB,KAAK,CAACiC,SAAS,EACxB;MACA7B,OAAO,CAACiB,OAAO,CAACH,IAAI,CAAC;MACrB,OAAOc,QAAQ;IACjB;IAEA,OAAOA,QAAQ,CAACd,IAAI,CAAC;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASY,GAAGA,CAACZ,IAAI,EAAE;IACjB,IACE,CAACF,OAAO,KACPE,IAAI,KAAKlB,KAAK,CAACwB,GAAG,IACjBN,IAAI,KAAKlB,KAAK,CAAC0B,gBAAgB,IAC/B5B,yBAAyB,CAACoB,IAAI,CAAC,CAAC,EAClC;MACAd,OAAO,CAACkB,IAAI,CAACpB,KAAK,CAACyB,WAAW,CAAC;MAC/BvB,OAAO,CAACkB,IAAI,CAACX,UAAU,CAAC;MACxBP,OAAO,CAACkB,IAAI,CAACZ,OAAO,CAAC;MACrBN,OAAO,CAACkB,IAAI,CAACf,IAAI,CAAC;MAClB,OAAOF,EAAE,CAACa,IAAI,CAAC;IACjB;IAEA,IAAIF,OAAO,GAAGH,KAAK,IAAIK,IAAI,KAAKlB,KAAK,CAACmC,eAAe,EAAE;MACrD/B,OAAO,CAACiB,OAAO,CAACH,IAAI,CAAC;MACrBF,OAAO,EAAE;MACT,OAAOc,GAAG;IACZ;IAEA,IAAIZ,IAAI,KAAKlB,KAAK,CAAC0B,gBAAgB,EAAE;MACnCtB,OAAO,CAACiB,OAAO,CAACH,IAAI,CAAC;MACrBF,OAAO,EAAE;MACT,OAAOc,GAAG;IACZ;;IAEA;IACA;IACA;IACA,IACEZ,IAAI,KAAKlB,KAAK,CAACwB,GAAG,IAClBN,IAAI,KAAKlB,KAAK,CAACyB,KAAK,IACpBP,IAAI,KAAKlB,KAAK,CAACmC,eAAe,IAC9BtC,YAAY,CAACqB,IAAI,CAAC,EAClB;MACA,OAAOZ,GAAG,CAACY,IAAI,CAAC;IAClB;IAEAd,OAAO,CAACiB,OAAO,CAACH,IAAI,CAAC;IACrB,OAAOA,IAAI,KAAKlB,KAAK,CAACiC,SAAS,GAAGG,SAAS,GAAGN,GAAG;EACnD;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASM,SAASA,CAAClB,IAAI,EAAE;IACvB,IACEA,IAAI,KAAKlB,KAAK,CAACmC,eAAe,IAC9BjB,IAAI,KAAKlB,KAAK,CAAC0B,gBAAgB,IAC/BR,IAAI,KAAKlB,KAAK,CAACiC,SAAS,EACxB;MACA7B,OAAO,CAACiB,OAAO,CAACH,IAAI,CAAC;MACrB,OAAOY,GAAG;IACZ;IAEA,OAAOA,GAAG,CAACZ,IAAI,CAAC;EAClB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}