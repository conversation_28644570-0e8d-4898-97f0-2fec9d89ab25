{"ast": null, "code": "// Note: types exposed from `index.d.ts`.\nexport { toJsxRuntime } from './lib/index.js';", "map": {"version": 3, "names": ["toJsxRuntime"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/hast-util-to-jsx-runtime/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nexport {toJsxRuntime} from './lib/index.js'\n"], "mappings": "AAAA;AACA,SAAQA,YAAY,QAAO,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}