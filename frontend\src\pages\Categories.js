import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FolderIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

const Categories = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await axios.get('/categories');
      setCategories(response.data);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const buildCategoryTree = (categories, parentId = null) => {
    return categories
      .filter(category => category.parent_id === parentId)
      .map(category => ({
        ...category,
        children: buildCategoryTree(categories, category.id)
      }));
  };

  const renderCategory = (category, level = 0) => {
    const indent = level * 20;
    
    return (
      <div key={category.id} style={{ marginLeft: `${indent}px` }}>
        <Link
          to={`/articles?category=${category.id}`}
          className="flex items-center space-x-3 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow border border-gray-200 mb-3"
        >
          <FolderIcon className="h-6 w-6 text-blue-600 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">{category.name}</h3>
            {category.description && (
              <p className="text-sm text-gray-600 mt-1">{category.description}</p>
            )}
          </div>
          <div className="flex items-center space-x-1 text-sm text-gray-500">
            <DocumentTextIcon className="h-4 w-4" />
            <span>{category.articles?.length || 0} articles</span>
          </div>
        </Link>
        
        {category.children && category.children.map(child => 
          renderCategory(child, level + 1)
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const categoryTree = buildCategoryTree(categories);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Categories</h1>
      </div>
      
      {categoryTree.length > 0 ? (
        <div className="space-y-4">
          {categoryTree.map(category => renderCategory(category))}
        </div>
      ) : (
        <div className="text-center py-12">
          <FolderIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No categories yet</h3>
          <p className="text-gray-600">Categories will appear here once they are created.</p>
        </div>
      )}
    </div>
  );
};

export default Categories;
