#!/usr/bin/env python3
"""
Create test data for the Knowledge Platform
Creates users, categories, tags, and articles for testing
"""

import requests
import json

BACKEND_URL = "http://localhost:8000"

def create_test_users():
    """Create test users with different roles"""
    print("🔍 Creating test users...")
    
    users = [
        {
            "username": "admin_user",
            "email": "<EMAIL>",
            "password": "admin123",
            "full_name": "Admin User",
            "role": "admin"
        },
        {
            "username": "editor_user", 
            "email": "<EMAIL>",
            "password": "editor123",
            "full_name": "Editor User",
            "role": "editor"
        },
        {
            "username": "viewer_user",
            "email": "<EMAIL>", 
            "password": "viewer123",
            "full_name": "Viewer User",
            "role": "viewer"
        }
    ]
    
    created_users = []
    
    for user_data in users:
        role = user_data.pop("role")  # Remove role from registration data
        
        try:
            # Register user
            response = requests.post(f"{BACKEND_URL}/auth/register", json=user_data)
            if response.status_code == 200:
                user = response.json()
                print(f"✅ Created user: {user['username']}")
                
                # Login to get token
                login_response = requests.post(f"{BACKEND_URL}/auth/login", json={
                    "username": user_data["username"],
                    "password": user_data["password"]
                })
                
                if login_response.status_code == 200:
                    token = login_response.json()["access_token"]
                    created_users.append({
                        "user": user,
                        "token": token,
                        "role": role,
                        "credentials": user_data
                    })
                
            else:
                print(f"❌ Failed to create user {user_data['username']}: {response.text}")
                
        except Exception as e:
            print(f"❌ Error creating user {user_data['username']}: {e}")
    
    return created_users

def create_test_categories(editor_token):
    """Create test categories"""
    print("🔍 Creating test categories...")
    
    categories = [
        {
            "name": "Technology",
            "description": "Articles about technology, programming, and software development"
        },
        {
            "name": "Documentation", 
            "description": "Technical documentation and guides"
        },
        {
            "name": "Tutorials",
            "description": "Step-by-step tutorials and how-to guides"
        },
        {
            "name": "Best Practices",
            "description": "Industry best practices and standards"
        }
    ]
    
    headers = {"Authorization": f"Bearer {editor_token}"}
    created_categories = []
    
    for category_data in categories:
        try:
            response = requests.post(f"{BACKEND_URL}/categories/", json=category_data, headers=headers)
            if response.status_code == 200:
                category = response.json()
                print(f"✅ Created category: {category['name']}")
                created_categories.append(category)
            else:
                print(f"❌ Failed to create category {category_data['name']}: {response.text}")
        except Exception as e:
            print(f"❌ Error creating category {category_data['name']}: {e}")
    
    return created_categories

def create_test_tags(editor_token):
    """Create test tags"""
    print("🔍 Creating test tags...")
    
    tags = [
        {"name": "python", "color": "#3776ab"},
        {"name": "javascript", "color": "#f7df1e"},
        {"name": "react", "color": "#61dafb"},
        {"name": "fastapi", "color": "#009688"},
        {"name": "tutorial", "color": "#ff9800"},
        {"name": "beginner", "color": "#4caf50"},
        {"name": "advanced", "color": "#f44336"},
        {"name": "api", "color": "#9c27b0"}
    ]
    
    headers = {"Authorization": f"Bearer {editor_token}"}
    created_tags = []
    
    for tag_data in tags:
        try:
            response = requests.post(f"{BACKEND_URL}/tags/", json=tag_data, headers=headers)
            if response.status_code == 200:
                tag = response.json()
                print(f"✅ Created tag: {tag['name']}")
                created_tags.append(tag)
            else:
                print(f"❌ Failed to create tag {tag_data['name']}: {response.text}")
        except Exception as e:
            print(f"❌ Error creating tag {tag_data['name']}: {e}")
    
    return created_tags

def create_test_articles(editor_token, categories, tags):
    """Create test articles"""
    print("🔍 Creating test articles...")
    
    articles = [
        {
            "title": "Getting Started with FastAPI",
            "content": """# Getting Started with FastAPI

FastAPI is a modern, fast (high-performance), web framework for building APIs with Python 3.7+ based on standard Python type hints.

## Key Features

- **Fast**: Very high performance, on par with NodeJS and Go
- **Fast to code**: Increase the speed to develop features by about 200% to 300%
- **Fewer bugs**: Reduce about 40% of human (developer) induced errors
- **Intuitive**: Great editor support with completion everywhere
- **Easy**: Designed to be easy to use and learn
- **Short**: Minimize code duplication

## Installation

```bash
pip install fastapi
pip install uvicorn[standard]
```

## Basic Example

```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}

@app.get("/items/{item_id}")
def read_item(item_id: int, q: str = None):
    return {"item_id": item_id, "q": q}
```

## Running the Application

```bash
uvicorn main:app --reload
```

Visit `http://127.0.0.1:8000` to see your API in action!""",
            "summary": "A comprehensive guide to getting started with FastAPI, covering installation, basic usage, and key features.",
            "category_id": None,  # Will be set to Technology category
            "tag_ids": [],  # Will be set to fastapi, python, tutorial, beginner
            "is_published": True,
            "is_featured": True
        },
        {
            "title": "React Hooks Best Practices",
            "content": """# React Hooks Best Practices

React Hooks have revolutionized how we write React components. Here are some best practices to follow.

## 1. Use Custom Hooks for Reusable Logic

```javascript
function useCounter(initialValue = 0) {
  const [count, setCount] = useState(initialValue);
  
  const increment = () => setCount(count + 1);
  const decrement = () => setCount(count - 1);
  const reset = () => setCount(initialValue);
  
  return { count, increment, decrement, reset };
}
```

## 2. Optimize with useMemo and useCallback

```javascript
const ExpensiveComponent = ({ items, filter }) => {
  const filteredItems = useMemo(() => {
    return items.filter(item => item.category === filter);
  }, [items, filter]);
  
  const handleClick = useCallback((id) => {
    // Handle click logic
  }, []);
  
  return (
    <div>
      {filteredItems.map(item => (
        <Item key={item.id} item={item} onClick={handleClick} />
      ))}
    </div>
  );
};
```

## 3. Use useEffect Properly

- Always include dependencies in the dependency array
- Clean up side effects to prevent memory leaks
- Separate concerns into different useEffect hooks

## Conclusion

Following these best practices will help you write more maintainable and performant React applications.""",
            "summary": "Essential best practices for using React Hooks effectively, including custom hooks, optimization techniques, and proper useEffect usage.",
            "category_id": None,  # Will be set to Best Practices category
            "tag_ids": [],  # Will be set to react, javascript, advanced
            "is_published": True,
            "is_featured": False
        },
        {
            "title": "API Documentation Guidelines",
            "content": """# API Documentation Guidelines

Good API documentation is crucial for developer experience. Here's how to create excellent API docs.

## 1. Structure Your Documentation

### Overview
- What does your API do?
- Who is it for?
- What are the main use cases?

### Authentication
- How to authenticate requests
- API key management
- Rate limiting information

### Endpoints
- Clear endpoint descriptions
- Request/response examples
- Error codes and messages

## 2. Use Interactive Examples

Provide working code examples in multiple languages:

```python
import requests

response = requests.get(
    'https://api.example.com/users',
    headers={'Authorization': 'Bearer YOUR_TOKEN'}
)
```

```javascript
fetch('https://api.example.com/users', {
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  }
})
.then(response => response.json())
.then(data => console.log(data));
```

## 3. Document Error Responses

```json
{
  "error": {
    "code": "INVALID_TOKEN",
    "message": "The provided authentication token is invalid",
    "details": "Token has expired or is malformed"
  }
}
```

## 4. Keep It Updated

- Use automated tools to generate docs from code
- Version your documentation
- Provide migration guides for breaking changes

## Tools for API Documentation

- **OpenAPI/Swagger**: Industry standard
- **Postman**: Great for testing and documentation
- **Insomnia**: Alternative to Postman
- **GitBook**: For comprehensive documentation sites""",
            "summary": "Comprehensive guidelines for creating excellent API documentation that developers will love to use.",
            "category_id": None,  # Will be set to Documentation category
            "tag_ids": [],  # Will be set to api, documentation, best-practices
            "is_published": True,
            "is_featured": False
        }
    ]
    
    # Map category names to IDs
    category_map = {cat['name']: cat['id'] for cat in categories}
    tag_map = {tag['name']: tag['id'] for tag in tags}
    
    # Set category and tag IDs
    articles[0]['category_id'] = category_map.get('Technology')
    articles[0]['tag_ids'] = [tag_map.get('fastapi'), tag_map.get('python'), tag_map.get('tutorial'), tag_map.get('beginner')]
    
    articles[1]['category_id'] = category_map.get('Best Practices')
    articles[1]['tag_ids'] = [tag_map.get('react'), tag_map.get('javascript'), tag_map.get('advanced')]
    
    articles[2]['category_id'] = category_map.get('Documentation')
    articles[2]['tag_ids'] = [tag_map.get('api'), tag_map.get('tutorial')]
    
    # Filter out None values
    for article in articles:
        article['tag_ids'] = [tag_id for tag_id in article['tag_ids'] if tag_id is not None]
    
    headers = {"Authorization": f"Bearer {editor_token}"}
    created_articles = []
    
    for article_data in articles:
        try:
            response = requests.post(f"{BACKEND_URL}/articles/", json=article_data, headers=headers)
            if response.status_code == 200:
                article = response.json()
                print(f"✅ Created article: {article['title']}")
                created_articles.append(article)
            else:
                print(f"❌ Failed to create article {article_data['title']}: {response.text}")
        except Exception as e:
            print(f"❌ Error creating article {article_data['title']}: {e}")
    
    return created_articles

def main():
    """Main function to create all test data"""
    print("🚀 Creating test data for Knowledge Platform\n")
    
    # Create users
    users = create_test_users()
    if not users:
        print("❌ Failed to create users")
        return
    
    # Find editor user
    editor_user = next((u for u in users if u['role'] == 'editor'), None)
    if not editor_user:
        print("❌ No editor user found")
        return
    
    print()
    
    # Create categories
    categories = create_test_categories(editor_user['token'])
    
    print()
    
    # Create tags
    tags = create_test_tags(editor_user['token'])
    
    print()
    
    # Create articles
    articles = create_test_articles(editor_user['token'], categories, tags)
    
    print()
    print("🎉 Test data creation completed!")
    print(f"\n📊 Created:")
    print(f"   👥 {len(users)} users")
    print(f"   📁 {len(categories)} categories")
    print(f"   🏷️  {len(tags)} tags")
    print(f"   📄 {len(articles)} articles")
    
    print(f"\n🔑 Test User Credentials:")
    for user in users:
        print(f"   {user['role'].title()}: {user['credentials']['username']} / {user['credentials']['password']}")

if __name__ == "__main__":
    main()
