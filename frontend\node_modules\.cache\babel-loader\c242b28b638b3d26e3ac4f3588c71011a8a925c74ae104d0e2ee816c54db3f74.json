{"ast": null, "code": "/**\n * While micromark is a lexer/tokenizer, the common case of going from markdown\n * to html is currently built in as this module, even though the parts can be\n * used separately to build ASTs, CSTs, or many other output formats.\n *\n * Having an HTML compiler built in is useful because it allows us to check for\n * compliancy to CommonMark, the de facto norm of markdown, specified in roughly\n * 600 input/output cases.\n *\n * This module has an interface that accepts lists of events instead of the\n * whole at once, however, because markdown can’t be truly streaming, we buffer\n * events before processing and outputting the final result.\n */\n\n/**\n * @import {\n *   CompileContext,\n *   CompileData,\n *   CompileOptions,\n *   Compile,\n *   Definition,\n *   Event,\n *   Handle,\n *   HtmlExtension,\n *   LineEnding,\n *   NormalizedHtmlExtension,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef Media\n * @property {boolean | undefined} [image]\n * @property {string | undefined} [labelId]\n * @property {string | undefined} [label]\n * @property {string | undefined} [referenceId]\n * @property {string | undefined} [destination]\n * @property {string | undefined} [title]\n */\n\nimport { decodeNamedCharacterReference } from 'decode-named-character-reference';\nimport { ok as assert } from 'devlop';\nimport { push } from 'micromark-util-chunked';\nimport { combineHtmlExtensions } from 'micromark-util-combine-extensions';\nimport { decodeNumericCharacterReference } from 'micromark-util-decode-numeric-character-reference';\nimport { encode as _encode } from 'micromark-util-encode';\nimport { normalizeIdentifier } from 'micromark-util-normalize-identifier';\nimport { sanitizeUri } from 'micromark-util-sanitize-uri';\nimport { codes, constants, types } from 'micromark-util-symbol';\nconst hasOwnProperty = {}.hasOwnProperty;\n\n/**\n * These two are allowlists of safe protocols for full URLs in respectively the\n * `href` (on `<a>`) and `src` (on `<img>`) attributes.\n * They are based on what is allowed on GitHub,\n * <https://github.com/syntax-tree/hast-util-sanitize/blob/9275b21/lib/github.json#L31>\n */\nconst protocolHref = /^(https?|ircs?|mailto|xmpp)$/i;\nconst protocolSource = /^https?$/i;\n\n/**\n * @param {CompileOptions | null | undefined} [options]\n * @returns {Compile}\n */\nexport function compile(options) {\n  const settings = options || {};\n\n  /**\n   * Tags is needed because according to markdown, links and emphasis and\n   * whatnot can exist in images, however, as HTML doesn’t allow content in\n   * images, the tags are ignored in the `alt` attribute, but the content\n   * remains.\n   *\n   * @type {boolean | undefined}\n   */\n  let tags = true;\n\n  /**\n   * An object to track identifiers to media (URLs and titles) defined with\n   * definitions.\n   *\n   * @type {Record<string, Definition>}\n   */\n  const definitions = {};\n\n  /**\n   * A lot of the handlers need to capture some of the output data, modify it\n   * somehow, and then deal with it.\n   * We do that by tracking a stack of buffers, that can be opened (with\n   * `buffer`) and closed (with `resume`) to access them.\n   *\n   * @type {Array<Array<string>>}\n   */\n  const buffers = [[]];\n\n  /**\n   * As we can have links in images and the other way around, where the deepest\n   * ones are closed first, we need to track which one we’re in.\n   *\n   * @type {Array<Media>}\n   */\n  const mediaStack = [];\n\n  /**\n   * Same as `mediaStack` for tightness, which is specific to lists.\n   * We need to track if we’re currently in a tight or loose container.\n   *\n   * @type {Array<boolean>}\n   */\n  const tightStack = [];\n\n  /** @type {HtmlExtension} */\n  const defaultHandlers = {\n    enter: {\n      blockQuote: onenterblockquote,\n      codeFenced: onentercodefenced,\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: onentercodeindented,\n      codeText: onentercodetext,\n      content: onentercontent,\n      definition: onenterdefinition,\n      definitionDestinationString: onenterdefinitiondestinationstring,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: onenteremphasis,\n      htmlFlow: onenterhtmlflow,\n      htmlText: onenterhtml,\n      image: onenterimage,\n      label: buffer,\n      link: onenterlink,\n      listItemMarker: onenterlistitemmarker,\n      listItemValue: onenterlistitemvalue,\n      listOrdered: onenterlistordered,\n      listUnordered: onenterlistunordered,\n      paragraph: onenterparagraph,\n      reference: buffer,\n      resource: onenterresource,\n      resourceDestinationString: onenterresourcedestinationstring,\n      resourceTitleString: buffer,\n      setextHeading: onentersetextheading,\n      strong: onenterstrong\n    },\n    exit: {\n      atxHeading: onexitatxheading,\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: onexitblockquote,\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      codeFenced: onexitflowcode,\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onresumedrop,\n      codeFlowValue: onexitcodeflowvalue,\n      codeIndented: onexitflowcode,\n      codeText: onexitcodetext,\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: onexitdefinition,\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: onexitemphasis,\n      hardBreakEscape: onexithardbreak,\n      hardBreakTrailing: onexithardbreak,\n      htmlFlow: onexithtml,\n      htmlFlowData: onexitdata,\n      htmlText: onexithtml,\n      htmlTextData: onexitdata,\n      image: onexitmedia,\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: onexitmedia,\n      listOrdered: onexitlistordered,\n      listUnordered: onexitlistunordered,\n      paragraph: onexitparagraph,\n      reference: onresumedrop,\n      referenceString: onexitreferencestring,\n      resource: onresumedrop,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      setextHeading: onexitsetextheading,\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: onexitstrong,\n      thematicBreak: onexitthematicbreak\n    }\n  };\n\n  /**\n   * Combine the HTML extensions with the default handlers.\n   * An HTML extension is an object whose fields are either `enter` or `exit`\n   * (reflecting whether a token is entered or exited).\n   * The values at such objects are names of tokens mapping to handlers.\n   * Handlers are called, respectively when a token is opener or closed, with\n   * that token, and a context as `this`.\n   */\n  const handlers = /** @type {NormalizedHtmlExtension} */\n  combineHtmlExtensions([defaultHandlers, ...(settings.htmlExtensions || [])]);\n\n  /**\n   * Handlers do often need to keep track of some state.\n   * That state is provided here as a key-value store (an object).\n   *\n   * @type {CompileData}\n   */\n  const data = {\n    definitions,\n    tightStack\n  };\n\n  /**\n   * The context for handlers references a couple of useful functions.\n   * In handlers from extensions, those can be accessed at `this`.\n   * For the handlers here, they can be accessed directly.\n   *\n   * @type {Omit<CompileContext, 'sliceSerialize'>}\n   */\n  const context = {\n    buffer,\n    encode,\n    getData,\n    lineEndingIfNeeded,\n    options: settings,\n    raw,\n    resume,\n    setData,\n    tag\n  };\n\n  /**\n   * Generally, micromark copies line endings (`'\\r'`, `'\\n'`, `'\\r\\n'`) in the\n   * markdown document over to the compiled HTML.\n   * In some cases, such as `> a`, CommonMark requires that extra line endings\n   * are added: `<blockquote>\\n<p>a</p>\\n</blockquote>`.\n   * This variable hold the default line ending when given (or `undefined`),\n   * and in the latter case will be updated to the first found line ending if\n   * there is one.\n   */\n  let lineEndingStyle = settings.defaultLineEnding;\n\n  // Return the function that handles a slice of events.\n  return compile;\n\n  /**\n   * Deal w/ a slice of events.\n   * Return either the empty string if there’s nothing of note to return, or the\n   * result when done.\n   *\n   * @param {ReadonlyArray<Event>} events\n   * @returns {string}\n   */\n  function compile(events) {\n    let index = -1;\n    let start = 0;\n    /** @type {Array<number>} */\n    const listStack = [];\n    // As definitions can come after references, we need to figure out the media\n    // (urls and titles) defined by them before handling the references.\n    // So, we do sort of what HTML does: put metadata at the start (in head), and\n    // then put content after (`body`).\n    /** @type {Array<Event>} */\n    let head = [];\n    /** @type {Array<Event>} */\n    let body = [];\n    while (++index < events.length) {\n      // Figure out the line ending style used in the document.\n      if (!lineEndingStyle && (events[index][1].type === types.lineEnding || events[index][1].type === types.lineEndingBlank)) {\n        lineEndingStyle = /** @type {LineEnding} */\n        events[index][2].sliceSerialize(events[index][1]);\n      }\n\n      // Preprocess lists to infer whether the list is loose or not.\n      if (events[index][1].type === types.listOrdered || events[index][1].type === types.listUnordered) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index);\n        } else {\n          prepareList(events.slice(listStack.pop(), index));\n        }\n      }\n\n      // Move definitions to the front.\n      if (events[index][1].type === types.definition) {\n        if (events[index][0] === 'enter') {\n          body = push(body, events.slice(start, index));\n          start = index;\n        } else {\n          head = push(head, events.slice(start, index + 1));\n          start = index + 1;\n        }\n      }\n    }\n    head = push(head, body);\n    head = push(head, events.slice(start));\n    index = -1;\n    const result = head;\n\n    // Handle the start of the document, if defined.\n    if (handlers.enter.null) {\n      handlers.enter.null.call(context);\n    }\n\n    // Handle all events.\n    while (++index < events.length) {\n      const handles = handlers[result[index][0]];\n      const kind = result[index][1].type;\n      const handle = handles[kind];\n      if (hasOwnProperty.call(handles, kind) && handle) {\n        handle.call({\n          sliceSerialize: result[index][2].sliceSerialize,\n          ...context\n        }, result[index][1]);\n      }\n    }\n\n    // Handle the end of the document, if defined.\n    if (handlers.exit.null) {\n      handlers.exit.null.call(context);\n    }\n    return buffers[0].join('');\n  }\n\n  /**\n   * Figure out whether lists are loose or not.\n   *\n   * @param {ReadonlyArray<Event>} slice\n   * @returns {undefined}\n   */\n  function prepareList(slice) {\n    const length = slice.length;\n    let index = 0; // Skip open.\n    let containerBalance = 0;\n    let loose = false;\n    /** @type {boolean | undefined} */\n    let atMarker;\n    while (++index < length) {\n      const event = slice[index];\n      if (event[1]._container) {\n        atMarker = undefined;\n        if (event[0] === 'enter') {\n          containerBalance++;\n        } else {\n          containerBalance--;\n        }\n      } else switch (event[1].type) {\n        case types.listItemPrefix:\n          {\n            if (event[0] === 'exit') {\n              atMarker = true;\n            }\n            break;\n          }\n        case types.linePrefix:\n          {\n            // Ignore\n\n            break;\n          }\n        case types.lineEndingBlank:\n          {\n            if (event[0] === 'enter' && !containerBalance) {\n              if (atMarker) {\n                atMarker = undefined;\n              } else {\n                loose = true;\n              }\n            }\n            break;\n          }\n        default:\n          {\n            atMarker = undefined;\n          }\n      }\n    }\n    slice[0][1]._loose = loose;\n  }\n\n  /**\n   * @type {CompileContext['setData']}\n   */\n  function setData(key, value) {\n    // @ts-expect-error: assume `value` is omitted (`undefined` is passed) only\n    // if allowed.\n    data[key] = value;\n  }\n\n  /**\n   * @type {CompileContext['getData']}\n   */\n  function getData(key) {\n    return data[key];\n  }\n\n  /** @type {CompileContext['buffer']} */\n  function buffer() {\n    buffers.push([]);\n  }\n\n  /** @type {CompileContext['resume']} */\n  function resume() {\n    const buf = buffers.pop();\n    assert(buf !== undefined, 'Cannot resume w/o buffer');\n    return buf.join('');\n  }\n\n  /** @type {CompileContext['tag']} */\n  function tag(value) {\n    if (!tags) return;\n    setData('lastWasTag', true);\n    buffers[buffers.length - 1].push(value);\n  }\n\n  /** @type {CompileContext['raw']} */\n  function raw(value) {\n    setData('lastWasTag');\n    buffers[buffers.length - 1].push(value);\n  }\n\n  /**\n   * Output an extra line ending.\n   *\n   * @returns {undefined}\n   */\n  function lineEnding() {\n    raw(lineEndingStyle || '\\n');\n  }\n\n  /** @type {CompileContext['lineEndingIfNeeded']} */\n  function lineEndingIfNeeded() {\n    const buffer = buffers[buffers.length - 1];\n    const slice = buffer[buffer.length - 1];\n    const previous = slice ? slice.charCodeAt(slice.length - 1) : codes.eof;\n    if (previous === codes.lf || previous === codes.cr || previous === codes.eof) {\n      return;\n    }\n    lineEnding();\n  }\n\n  /** @type {CompileContext['encode']} */\n  function encode(value) {\n    return getData('ignoreEncode') ? value : _encode(value);\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @returns {undefined}\n   */\n  function onresumedrop() {\n    resume();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered(token) {\n    tightStack.push(!token._loose);\n    lineEndingIfNeeded();\n    tag('<ol');\n    setData('expectFirstItem', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistunordered(token) {\n    tightStack.push(!token._loose);\n    lineEndingIfNeeded();\n    tag('<ul');\n    setData('expectFirstItem', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (getData('expectFirstItem')) {\n      const value = Number.parseInt(this.sliceSerialize(token), constants.numericBaseDecimal);\n      if (value !== 1) {\n        tag(' start=\"' + encode(String(value)) + '\"');\n      }\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterlistitemmarker() {\n    if (getData('expectFirstItem')) {\n      tag('>');\n    } else {\n      onexitlistitem();\n    }\n    lineEndingIfNeeded();\n    tag('<li>');\n    setData('expectFirstItem');\n    // “Hack” to prevent a line ending from showing up if the item is empty.\n    setData('lastWasTag');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistordered() {\n    onexitlistitem();\n    tightStack.pop();\n    lineEnding();\n    tag('</ol>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistunordered() {\n    onexitlistitem();\n    tightStack.pop();\n    lineEnding();\n    tag('</ul>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistitem() {\n    if (getData('lastWasTag') && !getData('slurpAllLineEndings')) {\n      lineEndingIfNeeded();\n    }\n    tag('</li>');\n    setData('slurpAllLineEndings');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterblockquote() {\n    tightStack.push(false);\n    lineEndingIfNeeded();\n    tag('<blockquote>');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitblockquote() {\n    tightStack.pop();\n    lineEndingIfNeeded();\n    tag('</blockquote>');\n    setData('slurpAllLineEndings');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterparagraph() {\n    if (!tightStack[tightStack.length - 1]) {\n      lineEndingIfNeeded();\n      tag('<p>');\n    }\n    setData('slurpAllLineEndings');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitparagraph() {\n    if (tightStack[tightStack.length - 1]) {\n      setData('slurpAllLineEndings', true);\n    } else {\n      tag('</p>');\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodefenced() {\n    lineEndingIfNeeded();\n    tag('<pre><code');\n    setData('fencesCount', 0);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const value = resume();\n    tag(' class=\"language-' + value + '\"');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    const count = getData('fencesCount') || 0;\n    if (!count) {\n      tag('>');\n      setData('slurpOneLineEnding', true);\n    }\n    setData('fencesCount', count + 1);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodeindented() {\n    lineEndingIfNeeded();\n    tag('<pre><code>');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitflowcode() {\n    const count = getData('fencesCount');\n\n    // One special case is if we are inside a container, and the fenced code was\n    // not closed (meaning it runs to the end).\n    // In that case, the following line ending, is considered *outside* the\n    // fenced code and block quote by micromark, but CM wants to treat that\n    // ending as part of the code.\n    if (count !== undefined && count < 2 && data.tightStack.length > 0 && !getData('lastWasTag')) {\n      lineEnding();\n    }\n\n    // But in most cases, it’s simpler: when we’ve seen some data, emit an extra\n    // line ending when needed.\n    if (getData('flowCodeSeenData')) {\n      lineEndingIfNeeded();\n    }\n    tag('</code></pre>');\n    if (count !== undefined && count < 2) lineEndingIfNeeded();\n    setData('flowCodeSeenData');\n    setData('fencesCount');\n    setData('slurpOneLineEnding');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterimage() {\n    mediaStack.push({\n      image: true\n    });\n    tags = undefined; // Disallow tags.\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlink() {\n    mediaStack.push({});\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabeltext(token) {\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabel() {\n    mediaStack[mediaStack.length - 1].label = resume();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitreferencestring(token) {\n    mediaStack[mediaStack.length - 1].referenceId = this.sliceSerialize(token);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresource() {\n    buffer(); // We can have line endings in the resource, ignore them.\n    mediaStack[mediaStack.length - 1].destination = '';\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresourcedestinationstring() {\n    buffer();\n    // Ignore encoding the result, as we’ll first percent encode the url and\n    // encode manually after.\n    setData('ignoreEncode', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcedestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume();\n    setData('ignoreEncode');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcetitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitmedia() {\n    let index = mediaStack.length - 1; // Skip current.\n    const media = mediaStack[index];\n    const id = media.referenceId || media.labelId;\n    assert(id !== undefined, 'media should have `referenceId` or `labelId`');\n    assert(media.label !== undefined, 'media should have `label`');\n    const context = media.destination === undefined ? definitions[normalizeIdentifier(id)] : media;\n    tags = true;\n    while (index--) {\n      if (mediaStack[index].image) {\n        tags = undefined;\n        break;\n      }\n    }\n    if (media.image) {\n      tag('<img src=\"' + sanitizeUri(context.destination, settings.allowDangerousProtocol ? undefined : protocolSource) + '\" alt=\"');\n      raw(media.label);\n      tag('\"');\n    } else {\n      tag('<a href=\"' + sanitizeUri(context.destination, settings.allowDangerousProtocol ? undefined : protocolHref) + '\"');\n    }\n    tag(context.title ? ' title=\"' + context.title + '\"' : '');\n    if (media.image) {\n      tag(' />');\n    } else {\n      tag('>');\n      raw(media.label);\n      tag('</a>');\n    }\n    mediaStack.pop();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinition() {\n    buffer();\n    mediaStack.push({});\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    // Discard label, use the source content instead.\n    resume();\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinitiondestinationstring() {\n    buffer();\n    setData('ignoreEncode', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume();\n    setData('ignoreEncode');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinition() {\n    const media = mediaStack[mediaStack.length - 1];\n    assert(media.labelId !== undefined, 'media should have `labelId`');\n    const id = normalizeIdentifier(media.labelId);\n    resume();\n    if (!hasOwnProperty.call(definitions, id)) {\n      definitions[id] = mediaStack[mediaStack.length - 1];\n    }\n    mediaStack.pop();\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercontent() {\n    setData('slurpAllLineEndings', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    // Exit for further sequences.\n    if (getData('headingRank')) return;\n    setData('headingRank', this.sliceSerialize(token).length);\n    lineEndingIfNeeded();\n    tag('<h' + getData('headingRank') + '>');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentersetextheading() {\n    buffer();\n    setData('slurpAllLineEndings');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    setData('slurpAllLineEndings', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheading() {\n    tag('</h' + getData('headingRank') + '>');\n    setData('headingRank');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    setData('headingRank', this.sliceSerialize(token).charCodeAt(0) === codes.equalsTo ? 1 : 2);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    const value = resume();\n    lineEndingIfNeeded();\n    tag('<h' + getData('headingRank') + '>');\n    raw(value);\n    tag('</h' + getData('headingRank') + '>');\n    setData('slurpAllLineEndings');\n    setData('headingRank');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdata(token) {\n    raw(encode(this.sliceSerialize(token)));\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlineending(token) {\n    if (getData('slurpAllLineEndings')) {\n      return;\n    }\n    if (getData('slurpOneLineEnding')) {\n      setData('slurpOneLineEnding');\n      return;\n    }\n    if (getData('inCodeText')) {\n      raw(' ');\n      return;\n    }\n    raw(encode(this.sliceSerialize(token)));\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeflowvalue(token) {\n    raw(encode(this.sliceSerialize(token)));\n    setData('flowCodeSeenData', true);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexithardbreak() {\n    tag('<br />');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtmlflow() {\n    lineEndingIfNeeded();\n    onenterhtml();\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexithtml() {\n    setData('ignoreEncode');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtml() {\n    if (settings.allowDangerousHtml) {\n      setData('ignoreEncode', true);\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenteremphasis() {\n    tag('<em>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterstrong() {\n    tag('<strong>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onentercodetext() {\n    setData('inCodeText', true);\n    tag('<code>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitcodetext() {\n    setData('inCodeText');\n    tag('</code>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitemphasis() {\n    tag('</em>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitstrong() {\n    tag('</strong>');\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitthematicbreak() {\n    lineEndingIfNeeded();\n    tag('<hr />');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @param {Token} token\n   * @returns {undefined}\n   */\n  function onexitcharacterreferencemarker(token) {\n    setData('characterReferenceType', token.type);\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const value = this.sliceSerialize(token);\n    const decoded = getData('characterReferenceType') ? decodeNumericCharacterReference(value, getData('characterReferenceType') === types.characterReferenceMarkerNumeric ? constants.numericBaseDecimal : constants.numericBaseHexadecimal) : decodeNamedCharacterReference(value);\n\n    // `decodeNamedCharacterReference` can return `false` for invalid named\n    // character references,\n    // but everything we’ve tokenized is valid.\n    raw(encode(/** @type {string} */decoded));\n    setData('characterReferenceType');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    const uri = this.sliceSerialize(token);\n    tag('<a href=\"' + sanitizeUri(uri, settings.allowDangerousProtocol ? undefined : protocolHref) + '\">');\n    raw(encode(uri));\n    tag('</a>');\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    const uri = this.sliceSerialize(token);\n    tag('<a href=\"' + sanitizeUri('mailto:' + uri) + '\">');\n    raw(encode(uri));\n    tag('</a>');\n  }\n}", "map": {"version": 3, "names": ["decodeNamedCharacterReference", "ok", "assert", "push", "combineHtmlExtensions", "decodeNumericCharacterReference", "encode", "_encode", "normalizeIdentifier", "sanitizeUri", "codes", "constants", "types", "hasOwnProperty", "protocolHref", "protocolSource", "compile", "options", "settings", "tags", "definitions", "buffers", "mediaStack", "tightStack", "defaultHandlers", "enter", "blockQuote", "onenterblockquote", "codeFenced", "onentercodefenced", "codeFencedFenceInfo", "buffer", "codeFencedFenceMeta", "codeIndented", "onentercodeindented", "codeText", "onentercodetext", "content", "onentercontent", "definition", "onenterdefinition", "definitionDestinationString", "onenterdefinitiondestinationstring", "definitionLabelString", "definitionTitleString", "emphasis", "onenteremphasis", "htmlFlow", "onenterhtmlflow", "htmlText", "onenterhtml", "image", "onenterimage", "label", "link", "onenterlink", "listItemMarker", "onenterlistitemmarker", "listItemValue", "onenterlistitemvalue", "listOrdered", "onenterlistordered", "listUnordered", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paragraph", "onenterparagraph", "reference", "resource", "onenterresource", "resourceDestinationString", "onenterresourcedestinationstring", "resourceTitleString", "setextHeading", "onentersetextheading", "strong", "onenterstrong", "exit", "atxHeading", "onexitatxheading", "atxHeadingSequence", "onexitatxheadingsequence", "autolinkEmail", "onexitautolinkemail", "autolinkProtocol", "onexitautolinkprotocol", "onexitblockquote", "characterEscapeValue", "onexitdata", "characterReferenceMarkerHexadecimal", "onexitcharacterreferencemarker", "characterReferenceMarkerNumeric", "characterReferenceValue", "onexitcharacterreferencevalue", "onexitflowcode", "codeFencedFence", "onexitcodefencedfence", "onexitcodefencedfenceinfo", "onresumedrop", "codeFlowValue", "onexitcodeflowvalue", "onexitcodetext", "codeTextData", "data", "onexitdefinition", "onexitdefinitiondestinationstring", "onexitdefinitionlabelstring", "onexitdefinitiontitlestring", "onexitemphasis", "hardBreakEscape", "onexithardbreak", "hardBreakTrailing", "onexithtml", "htmlFlowData", "htmlTextData", "onexitmedia", "onexitlabel", "labelText", "onexitlabeltext", "lineEnding", "onexitlineending", "onexitlistordered", "onexitlist<PERSON><PERSON><PERSON>", "onexitparagraph", "referenceString", "onexitreferencestring", "onexitresourcedestinationstring", "onexitresourcetitlestring", "onexitsetextheading", "setextHeadingLineSequence", "onexitsetextheadinglinesequence", "setextHeadingText", "onexitsetextheadingtext", "onexitstrong", "thematicBreak", "onexitthematicbreak", "handlers", "htmlExtensions", "context", "getData", "lineEndingIfNeeded", "raw", "resume", "setData", "tag", "lineEndingStyle", "defaultLineEnding", "events", "index", "start", "listStack", "head", "body", "length", "type", "lineEndingBlank", "sliceSerialize", "prepareList", "slice", "pop", "result", "null", "call", "handles", "kind", "handle", "join", "containerBalance", "loose", "atMarker", "event", "_container", "undefined", "listItemPrefix", "linePrefix", "_loose", "key", "value", "buf", "previous", "charCodeAt", "eof", "lf", "cr", "token", "Number", "parseInt", "numericBaseDecimal", "String", "onexitlistitem", "count", "labelId", "referenceId", "destination", "title", "media", "id", "allowDangerousProtocol", "equalsTo", "allowDangerousHtml", "decoded", "numericBaseHexadecimal", "uri"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark/dev/lib/compile.js"], "sourcesContent": ["/**\n * While micromark is a lexer/tokenizer, the common case of going from markdown\n * to html is currently built in as this module, even though the parts can be\n * used separately to build ASTs, CSTs, or many other output formats.\n *\n * Having an HTML compiler built in is useful because it allows us to check for\n * compliancy to CommonMark, the de facto norm of markdown, specified in roughly\n * 600 input/output cases.\n *\n * This module has an interface that accepts lists of events instead of the\n * whole at once, however, because markdown can’t be truly streaming, we buffer\n * events before processing and outputting the final result.\n */\n\n/**\n * @import {\n *   CompileContext,\n *   CompileData,\n *   CompileOptions,\n *   Compile,\n *   Definition,\n *   Event,\n *   Handle,\n *   HtmlExtension,\n *   LineEnding,\n *   NormalizedHtmlExtension,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef Media\n * @property {boolean | undefined} [image]\n * @property {string | undefined} [labelId]\n * @property {string | undefined} [label]\n * @property {string | undefined} [referenceId]\n * @property {string | undefined} [destination]\n * @property {string | undefined} [title]\n */\n\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {ok as assert} from 'devlop'\nimport {push} from 'micromark-util-chunked'\nimport {combineHtmlExtensions} from 'micromark-util-combine-extensions'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {encode as _encode} from 'micromark-util-encode'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {sanitizeUri} from 'micromark-util-sanitize-uri'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\nconst hasOwnProperty = {}.hasOwnProperty\n\n/**\n * These two are allowlists of safe protocols for full URLs in respectively the\n * `href` (on `<a>`) and `src` (on `<img>`) attributes.\n * They are based on what is allowed on GitHub,\n * <https://github.com/syntax-tree/hast-util-sanitize/blob/9275b21/lib/github.json#L31>\n */\nconst protocolHref = /^(https?|ircs?|mailto|xmpp)$/i\nconst protocolSource = /^https?$/i\n\n/**\n * @param {CompileOptions | null | undefined} [options]\n * @returns {Compile}\n */\nexport function compile(options) {\n  const settings = options || {}\n\n  /**\n   * Tags is needed because according to markdown, links and emphasis and\n   * whatnot can exist in images, however, as HTML doesn’t allow content in\n   * images, the tags are ignored in the `alt` attribute, but the content\n   * remains.\n   *\n   * @type {boolean | undefined}\n   */\n  let tags = true\n\n  /**\n   * An object to track identifiers to media (URLs and titles) defined with\n   * definitions.\n   *\n   * @type {Record<string, Definition>}\n   */\n  const definitions = {}\n\n  /**\n   * A lot of the handlers need to capture some of the output data, modify it\n   * somehow, and then deal with it.\n   * We do that by tracking a stack of buffers, that can be opened (with\n   * `buffer`) and closed (with `resume`) to access them.\n   *\n   * @type {Array<Array<string>>}\n   */\n  const buffers = [[]]\n\n  /**\n   * As we can have links in images and the other way around, where the deepest\n   * ones are closed first, we need to track which one we’re in.\n   *\n   * @type {Array<Media>}\n   */\n  const mediaStack = []\n\n  /**\n   * Same as `mediaStack` for tightness, which is specific to lists.\n   * We need to track if we’re currently in a tight or loose container.\n   *\n   * @type {Array<boolean>}\n   */\n  const tightStack = []\n\n  /** @type {HtmlExtension} */\n  const defaultHandlers = {\n    enter: {\n      blockQuote: onenterblockquote,\n      codeFenced: onentercodefenced,\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: onentercodeindented,\n      codeText: onentercodetext,\n      content: onentercontent,\n      definition: onenterdefinition,\n      definitionDestinationString: onenterdefinitiondestinationstring,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: onenteremphasis,\n      htmlFlow: onenterhtmlflow,\n      htmlText: onenterhtml,\n      image: onenterimage,\n      label: buffer,\n      link: onenterlink,\n      listItemMarker: onenterlistitemmarker,\n      listItemValue: onenterlistitemvalue,\n      listOrdered: onenterlistordered,\n      listUnordered: onenterlistunordered,\n      paragraph: onenterparagraph,\n      reference: buffer,\n      resource: onenterresource,\n      resourceDestinationString: onenterresourcedestinationstring,\n      resourceTitleString: buffer,\n      setextHeading: onentersetextheading,\n      strong: onenterstrong\n    },\n    exit: {\n      atxHeading: onexitatxheading,\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: onexitblockquote,\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      codeFenced: onexitflowcode,\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onresumedrop,\n      codeFlowValue: onexitcodeflowvalue,\n      codeIndented: onexitflowcode,\n      codeText: onexitcodetext,\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: onexitdefinition,\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: onexitemphasis,\n      hardBreakEscape: onexithardbreak,\n      hardBreakTrailing: onexithardbreak,\n      htmlFlow: onexithtml,\n      htmlFlowData: onexitdata,\n      htmlText: onexithtml,\n      htmlTextData: onexitdata,\n      image: onexitmedia,\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: onexitmedia,\n      listOrdered: onexitlistordered,\n      listUnordered: onexitlistunordered,\n      paragraph: onexitparagraph,\n      reference: onresumedrop,\n      referenceString: onexitreferencestring,\n      resource: onresumedrop,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      setextHeading: onexitsetextheading,\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: onexitstrong,\n      thematicBreak: onexitthematicbreak\n    }\n  }\n\n  /**\n   * Combine the HTML extensions with the default handlers.\n   * An HTML extension is an object whose fields are either `enter` or `exit`\n   * (reflecting whether a token is entered or exited).\n   * The values at such objects are names of tokens mapping to handlers.\n   * Handlers are called, respectively when a token is opener or closed, with\n   * that token, and a context as `this`.\n   */\n  const handlers = /** @type {NormalizedHtmlExtension} */ (\n    combineHtmlExtensions([defaultHandlers, ...(settings.htmlExtensions || [])])\n  )\n\n  /**\n   * Handlers do often need to keep track of some state.\n   * That state is provided here as a key-value store (an object).\n   *\n   * @type {CompileData}\n   */\n  const data = {\n    definitions,\n    tightStack\n  }\n\n  /**\n   * The context for handlers references a couple of useful functions.\n   * In handlers from extensions, those can be accessed at `this`.\n   * For the handlers here, they can be accessed directly.\n   *\n   * @type {Omit<CompileContext, 'sliceSerialize'>}\n   */\n  const context = {\n    buffer,\n    encode,\n    getData,\n    lineEndingIfNeeded,\n    options: settings,\n    raw,\n    resume,\n    setData,\n    tag\n  }\n\n  /**\n   * Generally, micromark copies line endings (`'\\r'`, `'\\n'`, `'\\r\\n'`) in the\n   * markdown document over to the compiled HTML.\n   * In some cases, such as `> a`, CommonMark requires that extra line endings\n   * are added: `<blockquote>\\n<p>a</p>\\n</blockquote>`.\n   * This variable hold the default line ending when given (or `undefined`),\n   * and in the latter case will be updated to the first found line ending if\n   * there is one.\n   */\n  let lineEndingStyle = settings.defaultLineEnding\n\n  // Return the function that handles a slice of events.\n  return compile\n\n  /**\n   * Deal w/ a slice of events.\n   * Return either the empty string if there’s nothing of note to return, or the\n   * result when done.\n   *\n   * @param {ReadonlyArray<Event>} events\n   * @returns {string}\n   */\n  function compile(events) {\n    let index = -1\n    let start = 0\n    /** @type {Array<number>} */\n    const listStack = []\n    // As definitions can come after references, we need to figure out the media\n    // (urls and titles) defined by them before handling the references.\n    // So, we do sort of what HTML does: put metadata at the start (in head), and\n    // then put content after (`body`).\n    /** @type {Array<Event>} */\n    let head = []\n    /** @type {Array<Event>} */\n    let body = []\n\n    while (++index < events.length) {\n      // Figure out the line ending style used in the document.\n      if (\n        !lineEndingStyle &&\n        (events[index][1].type === types.lineEnding ||\n          events[index][1].type === types.lineEndingBlank)\n      ) {\n        lineEndingStyle = /** @type {LineEnding} */ (\n          events[index][2].sliceSerialize(events[index][1])\n        )\n      }\n\n      // Preprocess lists to infer whether the list is loose or not.\n      if (\n        events[index][1].type === types.listOrdered ||\n        events[index][1].type === types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          prepareList(events.slice(listStack.pop(), index))\n        }\n      }\n\n      // Move definitions to the front.\n      if (events[index][1].type === types.definition) {\n        if (events[index][0] === 'enter') {\n          body = push(body, events.slice(start, index))\n          start = index\n        } else {\n          head = push(head, events.slice(start, index + 1))\n          start = index + 1\n        }\n      }\n    }\n\n    head = push(head, body)\n    head = push(head, events.slice(start))\n    index = -1\n    const result = head\n\n    // Handle the start of the document, if defined.\n    if (handlers.enter.null) {\n      handlers.enter.null.call(context)\n    }\n\n    // Handle all events.\n    while (++index < events.length) {\n      const handles = handlers[result[index][0]]\n      const kind = result[index][1].type\n      const handle = handles[kind]\n\n      if (hasOwnProperty.call(handles, kind) && handle) {\n        handle.call(\n          {sliceSerialize: result[index][2].sliceSerialize, ...context},\n          result[index][1]\n        )\n      }\n    }\n\n    // Handle the end of the document, if defined.\n    if (handlers.exit.null) {\n      handlers.exit.null.call(context)\n    }\n\n    return buffers[0].join('')\n  }\n\n  /**\n   * Figure out whether lists are loose or not.\n   *\n   * @param {ReadonlyArray<Event>} slice\n   * @returns {undefined}\n   */\n  function prepareList(slice) {\n    const length = slice.length\n    let index = 0 // Skip open.\n    let containerBalance = 0\n    let loose = false\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index < length) {\n      const event = slice[index]\n\n      if (event[1]._container) {\n        atMarker = undefined\n\n        if (event[0] === 'enter') {\n          containerBalance++\n        } else {\n          containerBalance--\n        }\n      } else\n        switch (event[1].type) {\n          case types.listItemPrefix: {\n            if (event[0] === 'exit') {\n              atMarker = true\n            }\n\n            break\n          }\n\n          case types.linePrefix: {\n            // Ignore\n\n            break\n          }\n\n          case types.lineEndingBlank: {\n            if (event[0] === 'enter' && !containerBalance) {\n              if (atMarker) {\n                atMarker = undefined\n              } else {\n                loose = true\n              }\n            }\n\n            break\n          }\n\n          default: {\n            atMarker = undefined\n          }\n        }\n    }\n\n    slice[0][1]._loose = loose\n  }\n\n  /**\n   * @type {CompileContext['setData']}\n   */\n  function setData(key, value) {\n    // @ts-expect-error: assume `value` is omitted (`undefined` is passed) only\n    // if allowed.\n    data[key] = value\n  }\n\n  /**\n   * @type {CompileContext['getData']}\n   */\n  function getData(key) {\n    return data[key]\n  }\n\n  /** @type {CompileContext['buffer']} */\n  function buffer() {\n    buffers.push([])\n  }\n\n  /** @type {CompileContext['resume']} */\n  function resume() {\n    const buf = buffers.pop()\n    assert(buf !== undefined, 'Cannot resume w/o buffer')\n    return buf.join('')\n  }\n\n  /** @type {CompileContext['tag']} */\n  function tag(value) {\n    if (!tags) return\n    setData('lastWasTag', true)\n    buffers[buffers.length - 1].push(value)\n  }\n\n  /** @type {CompileContext['raw']} */\n  function raw(value) {\n    setData('lastWasTag')\n    buffers[buffers.length - 1].push(value)\n  }\n\n  /**\n   * Output an extra line ending.\n   *\n   * @returns {undefined}\n   */\n  function lineEnding() {\n    raw(lineEndingStyle || '\\n')\n  }\n\n  /** @type {CompileContext['lineEndingIfNeeded']} */\n  function lineEndingIfNeeded() {\n    const buffer = buffers[buffers.length - 1]\n    const slice = buffer[buffer.length - 1]\n    const previous = slice ? slice.charCodeAt(slice.length - 1) : codes.eof\n\n    if (\n      previous === codes.lf ||\n      previous === codes.cr ||\n      previous === codes.eof\n    ) {\n      return\n    }\n\n    lineEnding()\n  }\n\n  /** @type {CompileContext['encode']} */\n  function encode(value) {\n    return getData('ignoreEncode') ? value : _encode(value)\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @returns {undefined}\n   */\n  function onresumedrop() {\n    resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered(token) {\n    tightStack.push(!token._loose)\n    lineEndingIfNeeded()\n    tag('<ol')\n    setData('expectFirstItem', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistunordered(token) {\n    tightStack.push(!token._loose)\n    lineEndingIfNeeded()\n    tag('<ul')\n    setData('expectFirstItem', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (getData('expectFirstItem')) {\n      const value = Number.parseInt(\n        this.sliceSerialize(token),\n        constants.numericBaseDecimal\n      )\n\n      if (value !== 1) {\n        tag(' start=\"' + encode(String(value)) + '\"')\n      }\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterlistitemmarker() {\n    if (getData('expectFirstItem')) {\n      tag('>')\n    } else {\n      onexitlistitem()\n    }\n\n    lineEndingIfNeeded()\n    tag('<li>')\n    setData('expectFirstItem')\n    // “Hack” to prevent a line ending from showing up if the item is empty.\n    setData('lastWasTag')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistordered() {\n    onexitlistitem()\n    tightStack.pop()\n    lineEnding()\n    tag('</ol>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistunordered() {\n    onexitlistitem()\n    tightStack.pop()\n    lineEnding()\n    tag('</ul>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistitem() {\n    if (getData('lastWasTag') && !getData('slurpAllLineEndings')) {\n      lineEndingIfNeeded()\n    }\n\n    tag('</li>')\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterblockquote() {\n    tightStack.push(false)\n    lineEndingIfNeeded()\n    tag('<blockquote>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitblockquote() {\n    tightStack.pop()\n    lineEndingIfNeeded()\n    tag('</blockquote>')\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterparagraph() {\n    if (!tightStack[tightStack.length - 1]) {\n      lineEndingIfNeeded()\n      tag('<p>')\n    }\n\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitparagraph() {\n    if (tightStack[tightStack.length - 1]) {\n      setData('slurpAllLineEndings', true)\n    } else {\n      tag('</p>')\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodefenced() {\n    lineEndingIfNeeded()\n    tag('<pre><code')\n    setData('fencesCount', 0)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const value = resume()\n    tag(' class=\"language-' + value + '\"')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    const count = getData('fencesCount') || 0\n\n    if (!count) {\n      tag('>')\n      setData('slurpOneLineEnding', true)\n    }\n\n    setData('fencesCount', count + 1)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodeindented() {\n    lineEndingIfNeeded()\n    tag('<pre><code>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitflowcode() {\n    const count = getData('fencesCount')\n\n    // One special case is if we are inside a container, and the fenced code was\n    // not closed (meaning it runs to the end).\n    // In that case, the following line ending, is considered *outside* the\n    // fenced code and block quote by micromark, but CM wants to treat that\n    // ending as part of the code.\n    if (\n      count !== undefined &&\n      count < 2 &&\n      data.tightStack.length > 0 &&\n      !getData('lastWasTag')\n    ) {\n      lineEnding()\n    }\n\n    // But in most cases, it’s simpler: when we’ve seen some data, emit an extra\n    // line ending when needed.\n    if (getData('flowCodeSeenData')) {\n      lineEndingIfNeeded()\n    }\n\n    tag('</code></pre>')\n    if (count !== undefined && count < 2) lineEndingIfNeeded()\n    setData('flowCodeSeenData')\n    setData('fencesCount')\n    setData('slurpOneLineEnding')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterimage() {\n    mediaStack.push({image: true})\n    tags = undefined // Disallow tags.\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlink() {\n    mediaStack.push({})\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabeltext(token) {\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabel() {\n    mediaStack[mediaStack.length - 1].label = resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitreferencestring(token) {\n    mediaStack[mediaStack.length - 1].referenceId = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresource() {\n    buffer() // We can have line endings in the resource, ignore them.\n    mediaStack[mediaStack.length - 1].destination = ''\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresourcedestinationstring() {\n    buffer()\n    // Ignore encoding the result, as we’ll first percent encode the url and\n    // encode manually after.\n    setData('ignoreEncode', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcedestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume()\n    setData('ignoreEncode')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcetitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitmedia() {\n    let index = mediaStack.length - 1 // Skip current.\n    const media = mediaStack[index]\n    const id = media.referenceId || media.labelId\n    assert(id !== undefined, 'media should have `referenceId` or `labelId`')\n    assert(media.label !== undefined, 'media should have `label`')\n    const context =\n      media.destination === undefined\n        ? definitions[normalizeIdentifier(id)]\n        : media\n\n    tags = true\n\n    while (index--) {\n      if (mediaStack[index].image) {\n        tags = undefined\n        break\n      }\n    }\n\n    if (media.image) {\n      tag(\n        '<img src=\"' +\n          sanitizeUri(\n            context.destination,\n            settings.allowDangerousProtocol ? undefined : protocolSource\n          ) +\n          '\" alt=\"'\n      )\n      raw(media.label)\n      tag('\"')\n    } else {\n      tag(\n        '<a href=\"' +\n          sanitizeUri(\n            context.destination,\n            settings.allowDangerousProtocol ? undefined : protocolHref\n          ) +\n          '\"'\n      )\n    }\n\n    tag(context.title ? ' title=\"' + context.title + '\"' : '')\n\n    if (media.image) {\n      tag(' />')\n    } else {\n      tag('>')\n      raw(media.label)\n      tag('</a>')\n    }\n\n    mediaStack.pop()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinition() {\n    buffer()\n    mediaStack.push({})\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    // Discard label, use the source content instead.\n    resume()\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinitiondestinationstring() {\n    buffer()\n    setData('ignoreEncode', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume()\n    setData('ignoreEncode')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinition() {\n    const media = mediaStack[mediaStack.length - 1]\n    assert(media.labelId !== undefined, 'media should have `labelId`')\n    const id = normalizeIdentifier(media.labelId)\n\n    resume()\n\n    if (!hasOwnProperty.call(definitions, id)) {\n      definitions[id] = mediaStack[mediaStack.length - 1]\n    }\n\n    mediaStack.pop()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercontent() {\n    setData('slurpAllLineEndings', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    // Exit for further sequences.\n    if (getData('headingRank')) return\n    setData('headingRank', this.sliceSerialize(token).length)\n    lineEndingIfNeeded()\n    tag('<h' + getData('headingRank') + '>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentersetextheading() {\n    buffer()\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    setData('slurpAllLineEndings', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheading() {\n    tag('</h' + getData('headingRank') + '>')\n    setData('headingRank')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    setData(\n      'headingRank',\n      this.sliceSerialize(token).charCodeAt(0) === codes.equalsTo ? 1 : 2\n    )\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    const value = resume()\n    lineEndingIfNeeded()\n    tag('<h' + getData('headingRank') + '>')\n    raw(value)\n    tag('</h' + getData('headingRank') + '>')\n    setData('slurpAllLineEndings')\n    setData('headingRank')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdata(token) {\n    raw(encode(this.sliceSerialize(token)))\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlineending(token) {\n    if (getData('slurpAllLineEndings')) {\n      return\n    }\n\n    if (getData('slurpOneLineEnding')) {\n      setData('slurpOneLineEnding')\n      return\n    }\n\n    if (getData('inCodeText')) {\n      raw(' ')\n      return\n    }\n\n    raw(encode(this.sliceSerialize(token)))\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeflowvalue(token) {\n    raw(encode(this.sliceSerialize(token)))\n    setData('flowCodeSeenData', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexithardbreak() {\n    tag('<br />')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtmlflow() {\n    lineEndingIfNeeded()\n    onenterhtml()\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexithtml() {\n    setData('ignoreEncode')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtml() {\n    if (settings.allowDangerousHtml) {\n      setData('ignoreEncode', true)\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenteremphasis() {\n    tag('<em>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterstrong() {\n    tag('<strong>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onentercodetext() {\n    setData('inCodeText', true)\n    tag('<code>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitcodetext() {\n    setData('inCodeText')\n    tag('</code>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitemphasis() {\n    tag('</em>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitstrong() {\n    tag('</strong>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitthematicbreak() {\n    lineEndingIfNeeded()\n    tag('<hr />')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @param {Token} token\n   * @returns {undefined}\n   */\n  function onexitcharacterreferencemarker(token) {\n    setData('characterReferenceType', token.type)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const value = this.sliceSerialize(token)\n    const decoded = getData('characterReferenceType')\n      ? decodeNumericCharacterReference(\n          value,\n          getData('characterReferenceType') ===\n            types.characterReferenceMarkerNumeric\n            ? constants.numericBaseDecimal\n            : constants.numericBaseHexadecimal\n        )\n      : decodeNamedCharacterReference(value)\n\n    // `decodeNamedCharacterReference` can return `false` for invalid named\n    // character references,\n    // but everything we’ve tokenized is valid.\n    raw(encode(/** @type {string} */ (decoded)))\n    setData('characterReferenceType')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    const uri = this.sliceSerialize(token)\n    tag(\n      '<a href=\"' +\n        sanitizeUri(\n          uri,\n          settings.allowDangerousProtocol ? undefined : protocolHref\n        ) +\n        '\">'\n    )\n    raw(encode(uri))\n    tag('</a>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    const uri = this.sliceSerialize(token)\n    tag('<a href=\"' + sanitizeUri('mailto:' + uri) + '\">')\n    raw(encode(uri))\n    tag('</a>')\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,6BAA6B,QAAO,kCAAkC;AAC9E,SAAQC,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,IAAI,QAAO,wBAAwB;AAC3C,SAAQC,qBAAqB,QAAO,mCAAmC;AACvE,SAAQC,+BAA+B,QAAO,mDAAmD;AACjG,SAAQC,MAAM,IAAIC,OAAO,QAAO,uBAAuB;AACvD,SAAQC,mBAAmB,QAAO,qCAAqC;AACvE,SAAQC,WAAW,QAAO,6BAA6B;AACvD,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;AAE7D,MAAMC,cAAc,GAAG,CAAC,CAAC,CAACA,cAAc;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,+BAA+B;AACpD,MAAMC,cAAc,GAAG,WAAW;;AAElC;AACA;AACA;AACA;AACA,OAAO,SAASC,OAAOA,CAACC,OAAO,EAAE;EAC/B,MAAMC,QAAQ,GAAGD,OAAO,IAAI,CAAC,CAAC;;EAE9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIE,IAAI,GAAG,IAAI;;EAEf;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,WAAW,GAAG,CAAC,CAAC;;EAEtB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,OAAO,GAAG,CAAC,EAAE,CAAC;;EAEpB;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,UAAU,GAAG,EAAE;;EAErB;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,UAAU,GAAG,EAAE;;EAErB;EACA,MAAMC,eAAe,GAAG;IACtBC,KAAK,EAAE;MACLC,UAAU,EAAEC,iBAAiB;MAC7BC,UAAU,EAAEC,iBAAiB;MAC7BC,mBAAmB,EAAEC,MAAM;MAC3BC,mBAAmB,EAAED,MAAM;MAC3BE,YAAY,EAAEC,mBAAmB;MACjCC,QAAQ,EAAEC,eAAe;MACzBC,OAAO,EAAEC,cAAc;MACvBC,UAAU,EAAEC,iBAAiB;MAC7BC,2BAA2B,EAAEC,kCAAkC;MAC/DC,qBAAqB,EAAEZ,MAAM;MAC7Ba,qBAAqB,EAAEb,MAAM;MAC7Bc,QAAQ,EAAEC,eAAe;MACzBC,QAAQ,EAAEC,eAAe;MACzBC,QAAQ,EAAEC,WAAW;MACrBC,KAAK,EAAEC,YAAY;MACnBC,KAAK,EAAEtB,MAAM;MACbuB,IAAI,EAAEC,WAAW;MACjBC,cAAc,EAAEC,qBAAqB;MACrCC,aAAa,EAAEC,oBAAoB;MACnCC,WAAW,EAAEC,kBAAkB;MAC/BC,aAAa,EAAEC,oBAAoB;MACnCC,SAAS,EAAEC,gBAAgB;MAC3BC,SAAS,EAAEnC,MAAM;MACjBoC,QAAQ,EAAEC,eAAe;MACzBC,yBAAyB,EAAEC,gCAAgC;MAC3DC,mBAAmB,EAAExC,MAAM;MAC3ByC,aAAa,EAAEC,oBAAoB;MACnCC,MAAM,EAAEC;IACV,CAAC;IACDC,IAAI,EAAE;MACJC,UAAU,EAAEC,gBAAgB;MAC5BC,kBAAkB,EAAEC,wBAAwB;MAC5CC,aAAa,EAAEC,mBAAmB;MAClCC,gBAAgB,EAAEC,sBAAsB;MACxC1D,UAAU,EAAE2D,gBAAgB;MAC5BC,oBAAoB,EAAEC,UAAU;MAChCC,mCAAmC,EAAEC,8BAA8B;MACnEC,+BAA+B,EAAED,8BAA8B;MAC/DE,uBAAuB,EAAEC,6BAA6B;MACtDhE,UAAU,EAAEiE,cAAc;MAC1BC,eAAe,EAAEC,qBAAqB;MACtCjE,mBAAmB,EAAEkE,yBAAyB;MAC9ChE,mBAAmB,EAAEiE,YAAY;MACjCC,aAAa,EAAEC,mBAAmB;MAClClE,YAAY,EAAE4D,cAAc;MAC5B1D,QAAQ,EAAEiE,cAAc;MACxBC,YAAY,EAAEd,UAAU;MACxBe,IAAI,EAAEf,UAAU;MAChBhD,UAAU,EAAEgE,gBAAgB;MAC5B9D,2BAA2B,EAAE+D,iCAAiC;MAC9D7D,qBAAqB,EAAE8D,2BAA2B;MAClD7D,qBAAqB,EAAE8D,2BAA2B;MAClD7D,QAAQ,EAAE8D,cAAc;MACxBC,eAAe,EAAEC,eAAe;MAChCC,iBAAiB,EAAED,eAAe;MAClC9D,QAAQ,EAAEgE,UAAU;MACpBC,YAAY,EAAEzB,UAAU;MACxBtC,QAAQ,EAAE8D,UAAU;MACpBE,YAAY,EAAE1B,UAAU;MACxBpC,KAAK,EAAE+D,WAAW;MAClB7D,KAAK,EAAE8D,WAAW;MAClBC,SAAS,EAAEC,eAAe;MAC1BC,UAAU,EAAEC,gBAAgB;MAC5BjE,IAAI,EAAE4D,WAAW;MACjBtD,WAAW,EAAE4D,iBAAiB;MAC9B1D,aAAa,EAAE2D,mBAAmB;MAClCzD,SAAS,EAAE0D,eAAe;MAC1BxD,SAAS,EAAE+B,YAAY;MACvB0B,eAAe,EAAEC,qBAAqB;MACtCzD,QAAQ,EAAE8B,YAAY;MACtB5B,yBAAyB,EAAEwD,+BAA+B;MAC1DtD,mBAAmB,EAAEuD,yBAAyB;MAC9CtD,aAAa,EAAEuD,mBAAmB;MAClCC,yBAAyB,EAAEC,+BAA+B;MAC1DC,iBAAiB,EAAEC,uBAAuB;MAC1CzD,MAAM,EAAE0D,YAAY;MACpBC,aAAa,EAAEC;IACjB;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,MAAMC,QAAQ,GAAG;EACfnI,qBAAqB,CAAC,CAACoB,eAAe,EAAE,IAAIN,QAAQ,CAACsH,cAAc,IAAI,EAAE,CAAC,CAAC,CAC5E;;EAED;AACF;AACA;AACA;AACA;AACA;EACE,MAAMlC,IAAI,GAAG;IACXlF,WAAW;IACXG;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMkH,OAAO,GAAG;IACd1G,MAAM;IACNzB,MAAM;IACNoI,OAAO;IACPC,kBAAkB;IAClB1H,OAAO,EAAEC,QAAQ;IACjB0H,GAAG;IACHC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIC,eAAe,GAAG9H,QAAQ,CAAC+H,iBAAiB;;EAEhD;EACA,OAAOjI,OAAO;;EAEd;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,OAAOA,CAACkI,MAAM,EAAE;IACvB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,IAAIC,KAAK,GAAG,CAAC;IACb;IACA,MAAMC,SAAS,GAAG,EAAE;IACpB;IACA;IACA;IACA;IACA;IACA,IAAIC,IAAI,GAAG,EAAE;IACb;IACA,IAAIC,IAAI,GAAG,EAAE;IAEb,OAAO,EAAEJ,KAAK,GAAGD,MAAM,CAACM,MAAM,EAAE;MAC9B;MACA,IACE,CAACR,eAAe,KACfE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,KAAK7I,KAAK,CAAC0G,UAAU,IACzC4B,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,KAAK7I,KAAK,CAAC8I,eAAe,CAAC,EAClD;QACAV,eAAe,GAAG;QAChBE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACQ,cAAc,CAACT,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CACjD;MACH;;MAEA;MACA,IACED,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,KAAK7I,KAAK,CAACgD,WAAW,IAC3CsF,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,KAAK7I,KAAK,CAACkD,aAAa,EAC7C;QACA,IAAIoF,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UAChCE,SAAS,CAAClJ,IAAI,CAACgJ,KAAK,CAAC;QACvB,CAAC,MAAM;UACLS,WAAW,CAACV,MAAM,CAACW,KAAK,CAACR,SAAS,CAACS,GAAG,CAAC,CAAC,EAAEX,KAAK,CAAC,CAAC;QACnD;MACF;;MAEA;MACA,IAAID,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI,KAAK7I,KAAK,CAAC2B,UAAU,EAAE;QAC9C,IAAI2G,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UAChCI,IAAI,GAAGpJ,IAAI,CAACoJ,IAAI,EAAEL,MAAM,CAACW,KAAK,CAACT,KAAK,EAAED,KAAK,CAAC,CAAC;UAC7CC,KAAK,GAAGD,KAAK;QACf,CAAC,MAAM;UACLG,IAAI,GAAGnJ,IAAI,CAACmJ,IAAI,EAAEJ,MAAM,CAACW,KAAK,CAACT,KAAK,EAAED,KAAK,GAAG,CAAC,CAAC,CAAC;UACjDC,KAAK,GAAGD,KAAK,GAAG,CAAC;QACnB;MACF;IACF;IAEAG,IAAI,GAAGnJ,IAAI,CAACmJ,IAAI,EAAEC,IAAI,CAAC;IACvBD,IAAI,GAAGnJ,IAAI,CAACmJ,IAAI,EAAEJ,MAAM,CAACW,KAAK,CAACT,KAAK,CAAC,CAAC;IACtCD,KAAK,GAAG,CAAC,CAAC;IACV,MAAMY,MAAM,GAAGT,IAAI;;IAEnB;IACA,IAAIf,QAAQ,CAAC9G,KAAK,CAACuI,IAAI,EAAE;MACvBzB,QAAQ,CAAC9G,KAAK,CAACuI,IAAI,CAACC,IAAI,CAACxB,OAAO,CAAC;IACnC;;IAEA;IACA,OAAO,EAAEU,KAAK,GAAGD,MAAM,CAACM,MAAM,EAAE;MAC9B,MAAMU,OAAO,GAAG3B,QAAQ,CAACwB,MAAM,CAACZ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1C,MAAMgB,IAAI,GAAGJ,MAAM,CAACZ,KAAK,CAAC,CAAC,CAAC,CAAC,CAACM,IAAI;MAClC,MAAMW,MAAM,GAAGF,OAAO,CAACC,IAAI,CAAC;MAE5B,IAAItJ,cAAc,CAACoJ,IAAI,CAACC,OAAO,EAAEC,IAAI,CAAC,IAAIC,MAAM,EAAE;QAChDA,MAAM,CAACH,IAAI,CACT;UAACN,cAAc,EAAEI,MAAM,CAACZ,KAAK,CAAC,CAAC,CAAC,CAAC,CAACQ,cAAc;UAAE,GAAGlB;QAAO,CAAC,EAC7DsB,MAAM,CAACZ,KAAK,CAAC,CAAC,CAAC,CACjB,CAAC;MACH;IACF;;IAEA;IACA,IAAIZ,QAAQ,CAAC3D,IAAI,CAACoF,IAAI,EAAE;MACtBzB,QAAQ,CAAC3D,IAAI,CAACoF,IAAI,CAACC,IAAI,CAACxB,OAAO,CAAC;IAClC;IAEA,OAAOpH,OAAO,CAAC,CAAC,CAAC,CAACgJ,IAAI,CAAC,EAAE,CAAC;EAC5B;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAST,WAAWA,CAACC,KAAK,EAAE;IAC1B,MAAML,MAAM,GAAGK,KAAK,CAACL,MAAM;IAC3B,IAAIL,KAAK,GAAG,CAAC,EAAC;IACd,IAAImB,gBAAgB,GAAG,CAAC;IACxB,IAAIC,KAAK,GAAG,KAAK;IACjB;IACA,IAAIC,QAAQ;IAEZ,OAAO,EAAErB,KAAK,GAAGK,MAAM,EAAE;MACvB,MAAMiB,KAAK,GAAGZ,KAAK,CAACV,KAAK,CAAC;MAE1B,IAAIsB,KAAK,CAAC,CAAC,CAAC,CAACC,UAAU,EAAE;QACvBF,QAAQ,GAAGG,SAAS;QAEpB,IAAIF,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UACxBH,gBAAgB,EAAE;QACpB,CAAC,MAAM;UACLA,gBAAgB,EAAE;QACpB;MACF,CAAC,MACC,QAAQG,KAAK,CAAC,CAAC,CAAC,CAAChB,IAAI;QACnB,KAAK7I,KAAK,CAACgK,cAAc;UAAE;YACzB,IAAIH,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;cACvBD,QAAQ,GAAG,IAAI;YACjB;YAEA;UACF;QAEA,KAAK5J,KAAK,CAACiK,UAAU;UAAE;YACrB;;YAEA;UACF;QAEA,KAAKjK,KAAK,CAAC8I,eAAe;UAAE;YAC1B,IAAIe,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,CAACH,gBAAgB,EAAE;cAC7C,IAAIE,QAAQ,EAAE;gBACZA,QAAQ,GAAGG,SAAS;cACtB,CAAC,MAAM;gBACLJ,KAAK,GAAG,IAAI;cACd;YACF;YAEA;UACF;QAEA;UAAS;YACPC,QAAQ,GAAGG,SAAS;UACtB;MACF;IACJ;IAEAd,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACiB,MAAM,GAAGP,KAAK;EAC5B;;EAEA;AACF;AACA;EACE,SAASzB,OAAOA,CAACiC,GAAG,EAAEC,KAAK,EAAE;IAC3B;IACA;IACA1E,IAAI,CAACyE,GAAG,CAAC,GAAGC,KAAK;EACnB;;EAEA;AACF;AACA;EACE,SAAStC,OAAOA,CAACqC,GAAG,EAAE;IACpB,OAAOzE,IAAI,CAACyE,GAAG,CAAC;EAClB;;EAEA;EACA,SAAShJ,MAAMA,CAAA,EAAG;IAChBV,OAAO,CAAClB,IAAI,CAAC,EAAE,CAAC;EAClB;;EAEA;EACA,SAAS0I,MAAMA,CAAA,EAAG;IAChB,MAAMoC,GAAG,GAAG5J,OAAO,CAACyI,GAAG,CAAC,CAAC;IACzB5J,MAAM,CAAC+K,GAAG,KAAKN,SAAS,EAAE,0BAA0B,CAAC;IACrD,OAAOM,GAAG,CAACZ,IAAI,CAAC,EAAE,CAAC;EACrB;;EAEA;EACA,SAAStB,GAAGA,CAACiC,KAAK,EAAE;IAClB,IAAI,CAAC7J,IAAI,EAAE;IACX2H,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC;IAC3BzH,OAAO,CAACA,OAAO,CAACmI,MAAM,GAAG,CAAC,CAAC,CAACrJ,IAAI,CAAC6K,KAAK,CAAC;EACzC;;EAEA;EACA,SAASpC,GAAGA,CAACoC,KAAK,EAAE;IAClBlC,OAAO,CAAC,YAAY,CAAC;IACrBzH,OAAO,CAACA,OAAO,CAACmI,MAAM,GAAG,CAAC,CAAC,CAACrJ,IAAI,CAAC6K,KAAK,CAAC;EACzC;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS1D,UAAUA,CAAA,EAAG;IACpBsB,GAAG,CAACI,eAAe,IAAI,IAAI,CAAC;EAC9B;;EAEA;EACA,SAASL,kBAAkBA,CAAA,EAAG;IAC5B,MAAM5G,MAAM,GAAGV,OAAO,CAACA,OAAO,CAACmI,MAAM,GAAG,CAAC,CAAC;IAC1C,MAAMK,KAAK,GAAG9H,MAAM,CAACA,MAAM,CAACyH,MAAM,GAAG,CAAC,CAAC;IACvC,MAAM0B,QAAQ,GAAGrB,KAAK,GAAGA,KAAK,CAACsB,UAAU,CAACtB,KAAK,CAACL,MAAM,GAAG,CAAC,CAAC,GAAG9I,KAAK,CAAC0K,GAAG;IAEvE,IACEF,QAAQ,KAAKxK,KAAK,CAAC2K,EAAE,IACrBH,QAAQ,KAAKxK,KAAK,CAAC4K,EAAE,IACrBJ,QAAQ,KAAKxK,KAAK,CAAC0K,GAAG,EACtB;MACA;IACF;IAEA9D,UAAU,CAAC,CAAC;EACd;;EAEA;EACA,SAAShH,MAAMA,CAAC0K,KAAK,EAAE;IACrB,OAAOtC,OAAO,CAAC,cAAc,CAAC,GAAGsC,KAAK,GAAGzK,OAAO,CAACyK,KAAK,CAAC;EACzD;;EAEA;EACA;EACA;;EAEA;AACF;AACA;EACE,SAAS/E,YAAYA,CAAA,EAAG;IACtB4C,MAAM,CAAC,CAAC;EACV;;EAEA;AACF;AACA;AACA;EACE,SAAShF,kBAAkBA,CAAC0H,KAAK,EAAE;IACjChK,UAAU,CAACpB,IAAI,CAAC,CAACoL,KAAK,CAACT,MAAM,CAAC;IAC9BnC,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,KAAK,CAAC;IACVD,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC;EAClC;;EAEA;AACF;AACA;AACA;EACE,SAAS/E,oBAAoBA,CAACwH,KAAK,EAAE;IACnChK,UAAU,CAACpB,IAAI,CAAC,CAACoL,KAAK,CAACT,MAAM,CAAC;IAC9BnC,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,KAAK,CAAC;IACVD,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC;EAClC;;EAEA;AACF;AACA;AACA;EACE,SAASnF,oBAAoBA,CAAC4H,KAAK,EAAE;IACnC,IAAI7C,OAAO,CAAC,iBAAiB,CAAC,EAAE;MAC9B,MAAMsC,KAAK,GAAGQ,MAAM,CAACC,QAAQ,CAC3B,IAAI,CAAC9B,cAAc,CAAC4B,KAAK,CAAC,EAC1B5K,SAAS,CAAC+K,kBACZ,CAAC;MAED,IAAIV,KAAK,KAAK,CAAC,EAAE;QACfjC,GAAG,CAAC,UAAU,GAAGzI,MAAM,CAACqL,MAAM,CAACX,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;MAC/C;IACF;EACF;;EAEA;AACF;AACA;EACE,SAASvH,qBAAqBA,CAAA,EAAG;IAC/B,IAAIiF,OAAO,CAAC,iBAAiB,CAAC,EAAE;MAC9BK,GAAG,CAAC,GAAG,CAAC;IACV,CAAC,MAAM;MACL6C,cAAc,CAAC,CAAC;IAClB;IAEAjD,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,MAAM,CAAC;IACXD,OAAO,CAAC,iBAAiB,CAAC;IAC1B;IACAA,OAAO,CAAC,YAAY,CAAC;EACvB;;EAEA;AACF;AACA;EACE,SAAStB,iBAAiBA,CAAA,EAAG;IAC3BoE,cAAc,CAAC,CAAC;IAChBrK,UAAU,CAACuI,GAAG,CAAC,CAAC;IAChBxC,UAAU,CAAC,CAAC;IACZyB,GAAG,CAAC,OAAO,CAAC;EACd;;EAEA;AACF;AACA;EACE,SAAStB,mBAAmBA,CAAA,EAAG;IAC7BmE,cAAc,CAAC,CAAC;IAChBrK,UAAU,CAACuI,GAAG,CAAC,CAAC;IAChBxC,UAAU,CAAC,CAAC;IACZyB,GAAG,CAAC,OAAO,CAAC;EACd;;EAEA;AACF;AACA;EACE,SAAS6C,cAAcA,CAAA,EAAG;IACxB,IAAIlD,OAAO,CAAC,YAAY,CAAC,IAAI,CAACA,OAAO,CAAC,qBAAqB,CAAC,EAAE;MAC5DC,kBAAkB,CAAC,CAAC;IACtB;IAEAI,GAAG,CAAC,OAAO,CAAC;IACZD,OAAO,CAAC,qBAAqB,CAAC;EAChC;;EAEA;AACF;AACA;AACA;EACE,SAASnH,iBAAiBA,CAAA,EAAG;IAC3BJ,UAAU,CAACpB,IAAI,CAAC,KAAK,CAAC;IACtBwI,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,cAAc,CAAC;EACrB;;EAEA;AACF;AACA;AACA;EACE,SAAS1D,gBAAgBA,CAAA,EAAG;IAC1B9D,UAAU,CAACuI,GAAG,CAAC,CAAC;IAChBnB,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,eAAe,CAAC;IACpBD,OAAO,CAAC,qBAAqB,CAAC;EAChC;;EAEA;AACF;AACA;AACA;EACE,SAAS7E,gBAAgBA,CAAA,EAAG;IAC1B,IAAI,CAAC1C,UAAU,CAACA,UAAU,CAACiI,MAAM,GAAG,CAAC,CAAC,EAAE;MACtCb,kBAAkB,CAAC,CAAC;MACpBI,GAAG,CAAC,KAAK,CAAC;IACZ;IAEAD,OAAO,CAAC,qBAAqB,CAAC;EAChC;;EAEA;AACF;AACA;AACA;EACE,SAASpB,eAAeA,CAAA,EAAG;IACzB,IAAInG,UAAU,CAACA,UAAU,CAACiI,MAAM,GAAG,CAAC,CAAC,EAAE;MACrCV,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC;IACtC,CAAC,MAAM;MACLC,GAAG,CAAC,MAAM,CAAC;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,SAASlH,iBAAiBA,CAAA,EAAG;IAC3B8G,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,YAAY,CAAC;IACjBD,OAAO,CAAC,aAAa,EAAE,CAAC,CAAC;EAC3B;;EAEA;AACF;AACA;AACA;EACE,SAAS9C,yBAAyBA,CAAA,EAAG;IACnC,MAAMgF,KAAK,GAAGnC,MAAM,CAAC,CAAC;IACtBE,GAAG,CAAC,mBAAmB,GAAGiC,KAAK,GAAG,GAAG,CAAC;EACxC;;EAEA;AACF;AACA;AACA;EACE,SAASjF,qBAAqBA,CAAA,EAAG;IAC/B,MAAM8F,KAAK,GAAGnD,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;IAEzC,IAAI,CAACmD,KAAK,EAAE;MACV9C,GAAG,CAAC,GAAG,CAAC;MACRD,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAC;IACrC;IAEAA,OAAO,CAAC,aAAa,EAAE+C,KAAK,GAAG,CAAC,CAAC;EACnC;;EAEA;AACF;AACA;AACA;EACE,SAAS3J,mBAAmBA,CAAA,EAAG;IAC7ByG,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,aAAa,CAAC;EACpB;;EAEA;AACF;AACA;AACA;EACE,SAASlD,cAAcA,CAAA,EAAG;IACxB,MAAMgG,KAAK,GAAGnD,OAAO,CAAC,aAAa,CAAC;;IAEpC;IACA;IACA;IACA;IACA;IACA,IACEmD,KAAK,KAAKlB,SAAS,IACnBkB,KAAK,GAAG,CAAC,IACTvF,IAAI,CAAC/E,UAAU,CAACiI,MAAM,GAAG,CAAC,IAC1B,CAACd,OAAO,CAAC,YAAY,CAAC,EACtB;MACApB,UAAU,CAAC,CAAC;IACd;;IAEA;IACA;IACA,IAAIoB,OAAO,CAAC,kBAAkB,CAAC,EAAE;MAC/BC,kBAAkB,CAAC,CAAC;IACtB;IAEAI,GAAG,CAAC,eAAe,CAAC;IACpB,IAAI8C,KAAK,KAAKlB,SAAS,IAAIkB,KAAK,GAAG,CAAC,EAAElD,kBAAkB,CAAC,CAAC;IAC1DG,OAAO,CAAC,kBAAkB,CAAC;IAC3BA,OAAO,CAAC,aAAa,CAAC;IACtBA,OAAO,CAAC,oBAAoB,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;EACE,SAAS1F,YAAYA,CAAA,EAAG;IACtB9B,UAAU,CAACnB,IAAI,CAAC;MAACgD,KAAK,EAAE;IAAI,CAAC,CAAC;IAC9BhC,IAAI,GAAGwJ,SAAS,EAAC;EACnB;;EAEA;AACF;AACA;AACA;EACE,SAASpH,WAAWA,CAAA,EAAG;IACrBjC,UAAU,CAACnB,IAAI,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA;AACF;AACA;AACA;EACE,SAASkH,eAAeA,CAACkE,KAAK,EAAE;IAC9BjK,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC,CAACsC,OAAO,GAAG,IAAI,CAACnC,cAAc,CAAC4B,KAAK,CAAC;EACxE;;EAEA;AACF;AACA;AACA;EACE,SAASpE,WAAWA,CAAA,EAAG;IACrB7F,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC,CAACnG,KAAK,GAAGwF,MAAM,CAAC,CAAC;EACpD;;EAEA;AACF;AACA;AACA;EACE,SAASjB,qBAAqBA,CAAC2D,KAAK,EAAE;IACpCjK,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC,CAACuC,WAAW,GAAG,IAAI,CAACpC,cAAc,CAAC4B,KAAK,CAAC;EAC5E;;EAEA;AACF;AACA;AACA;EACE,SAASnH,eAAeA,CAAA,EAAG;IACzBrC,MAAM,CAAC,CAAC,EAAC;IACTT,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC,CAACwC,WAAW,GAAG,EAAE;EACpD;;EAEA;AACF;AACA;AACA;EACE,SAAS1H,gCAAgCA,CAAA,EAAG;IAC1CvC,MAAM,CAAC,CAAC;IACR;IACA;IACA+G,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;EACE,SAASjB,+BAA+BA,CAAA,EAAG;IACzCvG,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC,CAACwC,WAAW,GAAGnD,MAAM,CAAC,CAAC;IACxDC,OAAO,CAAC,cAAc,CAAC;EACzB;;EAEA;AACF;AACA;AACA;EACE,SAAShB,yBAAyBA,CAAA,EAAG;IACnCxG,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC,CAACyC,KAAK,GAAGpD,MAAM,CAAC,CAAC;EACpD;;EAEA;AACF;AACA;AACA;EACE,SAAS3B,WAAWA,CAAA,EAAG;IACrB,IAAIiC,KAAK,GAAG7H,UAAU,CAACkI,MAAM,GAAG,CAAC,EAAC;IAClC,MAAM0C,KAAK,GAAG5K,UAAU,CAAC6H,KAAK,CAAC;IAC/B,MAAMgD,EAAE,GAAGD,KAAK,CAACH,WAAW,IAAIG,KAAK,CAACJ,OAAO;IAC7C5L,MAAM,CAACiM,EAAE,KAAKxB,SAAS,EAAE,8CAA8C,CAAC;IACxEzK,MAAM,CAACgM,KAAK,CAAC7I,KAAK,KAAKsH,SAAS,EAAE,2BAA2B,CAAC;IAC9D,MAAMlC,OAAO,GACXyD,KAAK,CAACF,WAAW,KAAKrB,SAAS,GAC3BvJ,WAAW,CAACZ,mBAAmB,CAAC2L,EAAE,CAAC,CAAC,GACpCD,KAAK;IAEX/K,IAAI,GAAG,IAAI;IAEX,OAAOgI,KAAK,EAAE,EAAE;MACd,IAAI7H,UAAU,CAAC6H,KAAK,CAAC,CAAChG,KAAK,EAAE;QAC3BhC,IAAI,GAAGwJ,SAAS;QAChB;MACF;IACF;IAEA,IAAIuB,KAAK,CAAC/I,KAAK,EAAE;MACf4F,GAAG,CACD,YAAY,GACVtI,WAAW,CACTgI,OAAO,CAACuD,WAAW,EACnB9K,QAAQ,CAACkL,sBAAsB,GAAGzB,SAAS,GAAG5J,cAChD,CAAC,GACD,SACJ,CAAC;MACD6H,GAAG,CAACsD,KAAK,CAAC7I,KAAK,CAAC;MAChB0F,GAAG,CAAC,GAAG,CAAC;IACV,CAAC,MAAM;MACLA,GAAG,CACD,WAAW,GACTtI,WAAW,CACTgI,OAAO,CAACuD,WAAW,EACnB9K,QAAQ,CAACkL,sBAAsB,GAAGzB,SAAS,GAAG7J,YAChD,CAAC,GACD,GACJ,CAAC;IACH;IAEAiI,GAAG,CAACN,OAAO,CAACwD,KAAK,GAAG,UAAU,GAAGxD,OAAO,CAACwD,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC;IAE1D,IAAIC,KAAK,CAAC/I,KAAK,EAAE;MACf4F,GAAG,CAAC,KAAK,CAAC;IACZ,CAAC,MAAM;MACLA,GAAG,CAAC,GAAG,CAAC;MACRH,GAAG,CAACsD,KAAK,CAAC7I,KAAK,CAAC;MAChB0F,GAAG,CAAC,MAAM,CAAC;IACb;IAEAzH,UAAU,CAACwI,GAAG,CAAC,CAAC;EAClB;;EAEA;AACF;AACA;AACA;EACE,SAAStH,iBAAiBA,CAAA,EAAG;IAC3BT,MAAM,CAAC,CAAC;IACRT,UAAU,CAACnB,IAAI,CAAC,CAAC,CAAC,CAAC;EACrB;;EAEA;AACF;AACA;AACA;EACE,SAASsG,2BAA2BA,CAAC8E,KAAK,EAAE;IAC1C;IACA1C,MAAM,CAAC,CAAC;IACRvH,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC,CAACsC,OAAO,GAAG,IAAI,CAACnC,cAAc,CAAC4B,KAAK,CAAC;EACxE;;EAEA;AACF;AACA;AACA;EACE,SAAS7I,kCAAkCA,CAAA,EAAG;IAC5CX,MAAM,CAAC,CAAC;IACR+G,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;EACE,SAAStC,iCAAiCA,CAAA,EAAG;IAC3ClF,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC,CAACwC,WAAW,GAAGnD,MAAM,CAAC,CAAC;IACxDC,OAAO,CAAC,cAAc,CAAC;EACzB;;EAEA;AACF;AACA;AACA;EACE,SAASpC,2BAA2BA,CAAA,EAAG;IACrCpF,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC,CAACyC,KAAK,GAAGpD,MAAM,CAAC,CAAC;EACpD;;EAEA;AACF;AACA;AACA;EACE,SAAStC,gBAAgBA,CAAA,EAAG;IAC1B,MAAM2F,KAAK,GAAG5K,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC;IAC/CtJ,MAAM,CAACgM,KAAK,CAACJ,OAAO,KAAKnB,SAAS,EAAE,6BAA6B,CAAC;IAClE,MAAMwB,EAAE,GAAG3L,mBAAmB,CAAC0L,KAAK,CAACJ,OAAO,CAAC;IAE7CjD,MAAM,CAAC,CAAC;IAER,IAAI,CAAChI,cAAc,CAACoJ,IAAI,CAAC7I,WAAW,EAAE+K,EAAE,CAAC,EAAE;MACzC/K,WAAW,CAAC+K,EAAE,CAAC,GAAG7K,UAAU,CAACA,UAAU,CAACkI,MAAM,GAAG,CAAC,CAAC;IACrD;IAEAlI,UAAU,CAACwI,GAAG,CAAC,CAAC;EAClB;;EAEA;AACF;AACA;AACA;EACE,SAASxH,cAAcA,CAAA,EAAG;IACxBwG,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC;EACtC;;EAEA;AACF;AACA;AACA;EACE,SAAS9D,wBAAwBA,CAACuG,KAAK,EAAE;IACvC;IACA,IAAI7C,OAAO,CAAC,aAAa,CAAC,EAAE;IAC5BI,OAAO,CAAC,aAAa,EAAE,IAAI,CAACa,cAAc,CAAC4B,KAAK,CAAC,CAAC/B,MAAM,CAAC;IACzDb,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,IAAI,GAAGL,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;EAC1C;;EAEA;AACF;AACA;AACA;EACE,SAASjE,oBAAoBA,CAAA,EAAG;IAC9B1C,MAAM,CAAC,CAAC;IACR+G,OAAO,CAAC,qBAAqB,CAAC;EAChC;;EAEA;AACF;AACA;AACA;EACE,SAASX,uBAAuBA,CAAA,EAAG;IACjCW,OAAO,CAAC,qBAAqB,EAAE,IAAI,CAAC;EACtC;;EAEA;AACF;AACA;AACA;EACE,SAAShE,gBAAgBA,CAAA,EAAG;IAC1BiE,GAAG,CAAC,KAAK,GAAGL,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;IACzCI,OAAO,CAAC,aAAa,CAAC;EACxB;;EAEA;AACF;AACA;AACA;EACE,SAASb,+BAA+BA,CAACsD,KAAK,EAAE;IAC9CzC,OAAO,CACL,aAAa,EACb,IAAI,CAACa,cAAc,CAAC4B,KAAK,CAAC,CAACJ,UAAU,CAAC,CAAC,CAAC,KAAKzK,KAAK,CAAC2L,QAAQ,GAAG,CAAC,GAAG,CACpE,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACE,SAAStE,mBAAmBA,CAAA,EAAG;IAC7B,MAAMiD,KAAK,GAAGnC,MAAM,CAAC,CAAC;IACtBF,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,IAAI,GAAGL,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;IACxCE,GAAG,CAACoC,KAAK,CAAC;IACVjC,GAAG,CAAC,KAAK,GAAGL,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;IACzCI,OAAO,CAAC,qBAAqB,CAAC;IAC9BA,OAAO,CAAC,aAAa,CAAC;EACxB;;EAEA;AACF;AACA;AACA;EACE,SAASvD,UAAUA,CAACgG,KAAK,EAAE;IACzB3C,GAAG,CAACtI,MAAM,CAAC,IAAI,CAACqJ,cAAc,CAAC4B,KAAK,CAAC,CAAC,CAAC;EACzC;;EAEA;AACF;AACA;AACA;EACE,SAAShE,gBAAgBA,CAACgE,KAAK,EAAE;IAC/B,IAAI7C,OAAO,CAAC,qBAAqB,CAAC,EAAE;MAClC;IACF;IAEA,IAAIA,OAAO,CAAC,oBAAoB,CAAC,EAAE;MACjCI,OAAO,CAAC,oBAAoB,CAAC;MAC7B;IACF;IAEA,IAAIJ,OAAO,CAAC,YAAY,CAAC,EAAE;MACzBE,GAAG,CAAC,GAAG,CAAC;MACR;IACF;IAEAA,GAAG,CAACtI,MAAM,CAAC,IAAI,CAACqJ,cAAc,CAAC4B,KAAK,CAAC,CAAC,CAAC;EACzC;;EAEA;AACF;AACA;AACA;EACE,SAASpF,mBAAmBA,CAACoF,KAAK,EAAE;IAClC3C,GAAG,CAACtI,MAAM,CAAC,IAAI,CAACqJ,cAAc,CAAC4B,KAAK,CAAC,CAAC,CAAC;IACvCzC,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC;EACnC;;EAEA;AACF;AACA;AACA;EACE,SAASjC,eAAeA,CAAA,EAAG;IACzBkC,GAAG,CAAC,QAAQ,CAAC;EACf;;EAEA;AACF;AACA;EACE,SAAS/F,eAAeA,CAAA,EAAG;IACzB2F,kBAAkB,CAAC,CAAC;IACpBzF,WAAW,CAAC,CAAC;EACf;;EAEA;AACF;AACA;EACE,SAAS6D,UAAUA,CAAA,EAAG;IACpB+B,OAAO,CAAC,cAAc,CAAC;EACzB;;EAEA;AACF;AACA;EACE,SAAS5F,WAAWA,CAAA,EAAG;IACrB,IAAIhC,QAAQ,CAACoL,kBAAkB,EAAE;MAC/BxD,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC;IAC/B;EACF;;EAEA;AACF;AACA;EACE,SAAShG,eAAeA,CAAA,EAAG;IACzBiG,GAAG,CAAC,MAAM,CAAC;EACb;;EAEA;AACF;AACA;EACE,SAASpE,aAAaA,CAAA,EAAG;IACvBoE,GAAG,CAAC,UAAU,CAAC;EACjB;;EAEA;AACF;AACA;EACE,SAAS3G,eAAeA,CAAA,EAAG;IACzB0G,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC;IAC3BC,GAAG,CAAC,QAAQ,CAAC;EACf;;EAEA;AACF;AACA;EACE,SAAS3C,cAAcA,CAAA,EAAG;IACxB0C,OAAO,CAAC,YAAY,CAAC;IACrBC,GAAG,CAAC,SAAS,CAAC;EAChB;;EAEA;AACF;AACA;EACE,SAASpC,cAAcA,CAAA,EAAG;IACxBoC,GAAG,CAAC,OAAO,CAAC;EACd;;EAEA;AACF;AACA;EACE,SAASX,YAAYA,CAAA,EAAG;IACtBW,GAAG,CAAC,WAAW,CAAC;EAClB;;EAEA;AACF;AACA;EACE,SAAST,mBAAmBA,CAAA,EAAG;IAC7BK,kBAAkB,CAAC,CAAC;IACpBI,GAAG,CAAC,QAAQ,CAAC;EACf;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAStD,8BAA8BA,CAAC8F,KAAK,EAAE;IAC7CzC,OAAO,CAAC,wBAAwB,EAAEyC,KAAK,CAAC9B,IAAI,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;EACE,SAAS7D,6BAA6BA,CAAC2F,KAAK,EAAE;IAC5C,MAAMP,KAAK,GAAG,IAAI,CAACrB,cAAc,CAAC4B,KAAK,CAAC;IACxC,MAAMgB,OAAO,GAAG7D,OAAO,CAAC,wBAAwB,CAAC,GAC7CrI,+BAA+B,CAC7B2K,KAAK,EACLtC,OAAO,CAAC,wBAAwB,CAAC,KAC/B9H,KAAK,CAAC8E,+BAA+B,GACnC/E,SAAS,CAAC+K,kBAAkB,GAC5B/K,SAAS,CAAC6L,sBAChB,CAAC,GACDxM,6BAA6B,CAACgL,KAAK,CAAC;;IAExC;IACA;IACA;IACApC,GAAG,CAACtI,MAAM,CAAC,qBAAuBiM,OAAQ,CAAC,CAAC;IAC5CzD,OAAO,CAAC,wBAAwB,CAAC;EACnC;;EAEA;AACF;AACA;AACA;EACE,SAAS1D,sBAAsBA,CAACmG,KAAK,EAAE;IACrC,MAAMkB,GAAG,GAAG,IAAI,CAAC9C,cAAc,CAAC4B,KAAK,CAAC;IACtCxC,GAAG,CACD,WAAW,GACTtI,WAAW,CACTgM,GAAG,EACHvL,QAAQ,CAACkL,sBAAsB,GAAGzB,SAAS,GAAG7J,YAChD,CAAC,GACD,IACJ,CAAC;IACD8H,GAAG,CAACtI,MAAM,CAACmM,GAAG,CAAC,CAAC;IAChB1D,GAAG,CAAC,MAAM,CAAC;EACb;;EAEA;AACF;AACA;AACA;EACE,SAAS7D,mBAAmBA,CAACqG,KAAK,EAAE;IAClC,MAAMkB,GAAG,GAAG,IAAI,CAAC9C,cAAc,CAAC4B,KAAK,CAAC;IACtCxC,GAAG,CAAC,WAAW,GAAGtI,WAAW,CAAC,SAAS,GAAGgM,GAAG,CAAC,GAAG,IAAI,CAAC;IACtD7D,GAAG,CAACtI,MAAM,CAACmM,GAAG,CAAC,CAAC;IAChB1D,GAAG,CAAC,MAAM,CAAC;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}