{"ast": null, "code": "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n'';\n\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function inlineCode(state, node) {\n  /** @type {Text} */\n  const text = {\n    type: 'text',\n    value: node.value.replace(/\\r?\\n|\\r/g, ' ')\n  };\n  state.patch(node, text);\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'code',\n    properties: {},\n    children: [text]\n  };\n  state.patch(node, result);\n  return state.applyData(node, result);\n}", "map": {"version": 3, "names": ["inlineCode", "state", "node", "text", "type", "value", "replace", "patch", "result", "tagName", "properties", "children", "applyData"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/mdast-util-to-hast/lib/handlers/inline-code.js"], "sourcesContent": ["/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function inlineCode(state, node) {\n  /** @type {Text} */\n  const text = {type: 'text', value: node.value.replace(/\\r?\\n|\\r/g, ' ')}\n  state.patch(node, text)\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'code',\n    properties: {},\n    children: [text]\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,EAAE;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,UAAUA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACtC;EACA,MAAMC,IAAI,GAAG;IAACC,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAEH,IAAI,CAACG,KAAK,CAACC,OAAO,CAAC,WAAW,EAAE,GAAG;EAAC,CAAC;EACxEL,KAAK,CAACM,KAAK,CAACL,IAAI,EAAEC,IAAI,CAAC;;EAEvB;EACA,MAAMK,MAAM,GAAG;IACbJ,IAAI,EAAE,SAAS;IACfK,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,CAAC,CAAC;IACdC,QAAQ,EAAE,CAACR,IAAI;EACjB,CAAC;EACDF,KAAK,CAACM,KAAK,CAACL,IAAI,EAAEM,MAAM,CAAC;EACzB,OAAOP,KAAK,CAACW,SAAS,CAACV,IAAI,EAAEM,MAAM,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}