{"ast": null, "code": "/**\n * @import {\n *   Code,\n *   Construct,\n *   Event,\n *   Point,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport { ok as assert } from 'devlop';\nimport { push, splice } from 'micromark-util-chunked';\nimport { classify<PERSON><PERSON><PERSON> } from 'micromark-util-classify-character';\nimport { resolveAll } from 'micromark-util-resolve-all';\nimport { codes, constants, types } from 'micromark-util-symbol';\n\n/** @type {Construct} */\nexport const attention = {\n  name: 'attention',\n  resolveAll: resolveAllAttention,\n  tokenize: tokenizeAttention\n};\n\n/**\n * Take all events and resolve attention to emphasis or strong.\n *\n * @type {Resolver}\n */\n// eslint-disable-next-line complexity\nfunction resolveAllAttention(events, context) {\n  let index = -1;\n  /** @type {number} */\n  let open;\n  /** @type {Token} */\n  let group;\n  /** @type {Token} */\n  let text;\n  /** @type {Token} */\n  let openingSequence;\n  /** @type {Token} */\n  let closingSequence;\n  /** @type {number} */\n  let use;\n  /** @type {Array<Event>} */\n  let nextEvents;\n  /** @type {number} */\n  let offset;\n\n  // Walk through all events.\n  //\n  // Note: performance of this is fine on an mb of normal markdown, but it’s\n  // a bottleneck for malicious stuff.\n  while (++index < events.length) {\n    // Find a token that can close.\n    if (events[index][0] === 'enter' && events[index][1].type === 'attentionSequence' && events[index][1]._close) {\n      open = index;\n\n      // Now walk back to find an opener.\n      while (open--) {\n        // Find a token that can open the closer.\n        if (events[open][0] === 'exit' && events[open][1].type === 'attentionSequence' && events[open][1]._open &&\n        // If the markers are the same:\n        context.sliceSerialize(events[open][1]).charCodeAt(0) === context.sliceSerialize(events[index][1]).charCodeAt(0)) {\n          // If the opening can close or the closing can open,\n          // and the close size *is not* a multiple of three,\n          // but the sum of the opening and closing size *is* multiple of three,\n          // then don’t match.\n          if ((events[open][1]._close || events[index][1]._open) && (events[index][1].end.offset - events[index][1].start.offset) % 3 && !((events[open][1].end.offset - events[open][1].start.offset + events[index][1].end.offset - events[index][1].start.offset) % 3)) {\n            continue;\n          }\n\n          // Number of markers to use from the sequence.\n          use = events[open][1].end.offset - events[open][1].start.offset > 1 && events[index][1].end.offset - events[index][1].start.offset > 1 ? 2 : 1;\n          const start = {\n            ...events[open][1].end\n          };\n          const end = {\n            ...events[index][1].start\n          };\n          movePoint(start, -use);\n          movePoint(end, use);\n          openingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start,\n            end: {\n              ...events[open][1].end\n            }\n          };\n          closingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start: {\n              ...events[index][1].start\n            },\n            end\n          };\n          text = {\n            type: use > 1 ? types.strongText : types.emphasisText,\n            start: {\n              ...events[open][1].end\n            },\n            end: {\n              ...events[index][1].start\n            }\n          };\n          group = {\n            type: use > 1 ? types.strong : types.emphasis,\n            start: {\n              ...openingSequence.start\n            },\n            end: {\n              ...closingSequence.end\n            }\n          };\n          events[open][1].end = {\n            ...openingSequence.start\n          };\n          events[index][1].start = {\n            ...closingSequence.end\n          };\n          nextEvents = [];\n\n          // If there are more markers in the opening, add them before.\n          if (events[open][1].end.offset - events[open][1].start.offset) {\n            nextEvents = push(nextEvents, [['enter', events[open][1], context], ['exit', events[open][1], context]]);\n          }\n\n          // Opening.\n          nextEvents = push(nextEvents, [['enter', group, context], ['enter', openingSequence, context], ['exit', openingSequence, context], ['enter', text, context]]);\n\n          // Always populated by defaults.\n          assert(context.parser.constructs.insideSpan.null, 'expected `insideSpan` to be populated');\n\n          // Between.\n          nextEvents = push(nextEvents, resolveAll(context.parser.constructs.insideSpan.null, events.slice(open + 1, index), context));\n\n          // Closing.\n          nextEvents = push(nextEvents, [['exit', text, context], ['enter', closingSequence, context], ['exit', closingSequence, context], ['exit', group, context]]);\n\n          // If there are more markers in the closing, add them after.\n          if (events[index][1].end.offset - events[index][1].start.offset) {\n            offset = 2;\n            nextEvents = push(nextEvents, [['enter', events[index][1], context], ['exit', events[index][1], context]]);\n          } else {\n            offset = 0;\n          }\n          splice(events, open - 1, index - open + 3, nextEvents);\n          index = open + nextEvents.length - offset - 2;\n          break;\n        }\n      }\n    }\n  }\n\n  // Remove remaining sequences.\n  index = -1;\n  while (++index < events.length) {\n    if (events[index][1].type === 'attentionSequence') {\n      events[index][1].type = 'data';\n    }\n  }\n  return events;\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAttention(effects, ok) {\n  const attentionMarkers = this.parser.constructs.attentionMarkers.null;\n  const previous = this.previous;\n  const before = classifyCharacter(previous);\n\n  /** @type {NonNullable<Code>} */\n  let marker;\n  return start;\n\n  /**\n   * Before a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.asterisk || code === codes.underscore, 'expected asterisk or underscore');\n    marker = code;\n    effects.enter('attentionSequence');\n    return inside(code);\n  }\n\n  /**\n   * In a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code);\n      return inside;\n    }\n    const token = effects.exit('attentionSequence');\n\n    // To do: next major: move this to resolver, just like `markdown-rs`.\n    const after = classifyCharacter(code);\n\n    // Always populated by defaults.\n    assert(attentionMarkers, 'expected `attentionMarkers` to be populated');\n    const open = !after || after === constants.characterGroupPunctuation && before || attentionMarkers.includes(code);\n    const close = !before || before === constants.characterGroupPunctuation && after || attentionMarkers.includes(previous);\n    token._open = Boolean(marker === codes.asterisk ? open : open && (before || !close));\n    token._close = Boolean(marker === codes.asterisk ? close : close && (after || !open));\n    return ok(code);\n  }\n}\n\n/**\n * Move a point a bit.\n *\n * Note: `move` only works inside lines! It’s not possible to move past other\n * chunks (replacement characters, tabs, or line endings).\n *\n * @param {Point} point\n *   Point.\n * @param {number} offset\n *   Amount to move.\n * @returns {undefined}\n *   Nothing.\n */\nfunction movePoint(point, offset) {\n  point.column += offset;\n  point.offset += offset;\n  point._bufferIndex += offset;\n}", "map": {"version": 3, "names": ["ok", "assert", "push", "splice", "classifyCharacter", "resolveAll", "codes", "constants", "types", "attention", "name", "resolveAllAttention", "tokenize", "tokenizeAttention", "events", "context", "index", "open", "group", "text", "openingSequence", "closingSequence", "use", "nextEvents", "offset", "length", "type", "_close", "_open", "sliceSerialize", "charCodeAt", "end", "start", "movePoint", "strongSequence", "emphasisSequence", "strongText", "emphasisText", "strong", "emphasis", "parser", "constructs", "insideSpan", "null", "slice", "effects", "attentionMarkers", "previous", "before", "marker", "code", "asterisk", "underscore", "enter", "inside", "consume", "token", "exit", "after", "characterGroupPunctuation", "includes", "close", "Boolean", "point", "column", "_bufferIndex"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark-core-commonmark/dev/lib/attention.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   Event,\n *   Point,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {push, splice} from 'micromark-util-chunked'\nimport {classify<PERSON><PERSON>cter} from 'micromark-util-classify-character'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const attention = {\n  name: 'attention',\n  resolveAll: resolveAllAttention,\n  tokenize: tokenizeAttention\n}\n\n/**\n * Take all events and resolve attention to emphasis or strong.\n *\n * @type {Resolver}\n */\n// eslint-disable-next-line complexity\nfunction resolveAllAttention(events, context) {\n  let index = -1\n  /** @type {number} */\n  let open\n  /** @type {Token} */\n  let group\n  /** @type {Token} */\n  let text\n  /** @type {Token} */\n  let openingSequence\n  /** @type {Token} */\n  let closingSequence\n  /** @type {number} */\n  let use\n  /** @type {Array<Event>} */\n  let nextEvents\n  /** @type {number} */\n  let offset\n\n  // Walk through all events.\n  //\n  // Note: performance of this is fine on an mb of normal markdown, but it’s\n  // a bottleneck for malicious stuff.\n  while (++index < events.length) {\n    // Find a token that can close.\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === 'attentionSequence' &&\n      events[index][1]._close\n    ) {\n      open = index\n\n      // Now walk back to find an opener.\n      while (open--) {\n        // Find a token that can open the closer.\n        if (\n          events[open][0] === 'exit' &&\n          events[open][1].type === 'attentionSequence' &&\n          events[open][1]._open &&\n          // If the markers are the same:\n          context.sliceSerialize(events[open][1]).charCodeAt(0) ===\n            context.sliceSerialize(events[index][1]).charCodeAt(0)\n        ) {\n          // If the opening can close or the closing can open,\n          // and the close size *is not* a multiple of three,\n          // but the sum of the opening and closing size *is* multiple of three,\n          // then don’t match.\n          if (\n            (events[open][1]._close || events[index][1]._open) &&\n            (events[index][1].end.offset - events[index][1].start.offset) % 3 &&\n            !(\n              (events[open][1].end.offset -\n                events[open][1].start.offset +\n                events[index][1].end.offset -\n                events[index][1].start.offset) %\n              3\n            )\n          ) {\n            continue\n          }\n\n          // Number of markers to use from the sequence.\n          use =\n            events[open][1].end.offset - events[open][1].start.offset > 1 &&\n            events[index][1].end.offset - events[index][1].start.offset > 1\n              ? 2\n              : 1\n\n          const start = {...events[open][1].end}\n          const end = {...events[index][1].start}\n          movePoint(start, -use)\n          movePoint(end, use)\n\n          openingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start,\n            end: {...events[open][1].end}\n          }\n          closingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start: {...events[index][1].start},\n            end\n          }\n          text = {\n            type: use > 1 ? types.strongText : types.emphasisText,\n            start: {...events[open][1].end},\n            end: {...events[index][1].start}\n          }\n          group = {\n            type: use > 1 ? types.strong : types.emphasis,\n            start: {...openingSequence.start},\n            end: {...closingSequence.end}\n          }\n\n          events[open][1].end = {...openingSequence.start}\n          events[index][1].start = {...closingSequence.end}\n\n          nextEvents = []\n\n          // If there are more markers in the opening, add them before.\n          if (events[open][1].end.offset - events[open][1].start.offset) {\n            nextEvents = push(nextEvents, [\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context]\n            ])\n          }\n\n          // Opening.\n          nextEvents = push(nextEvents, [\n            ['enter', group, context],\n            ['enter', openingSequence, context],\n            ['exit', openingSequence, context],\n            ['enter', text, context]\n          ])\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.insideSpan.null,\n            'expected `insideSpan` to be populated'\n          )\n\n          // Between.\n          nextEvents = push(\n            nextEvents,\n            resolveAll(\n              context.parser.constructs.insideSpan.null,\n              events.slice(open + 1, index),\n              context\n            )\n          )\n\n          // Closing.\n          nextEvents = push(nextEvents, [\n            ['exit', text, context],\n            ['enter', closingSequence, context],\n            ['exit', closingSequence, context],\n            ['exit', group, context]\n          ])\n\n          // If there are more markers in the closing, add them after.\n          if (events[index][1].end.offset - events[index][1].start.offset) {\n            offset = 2\n            nextEvents = push(nextEvents, [\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context]\n            ])\n          } else {\n            offset = 0\n          }\n\n          splice(events, open - 1, index - open + 3, nextEvents)\n\n          index = open + nextEvents.length - offset - 2\n          break\n        }\n      }\n    }\n  }\n\n  // Remove remaining sequences.\n  index = -1\n\n  while (++index < events.length) {\n    if (events[index][1].type === 'attentionSequence') {\n      events[index][1].type = 'data'\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAttention(effects, ok) {\n  const attentionMarkers = this.parser.constructs.attentionMarkers.null\n  const previous = this.previous\n  const before = classifyCharacter(previous)\n\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Before a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(\n      code === codes.asterisk || code === codes.underscore,\n      'expected asterisk or underscore'\n    )\n    marker = code\n    effects.enter('attentionSequence')\n    return inside(code)\n  }\n\n  /**\n   * In a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    const token = effects.exit('attentionSequence')\n\n    // To do: next major: move this to resolver, just like `markdown-rs`.\n    const after = classifyCharacter(code)\n\n    // Always populated by defaults.\n    assert(attentionMarkers, 'expected `attentionMarkers` to be populated')\n\n    const open =\n      !after ||\n      (after === constants.characterGroupPunctuation && before) ||\n      attentionMarkers.includes(code)\n    const close =\n      !before ||\n      (before === constants.characterGroupPunctuation && after) ||\n      attentionMarkers.includes(previous)\n\n    token._open = Boolean(\n      marker === codes.asterisk ? open : open && (before || !close)\n    )\n    token._close = Boolean(\n      marker === codes.asterisk ? close : close && (after || !open)\n    )\n    return ok(code)\n  }\n}\n\n/**\n * Move a point a bit.\n *\n * Note: `move` only works inside lines! It’s not possible to move past other\n * chunks (replacement characters, tabs, or line endings).\n *\n * @param {Point} point\n *   Point.\n * @param {number} offset\n *   Amount to move.\n * @returns {undefined}\n *   Nothing.\n */\nfunction movePoint(point, offset) {\n  point.column += offset\n  point.offset += offset\n  point._bufferIndex += offset\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAQA,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,IAAI,EAAEC,MAAM,QAAO,wBAAwB;AACnD,SAAQC,iBAAiB,QAAO,mCAAmC;AACnE,SAAQC,UAAU,QAAO,4BAA4B;AACrD,SAAQC,KAAK,EAAEC,SAAS,EAAEC,KAAK,QAAO,uBAAuB;;AAE7D;AACA,OAAO,MAAMC,SAAS,GAAG;EACvBC,IAAI,EAAE,WAAW;EACjBL,UAAU,EAAEM,mBAAmB;EAC/BC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,mBAAmBA,CAACG,MAAM,EAAEC,OAAO,EAAE;EAC5C,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,IAAI;EACR;EACA,IAAIC,KAAK;EACT;EACA,IAAIC,IAAI;EACR;EACA,IAAIC,eAAe;EACnB;EACA,IAAIC,eAAe;EACnB;EACA,IAAIC,GAAG;EACP;EACA,IAAIC,UAAU;EACd;EACA,IAAIC,MAAM;;EAEV;EACA;EACA;EACA;EACA,OAAO,EAAER,KAAK,GAAGF,MAAM,CAACW,MAAM,EAAE;IAC9B;IACA,IACEX,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,IAC5BF,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACU,IAAI,KAAK,mBAAmB,IAC7CZ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACW,MAAM,EACvB;MACAV,IAAI,GAAGD,KAAK;;MAEZ;MACA,OAAOC,IAAI,EAAE,EAAE;QACb;QACA,IACEH,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,IAC1BH,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACS,IAAI,KAAK,mBAAmB,IAC5CZ,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACW,KAAK;QACrB;QACAb,OAAO,CAACc,cAAc,CAACf,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAACa,UAAU,CAAC,CAAC,CAAC,KACnDf,OAAO,CAACc,cAAc,CAACf,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAACc,UAAU,CAAC,CAAC,CAAC,EACxD;UACA;UACA;UACA;UACA;UACA,IACE,CAAChB,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACU,MAAM,IAAIb,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACY,KAAK,KACjD,CAACd,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACe,GAAG,CAACP,MAAM,GAAGV,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgB,KAAK,CAACR,MAAM,IAAI,CAAC,IACjE,EACE,CAACV,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc,GAAG,CAACP,MAAM,GACzBV,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACe,KAAK,CAACR,MAAM,GAC5BV,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACe,GAAG,CAACP,MAAM,GAC3BV,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgB,KAAK,CAACR,MAAM,IAC/B,CAAC,CACF,EACD;YACA;UACF;;UAEA;UACAF,GAAG,GACDR,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc,GAAG,CAACP,MAAM,GAAGV,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACe,KAAK,CAACR,MAAM,GAAG,CAAC,IAC7DV,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACe,GAAG,CAACP,MAAM,GAAGV,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgB,KAAK,CAACR,MAAM,GAAG,CAAC,GAC3D,CAAC,GACD,CAAC;UAEP,MAAMQ,KAAK,GAAG;YAAC,GAAGlB,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc;UAAG,CAAC;UACtC,MAAMA,GAAG,GAAG;YAAC,GAAGjB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgB;UAAK,CAAC;UACvCC,SAAS,CAACD,KAAK,EAAE,CAACV,GAAG,CAAC;UACtBW,SAAS,CAACF,GAAG,EAAET,GAAG,CAAC;UAEnBF,eAAe,GAAG;YAChBM,IAAI,EAAEJ,GAAG,GAAG,CAAC,GAAGd,KAAK,CAAC0B,cAAc,GAAG1B,KAAK,CAAC2B,gBAAgB;YAC7DH,KAAK;YACLD,GAAG,EAAE;cAAC,GAAGjB,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc;YAAG;UAC9B,CAAC;UACDV,eAAe,GAAG;YAChBK,IAAI,EAAEJ,GAAG,GAAG,CAAC,GAAGd,KAAK,CAAC0B,cAAc,GAAG1B,KAAK,CAAC2B,gBAAgB;YAC7DH,KAAK,EAAE;cAAC,GAAGlB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgB;YAAK,CAAC;YAClCD;UACF,CAAC;UACDZ,IAAI,GAAG;YACLO,IAAI,EAAEJ,GAAG,GAAG,CAAC,GAAGd,KAAK,CAAC4B,UAAU,GAAG5B,KAAK,CAAC6B,YAAY;YACrDL,KAAK,EAAE;cAAC,GAAGlB,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc;YAAG,CAAC;YAC/BA,GAAG,EAAE;cAAC,GAAGjB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgB;YAAK;UACjC,CAAC;UACDd,KAAK,GAAG;YACNQ,IAAI,EAAEJ,GAAG,GAAG,CAAC,GAAGd,KAAK,CAAC8B,MAAM,GAAG9B,KAAK,CAAC+B,QAAQ;YAC7CP,KAAK,EAAE;cAAC,GAAGZ,eAAe,CAACY;YAAK,CAAC;YACjCD,GAAG,EAAE;cAAC,GAAGV,eAAe,CAACU;YAAG;UAC9B,CAAC;UAEDjB,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc,GAAG,GAAG;YAAC,GAAGX,eAAe,CAACY;UAAK,CAAC;UAChDlB,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgB,KAAK,GAAG;YAAC,GAAGX,eAAe,CAACU;UAAG,CAAC;UAEjDR,UAAU,GAAG,EAAE;;UAEf;UACA,IAAIT,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACc,GAAG,CAACP,MAAM,GAAGV,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,CAACe,KAAK,CAACR,MAAM,EAAE;YAC7DD,UAAU,GAAGrB,IAAI,CAACqB,UAAU,EAAE,CAC5B,CAAC,OAAO,EAAET,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEF,OAAO,CAAC,EACnC,CAAC,MAAM,EAAED,MAAM,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEF,OAAO,CAAC,CACnC,CAAC;UACJ;;UAEA;UACAQ,UAAU,GAAGrB,IAAI,CAACqB,UAAU,EAAE,CAC5B,CAAC,OAAO,EAAEL,KAAK,EAAEH,OAAO,CAAC,EACzB,CAAC,OAAO,EAAEK,eAAe,EAAEL,OAAO,CAAC,EACnC,CAAC,MAAM,EAAEK,eAAe,EAAEL,OAAO,CAAC,EAClC,CAAC,OAAO,EAAEI,IAAI,EAAEJ,OAAO,CAAC,CACzB,CAAC;;UAEF;UACAd,MAAM,CACJc,OAAO,CAACyB,MAAM,CAACC,UAAU,CAACC,UAAU,CAACC,IAAI,EACzC,uCACF,CAAC;;UAED;UACApB,UAAU,GAAGrB,IAAI,CACfqB,UAAU,EACVlB,UAAU,CACRU,OAAO,CAACyB,MAAM,CAACC,UAAU,CAACC,UAAU,CAACC,IAAI,EACzC7B,MAAM,CAAC8B,KAAK,CAAC3B,IAAI,GAAG,CAAC,EAAED,KAAK,CAAC,EAC7BD,OACF,CACF,CAAC;;UAED;UACAQ,UAAU,GAAGrB,IAAI,CAACqB,UAAU,EAAE,CAC5B,CAAC,MAAM,EAAEJ,IAAI,EAAEJ,OAAO,CAAC,EACvB,CAAC,OAAO,EAAEM,eAAe,EAAEN,OAAO,CAAC,EACnC,CAAC,MAAM,EAAEM,eAAe,EAAEN,OAAO,CAAC,EAClC,CAAC,MAAM,EAAEG,KAAK,EAAEH,OAAO,CAAC,CACzB,CAAC;;UAEF;UACA,IAAID,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACe,GAAG,CAACP,MAAM,GAAGV,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACgB,KAAK,CAACR,MAAM,EAAE;YAC/DA,MAAM,GAAG,CAAC;YACVD,UAAU,GAAGrB,IAAI,CAACqB,UAAU,EAAE,CAC5B,CAAC,OAAO,EAAET,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,EACpC,CAAC,MAAM,EAAED,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CACpC,CAAC;UACJ,CAAC,MAAM;YACLS,MAAM,GAAG,CAAC;UACZ;UAEArB,MAAM,CAACW,MAAM,EAAEG,IAAI,GAAG,CAAC,EAAED,KAAK,GAAGC,IAAI,GAAG,CAAC,EAAEM,UAAU,CAAC;UAEtDP,KAAK,GAAGC,IAAI,GAAGM,UAAU,CAACE,MAAM,GAAGD,MAAM,GAAG,CAAC;UAC7C;QACF;MACF;IACF;EACF;;EAEA;EACAR,KAAK,GAAG,CAAC,CAAC;EAEV,OAAO,EAAEA,KAAK,GAAGF,MAAM,CAACW,MAAM,EAAE;IAC9B,IAAIX,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACU,IAAI,KAAK,mBAAmB,EAAE;MACjDZ,MAAM,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC,CAACU,IAAI,GAAG,MAAM;IAChC;EACF;EAEA,OAAOZ,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASD,iBAAiBA,CAACgC,OAAO,EAAE7C,EAAE,EAAE;EACtC,MAAM8C,gBAAgB,GAAG,IAAI,CAACN,MAAM,CAACC,UAAU,CAACK,gBAAgB,CAACH,IAAI;EACrE,MAAMI,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC9B,MAAMC,MAAM,GAAG5C,iBAAiB,CAAC2C,QAAQ,CAAC;;EAE1C;EACA,IAAIE,MAAM;EAEV,OAAOjB,KAAK;;EAEZ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASA,KAAKA,CAACkB,IAAI,EAAE;IACnBjD,MAAM,CACJiD,IAAI,KAAK5C,KAAK,CAAC6C,QAAQ,IAAID,IAAI,KAAK5C,KAAK,CAAC8C,UAAU,EACpD,iCACF,CAAC;IACDH,MAAM,GAAGC,IAAI;IACbL,OAAO,CAACQ,KAAK,CAAC,mBAAmB,CAAC;IAClC,OAAOC,MAAM,CAACJ,IAAI,CAAC;EACrB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASI,MAAMA,CAACJ,IAAI,EAAE;IACpB,IAAIA,IAAI,KAAKD,MAAM,EAAE;MACnBJ,OAAO,CAACU,OAAO,CAACL,IAAI,CAAC;MACrB,OAAOI,MAAM;IACf;IAEA,MAAME,KAAK,GAAGX,OAAO,CAACY,IAAI,CAAC,mBAAmB,CAAC;;IAE/C;IACA,MAAMC,KAAK,GAAGtD,iBAAiB,CAAC8C,IAAI,CAAC;;IAErC;IACAjD,MAAM,CAAC6C,gBAAgB,EAAE,6CAA6C,CAAC;IAEvE,MAAM7B,IAAI,GACR,CAACyC,KAAK,IACLA,KAAK,KAAKnD,SAAS,CAACoD,yBAAyB,IAAIX,MAAO,IACzDF,gBAAgB,CAACc,QAAQ,CAACV,IAAI,CAAC;IACjC,MAAMW,KAAK,GACT,CAACb,MAAM,IACNA,MAAM,KAAKzC,SAAS,CAACoD,yBAAyB,IAAID,KAAM,IACzDZ,gBAAgB,CAACc,QAAQ,CAACb,QAAQ,CAAC;IAErCS,KAAK,CAAC5B,KAAK,GAAGkC,OAAO,CACnBb,MAAM,KAAK3C,KAAK,CAAC6C,QAAQ,GAAGlC,IAAI,GAAGA,IAAI,KAAK+B,MAAM,IAAI,CAACa,KAAK,CAC9D,CAAC;IACDL,KAAK,CAAC7B,MAAM,GAAGmC,OAAO,CACpBb,MAAM,KAAK3C,KAAK,CAAC6C,QAAQ,GAAGU,KAAK,GAAGA,KAAK,KAAKH,KAAK,IAAI,CAACzC,IAAI,CAC9D,CAAC;IACD,OAAOjB,EAAE,CAACkD,IAAI,CAAC;EACjB;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjB,SAASA,CAAC8B,KAAK,EAAEvC,MAAM,EAAE;EAChCuC,KAAK,CAACC,MAAM,IAAIxC,MAAM;EACtBuC,KAAK,CAACvC,MAAM,IAAIA,MAAM;EACtBuC,KAAK,CAACE,YAAY,IAAIzC,MAAM;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}