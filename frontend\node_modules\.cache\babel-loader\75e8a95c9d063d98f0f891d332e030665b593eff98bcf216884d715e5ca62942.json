{"ast": null, "code": "/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */\n\nimport createDebug from 'debug';\nimport { ok as assert } from 'devlop';\nimport { markdownLineEnding } from 'micromark-util-character';\nimport { push, splice } from 'micromark-util-chunked';\nimport { resolveAll } from 'micromark-util-resolve-all';\nimport { codes, values } from 'micromark-util-symbol';\nconst debug = createDebug('micromark');\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */\nexport function createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = {\n    _bufferIndex: -1,\n    _index: 0,\n    line: from && from.line || 1,\n    column: from && from.column || 1,\n    offset: from && from.offset || 0\n  };\n  /** @type {Record<string, number>} */\n  const columnStart = {};\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = [];\n  /** @type {Array<Chunk>} */\n  let chunks = [];\n  /** @type {Array<Token>} */\n  let stack = [];\n  /** @type {boolean | undefined} */\n  let consumed = true;\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    consume,\n    enter,\n    exit,\n    interrupt: constructFactory(onsuccessfulcheck, {\n      interrupt: true\n    })\n  };\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    code: codes.eof,\n    containerState: {},\n    defineSkip,\n    events: [],\n    now,\n    parser,\n    previous: codes.eof,\n    sliceSerialize,\n    sliceStream,\n    write\n  };\n\n  /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */\n  let state = initialize.tokenize.call(context, effects);\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode;\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize);\n  }\n  return context;\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = push(chunks, slice);\n    main();\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== codes.eof) {\n      return [];\n    }\n    addResult(initialize, 0);\n\n    // Otherwise, resolve, and exit.\n    context.events = resolveAll(resolveAllConstructs, context.events, context);\n    return context.events;\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs);\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token);\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {\n      _bufferIndex,\n      _index,\n      line,\n      column,\n      offset\n    } = point;\n    return {\n      _bufferIndex,\n      _index,\n      line,\n      column,\n      offset\n    };\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column;\n    accountForPotentialSkip();\n    debug('position: define skip: `%j`', point);\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex;\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index];\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index;\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0;\n        }\n        while (point._index === chunkIndex && point._bufferIndex < chunk.length) {\n          go(chunk.charCodeAt(point._bufferIndex));\n        }\n      } else {\n        go(chunk);\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function go(code) {\n    assert(consumed === true, 'expected character to be consumed');\n    consumed = undefined;\n    debug('main: passing `%s` to %s', code, state && state.name);\n    expectedCode = code;\n    assert(typeof state === 'function', 'expected state');\n    state = state(code);\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    assert(code === expectedCode, 'expected given code to equal expected code');\n    debug('consume: `%s`', code);\n    assert(consumed === undefined, 'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used');\n    assert(code === null ? context.events.length === 0 || context.events[context.events.length - 1][0] === 'exit' : context.events[context.events.length - 1][0] === 'enter', 'expected last token to be open');\n    if (markdownLineEnding(code)) {\n      point.line++;\n      point.column = 1;\n      point.offset += code === codes.carriageReturnLineFeed ? 2 : 1;\n      accountForPotentialSkip();\n      debug('position: after eol: `%j`', point);\n    } else if (code !== codes.virtualSpace) {\n      point.column++;\n      point.offset++;\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++;\n    } else {\n      point._bufferIndex++;\n\n      // At end of string chunk.\n      if (point._bufferIndex ===\n      // Points w/ non-negative `_bufferIndex` reference\n      // strings.\n      /** @type {string} */\n      chunks[point._index].length) {\n        point._bufferIndex = -1;\n        point._index++;\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code;\n\n    // Mark as consumed.\n    consumed = true;\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {};\n    token.type = type;\n    token.start = now();\n    assert(typeof type === 'string', 'expected string type');\n    assert(type.length > 0, 'expected non-empty string');\n    debug('enter: `%s`', type);\n    context.events.push(['enter', token, context]);\n    stack.push(token);\n    return token;\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    assert(typeof type === 'string', 'expected string type');\n    assert(type.length > 0, 'expected non-empty string');\n    const token = stack.pop();\n    assert(token, 'cannot close w/o open tokens');\n    token.end = now();\n    assert(type === token.type, 'expected exit token to match current token');\n    assert(!(token.start._index === token.end._index && token.start._bufferIndex === token.end._bufferIndex), 'expected non-empty token (`' + type + '`)');\n    debug('exit: `%s`', token.type);\n    context.events.push(['exit', token, context]);\n    return token;\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from);\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore();\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */\n  function constructFactory(onreturn, fields) {\n    return hook;\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {ReadonlyArray<Construct>} */\n      let listOfConstructs;\n      /** @type {number} */\n      let constructIndex;\n      /** @type {Construct} */\n      let currentConstruct;\n      /** @type {Info} */\n      let info;\n      return Array.isArray(constructs) ? /* c8 ignore next 1 */\n      handleListOfConstructs(constructs) : 'tokenize' in constructs ?\n      // Looks like a construct.\n      handleListOfConstructs([(/** @type {Construct} */constructs)]) : handleMapOfConstructs(constructs);\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleMapOfConstructs(map) {\n        return start;\n\n        /** @type {State} */\n        function start(code) {\n          const left = code !== null && map[code];\n          const all = code !== null && map.null;\n          const list = [\n          // To do: add more extension tests.\n          /* c8 ignore next 2 */\n          ...(Array.isArray(left) ? left : left ? [left] : []), ...(Array.isArray(all) ? all : all ? [all] : [])];\n          return handleListOfConstructs(list)(code);\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list;\n        constructIndex = 0;\n        if (list.length === 0) {\n          assert(bogusState, 'expected `bogusState` to be given');\n          return bogusState;\n        }\n        return handleConstruct(list[constructIndex]);\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */\n      function handleConstruct(construct) {\n        return start;\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store();\n          currentConstruct = construct;\n          if (!construct.partial) {\n            context.currentConstruct = construct;\n          }\n\n          // Always populated by defaults.\n          assert(context.parser.constructs.disable.null, 'expected `disable.null` to be populated');\n          if (construct.name && context.parser.constructs.disable.null.includes(construct.name)) {\n            return nok(code);\n          }\n          return construct.tokenize.call(\n          // If we do have fields, create an object w/ `context` as its\n          // prototype.\n          // This allows a “live binding”, which is needed for `interrupt`.\n          fields ? Object.assign(Object.create(context), fields) : context, effects, ok, nok)(code);\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        assert(code === expectedCode, 'expected code');\n        consumed = true;\n        onreturn(currentConstruct, info);\n        return returnState;\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        assert(code === expectedCode, 'expected code');\n        consumed = true;\n        info.restore();\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex]);\n        }\n        return bogusState;\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct);\n    }\n    if (construct.resolve) {\n      splice(context.events, from, context.events.length - from, construct.resolve(context.events.slice(from), context));\n    }\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context);\n    }\n    assert(construct.partial || context.events.length === 0 || context.events[context.events.length - 1][0] === 'exit', 'expected last token to end');\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */\n  function store() {\n    const startPoint = now();\n    const startPrevious = context.previous;\n    const startCurrentConstruct = context.currentConstruct;\n    const startEventsIndex = context.events.length;\n    const startStack = Array.from(stack);\n    return {\n      from: startEventsIndex,\n      restore\n    };\n\n    /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */\n    function restore() {\n      point = startPoint;\n      context.previous = startPrevious;\n      context.currentConstruct = startCurrentConstruct;\n      context.events.length = startEventsIndex;\n      stack = startStack;\n      accountForPotentialSkip();\n      debug('position: restore: `%j`', point);\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line];\n      point.offset += columnStart[point.line] - 1;\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index;\n  const startBufferIndex = token.start._bufferIndex;\n  const endIndex = token.end._index;\n  const endBufferIndex = token.end._bufferIndex;\n  /** @type {Array<Chunk>} */\n  let view;\n  if (startIndex === endIndex) {\n    assert(endBufferIndex > -1, 'expected non-negative end buffer index');\n    assert(startBufferIndex > -1, 'expected non-negative start buffer index');\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)];\n  } else {\n    view = chunks.slice(startIndex, endIndex);\n    if (startBufferIndex > -1) {\n      const head = view[0];\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex);\n        /* c8 ignore next 4 -- used to be used, no longer */\n      } else {\n        assert(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`');\n        view.shift();\n      }\n    }\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex));\n    }\n  }\n  return view;\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1;\n  /** @type {Array<string>} */\n  const result = [];\n  /** @type {boolean | undefined} */\n  let atTab;\n  while (++index < chunks.length) {\n    const chunk = chunks[index];\n    /** @type {string} */\n    let value;\n    if (typeof chunk === 'string') {\n      value = chunk;\n    } else switch (chunk) {\n      case codes.carriageReturn:\n        {\n          value = values.cr;\n          break;\n        }\n      case codes.lineFeed:\n        {\n          value = values.lf;\n          break;\n        }\n      case codes.carriageReturnLineFeed:\n        {\n          value = values.cr + values.lf;\n          break;\n        }\n      case codes.horizontalTab:\n        {\n          value = expandTabs ? values.space : values.ht;\n          break;\n        }\n      case codes.virtualSpace:\n        {\n          if (!expandTabs && atTab) continue;\n          value = values.space;\n          break;\n        }\n      default:\n        {\n          assert(typeof chunk === 'number', 'expected number');\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk);\n        }\n    }\n    atTab = chunk === codes.horizontalTab;\n    result.push(value);\n  }\n  return result.join('');\n}", "map": {"version": 3, "names": ["createDebug", "ok", "assert", "markdownLineEnding", "push", "splice", "resolveAll", "codes", "values", "debug", "createTokenizer", "parser", "initialize", "from", "point", "_bufferIndex", "_index", "line", "column", "offset", "columnStart", "resolveAllConstructs", "chunks", "stack", "consumed", "effects", "attempt", "constructFactory", "onsuccessfulconstruct", "check", "onsuccessfulcheck", "consume", "enter", "exit", "interrupt", "context", "code", "eof", "containerState", "defineSkip", "events", "now", "previous", "sliceSerialize", "sliceStream", "write", "state", "tokenize", "call", "expectedCode", "slice", "main", "length", "addResult", "token", "expandTabs", "serializeChunks", "sliceChunks", "value", "accountForPotentialSkip", "chunkIndex", "chunk", "go", "charCodeAt", "undefined", "name", "carriageReturnLineFeed", "virtualSpace", "type", "fields", "start", "pop", "end", "construct", "info", "_", "restore", "onreturn", "hook", "constructs", "returnState", "bogusState", "listOfConstructs", "constructIndex", "currentConstruct", "Array", "isArray", "handleListOfConstructs", "handleMapOfConstructs", "map", "left", "all", "null", "list", "handleConstruct", "store", "partial", "disable", "includes", "nok", "Object", "assign", "create", "resolve", "resolveTo", "startPoint", "startPrevious", "startCurrentConstruct", "startEventsIndex", "startStack", "startIndex", "startBufferIndex", "endIndex", "endBufferIndex", "view", "head", "shift", "index", "result", "atTab", "carriageReturn", "cr", "lineFeed", "lf", "horizontalTab", "space", "ht", "String", "fromCharCode", "join"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/micromark/dev/lib/create-tokenizer.js"], "sourcesContent": ["/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */\n\nimport createDebug from 'debug'\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, values} from 'micromark-util-symbol'\n\nconst debug = createDebug('micromark')\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */\nexport function createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = {\n    _bufferIndex: -1,\n    _index: 0,\n    line: (from && from.line) || 1,\n    column: (from && from.column) || 1,\n    offset: (from && from.offset) || 0\n  }\n  /** @type {Record<string, number>} */\n  const columnStart = {}\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = []\n  /** @type {Array<Chunk>} */\n  let chunks = []\n  /** @type {Array<Token>} */\n  let stack = []\n  /** @type {boolean | undefined} */\n  let consumed = true\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    consume,\n    enter,\n    exit,\n    interrupt: constructFactory(onsuccessfulcheck, {interrupt: true})\n  }\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    code: codes.eof,\n    containerState: {},\n    defineSkip,\n    events: [],\n    now,\n    parser,\n    previous: codes.eof,\n    sliceSerialize,\n    sliceStream,\n    write\n  }\n\n  /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */\n  let state = initialize.tokenize.call(context, effects)\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  }\n\n  return context\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = push(chunks, slice)\n\n    main()\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== codes.eof) {\n      return []\n    }\n\n    addResult(initialize, 0)\n\n    // Otherwise, resolve, and exit.\n    context.events = resolveAll(resolveAllConstructs, context.events, context)\n\n    return context.events\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs)\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {_bufferIndex, _index, line, column, offset} = point\n    return {_bufferIndex, _index, line, column, offset}\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n    debug('position: define skip: `%j`', point)\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex\n\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index]\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function go(code) {\n    assert(consumed === true, 'expected character to be consumed')\n    consumed = undefined\n    debug('main: passing `%s` to %s', code, state && state.name)\n    expectedCode = code\n    assert(typeof state === 'function', 'expected state')\n    state = state(code)\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    assert(code === expectedCode, 'expected given code to equal expected code')\n\n    debug('consume: `%s`', code)\n\n    assert(\n      consumed === undefined,\n      'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used'\n    )\n    assert(\n      code === null\n        ? context.events.length === 0 ||\n            context.events[context.events.length - 1][0] === 'exit'\n        : context.events[context.events.length - 1][0] === 'enter',\n      'expected last token to be open'\n    )\n\n    if (markdownLineEnding(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === codes.carriageReturnLineFeed ? 2 : 1\n      accountForPotentialSkip()\n      debug('position: after eol: `%j`', point)\n    } else if (code !== codes.virtualSpace) {\n      point.column++\n      point.offset++\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++\n\n      // At end of string chunk.\n      if (\n        point._bufferIndex ===\n        // Points w/ non-negative `_bufferIndex` reference\n        // strings.\n        /** @type {string} */ (chunks[point._index]).length\n      ) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code\n\n    // Mark as consumed.\n    consumed = true\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {}\n    token.type = type\n    token.start = now()\n\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n    debug('enter: `%s`', type)\n\n    context.events.push(['enter', token, context])\n\n    stack.push(token)\n\n    return token\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n\n    const token = stack.pop()\n    assert(token, 'cannot close w/o open tokens')\n    token.end = now()\n\n    assert(type === token.type, 'expected exit token to match current token')\n\n    assert(\n      !(\n        token.start._index === token.end._index &&\n        token.start._bufferIndex === token.end._bufferIndex\n      ),\n      'expected non-empty token (`' + type + '`)'\n    )\n\n    debug('exit: `%s`', token.type)\n    context.events.push(['exit', token, context])\n\n    return token\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore()\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */\n  function constructFactory(onreturn, fields) {\n    return hook\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {ReadonlyArray<Construct>} */\n      let listOfConstructs\n      /** @type {number} */\n      let constructIndex\n      /** @type {Construct} */\n      let currentConstruct\n      /** @type {Info} */\n      let info\n\n      return Array.isArray(constructs)\n        ? /* c8 ignore next 1 */\n          handleListOfConstructs(constructs)\n        : 'tokenize' in constructs\n          ? // Looks like a construct.\n            handleListOfConstructs([/** @type {Construct} */ (constructs)])\n          : handleMapOfConstructs(constructs)\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleMapOfConstructs(map) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          const left = code !== null && map[code]\n          const all = code !== null && map.null\n          const list = [\n            // To do: add more extension tests.\n            /* c8 ignore next 2 */\n            ...(Array.isArray(left) ? left : left ? [left] : []),\n            ...(Array.isArray(all) ? all : all ? [all] : [])\n          ]\n\n          return handleListOfConstructs(list)(code)\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n\n        if (list.length === 0) {\n          assert(bogusState, 'expected `bogusState` to be given')\n          return bogusState\n        }\n\n        return handleConstruct(list[constructIndex])\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */\n      function handleConstruct(construct) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.disable.null,\n            'expected `disable.null` to be populated'\n          )\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.includes(construct.name)\n          ) {\n            return nok(code)\n          }\n\n          return construct.tokenize.call(\n            // If we do have fields, create an object w/ `context` as its\n            // prototype.\n            // This allows a “live binding”, which is needed for `interrupt`.\n            fields ? Object.assign(Object.create(context), fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      splice(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n\n    assert(\n      construct.partial ||\n        context.events.length === 0 ||\n        context.events[context.events.length - 1][0] === 'exit',\n      'expected last token to end'\n    )\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */\n  function store() {\n    const startPoint = now()\n    const startPrevious = context.previous\n    const startCurrentConstruct = context.currentConstruct\n    const startEventsIndex = context.events.length\n    const startStack = Array.from(stack)\n\n    return {from: startEventsIndex, restore}\n\n    /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n      debug('position: restore: `%j`', point)\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index\n  const startBufferIndex = token.start._bufferIndex\n  const endIndex = token.end._index\n  const endBufferIndex = token.end._bufferIndex\n  /** @type {Array<Chunk>} */\n  let view\n\n  if (startIndex === endIndex) {\n    assert(endBufferIndex > -1, 'expected non-negative end buffer index')\n    assert(startBufferIndex > -1, 'expected non-negative start buffer index')\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      const head = view[0]\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex)\n        /* c8 ignore next 4 -- used to be used, no longer */\n      } else {\n        assert(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`')\n        view.shift()\n      }\n    }\n\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case codes.carriageReturn: {\n          value = values.cr\n\n          break\n        }\n\n        case codes.lineFeed: {\n          value = values.lf\n\n          break\n        }\n\n        case codes.carriageReturnLineFeed: {\n          value = values.cr + values.lf\n\n          break\n        }\n\n        case codes.horizontalTab: {\n          value = expandTabs ? values.space : values.ht\n\n          break\n        }\n\n        case codes.virtualSpace: {\n          if (!expandTabs && atTab) continue\n          value = values.space\n\n          break\n        }\n\n        default: {\n          assert(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,WAAW,MAAM,OAAO;AAC/B,SAAQC,EAAE,IAAIC,MAAM,QAAO,QAAQ;AACnC,SAAQC,kBAAkB,QAAO,0BAA0B;AAC3D,SAAQC,IAAI,EAAEC,MAAM,QAAO,wBAAwB;AACnD,SAAQC,UAAU,QAAO,4BAA4B;AACrD,SAAQC,KAAK,EAAEC,MAAM,QAAO,uBAAuB;AAEnD,MAAMC,KAAK,GAAGT,WAAW,CAAC,WAAW,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASU,eAAeA,CAACC,MAAM,EAAEC,UAAU,EAAEC,IAAI,EAAE;EACxD;EACA,IAAIC,KAAK,GAAG;IACVC,YAAY,EAAE,CAAC,CAAC;IAChBC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAGJ,IAAI,IAAIA,IAAI,CAACI,IAAI,IAAK,CAAC;IAC9BC,MAAM,EAAGL,IAAI,IAAIA,IAAI,CAACK,MAAM,IAAK,CAAC;IAClCC,MAAM,EAAGN,IAAI,IAAIA,IAAI,CAACM,MAAM,IAAK;EACnC,CAAC;EACD;EACA,MAAMC,WAAW,GAAG,CAAC,CAAC;EACtB;EACA,MAAMC,oBAAoB,GAAG,EAAE;EAC/B;EACA,IAAIC,MAAM,GAAG,EAAE;EACf;EACA,IAAIC,KAAK,GAAG,EAAE;EACd;EACA,IAAIC,QAAQ,GAAG,IAAI;;EAEnB;AACF;AACA;AACA;AACA;EACE,MAAMC,OAAO,GAAG;IACdC,OAAO,EAAEC,gBAAgB,CAACC,qBAAqB,CAAC;IAChDC,KAAK,EAAEF,gBAAgB,CAACG,iBAAiB,CAAC;IAC1CC,OAAO;IACPC,KAAK;IACLC,IAAI;IACJC,SAAS,EAAEP,gBAAgB,CAACG,iBAAiB,EAAE;MAACI,SAAS,EAAE;IAAI,CAAC;EAClE,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,MAAMC,OAAO,GAAG;IACdC,IAAI,EAAE7B,KAAK,CAAC8B,GAAG;IACfC,cAAc,EAAE,CAAC,CAAC;IAClBC,UAAU;IACVC,MAAM,EAAE,EAAE;IACVC,GAAG;IACH9B,MAAM;IACN+B,QAAQ,EAAEnC,KAAK,CAAC8B,GAAG;IACnBM,cAAc;IACdC,WAAW;IACXC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACE,IAAIC,KAAK,GAAGlC,UAAU,CAACmC,QAAQ,CAACC,IAAI,CAACb,OAAO,EAAEV,OAAO,CAAC;;EAEtD;AACF;AACA;AACA;AACA;EACE,IAAIwB,YAAY;EAEhB,IAAIrC,UAAU,CAACN,UAAU,EAAE;IACzBe,oBAAoB,CAACjB,IAAI,CAACQ,UAAU,CAAC;EACvC;EAEA,OAAOuB,OAAO;;EAEd;EACA,SAASU,KAAKA,CAACK,KAAK,EAAE;IACpB5B,MAAM,GAAGlB,IAAI,CAACkB,MAAM,EAAE4B,KAAK,CAAC;IAE5BC,IAAI,CAAC,CAAC;;IAEN;IACA,IAAI7B,MAAM,CAACA,MAAM,CAAC8B,MAAM,GAAG,CAAC,CAAC,KAAK7C,KAAK,CAAC8B,GAAG,EAAE;MAC3C,OAAO,EAAE;IACX;IAEAgB,SAAS,CAACzC,UAAU,EAAE,CAAC,CAAC;;IAExB;IACAuB,OAAO,CAACK,MAAM,GAAGlC,UAAU,CAACe,oBAAoB,EAAEc,OAAO,CAACK,MAAM,EAAEL,OAAO,CAAC;IAE1E,OAAOA,OAAO,CAACK,MAAM;EACvB;;EAEA;EACA;EACA;;EAEA;EACA,SAASG,cAAcA,CAACW,KAAK,EAAEC,UAAU,EAAE;IACzC,OAAOC,eAAe,CAACZ,WAAW,CAACU,KAAK,CAAC,EAAEC,UAAU,CAAC;EACxD;;EAEA;EACA,SAASX,WAAWA,CAACU,KAAK,EAAE;IAC1B,OAAOG,WAAW,CAACnC,MAAM,EAAEgC,KAAK,CAAC;EACnC;;EAEA;EACA,SAASb,GAAGA,CAAA,EAAG;IACb;IACA,MAAM;MAAC1B,YAAY;MAAEC,MAAM;MAAEC,IAAI;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGL,KAAK;IAC1D,OAAO;MAACC,YAAY;MAAEC,MAAM;MAAEC,IAAI;MAAEC,MAAM;MAAEC;IAAM,CAAC;EACrD;;EAEA;EACA,SAASoB,UAAUA,CAACmB,KAAK,EAAE;IACzBtC,WAAW,CAACsC,KAAK,CAACzC,IAAI,CAAC,GAAGyC,KAAK,CAACxC,MAAM;IACtCyC,uBAAuB,CAAC,CAAC;IACzBlD,KAAK,CAAC,6BAA6B,EAAEK,KAAK,CAAC;EAC7C;;EAEA;EACA;EACA;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASqC,IAAIA,CAAA,EAAG;IACd;IACA,IAAIS,UAAU;IAEd,OAAO9C,KAAK,CAACE,MAAM,GAAGM,MAAM,CAAC8B,MAAM,EAAE;MACnC,MAAMS,KAAK,GAAGvC,MAAM,CAACR,KAAK,CAACE,MAAM,CAAC;;MAElC;MACA,IAAI,OAAO6C,KAAK,KAAK,QAAQ,EAAE;QAC7BD,UAAU,GAAG9C,KAAK,CAACE,MAAM;QAEzB,IAAIF,KAAK,CAACC,YAAY,GAAG,CAAC,EAAE;UAC1BD,KAAK,CAACC,YAAY,GAAG,CAAC;QACxB;QAEA,OACED,KAAK,CAACE,MAAM,KAAK4C,UAAU,IAC3B9C,KAAK,CAACC,YAAY,GAAG8C,KAAK,CAACT,MAAM,EACjC;UACAU,EAAE,CAACD,KAAK,CAACE,UAAU,CAACjD,KAAK,CAACC,YAAY,CAAC,CAAC;QAC1C;MACF,CAAC,MAAM;QACL+C,EAAE,CAACD,KAAK,CAAC;MACX;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,EAAEA,CAAC1B,IAAI,EAAE;IAChBlC,MAAM,CAACsB,QAAQ,KAAK,IAAI,EAAE,mCAAmC,CAAC;IAC9DA,QAAQ,GAAGwC,SAAS;IACpBvD,KAAK,CAAC,0BAA0B,EAAE2B,IAAI,EAAEU,KAAK,IAAIA,KAAK,CAACmB,IAAI,CAAC;IAC5DhB,YAAY,GAAGb,IAAI;IACnBlC,MAAM,CAAC,OAAO4C,KAAK,KAAK,UAAU,EAAE,gBAAgB,CAAC;IACrDA,KAAK,GAAGA,KAAK,CAACV,IAAI,CAAC;EACrB;;EAEA;EACA,SAASL,OAAOA,CAACK,IAAI,EAAE;IACrBlC,MAAM,CAACkC,IAAI,KAAKa,YAAY,EAAE,4CAA4C,CAAC;IAE3ExC,KAAK,CAAC,eAAe,EAAE2B,IAAI,CAAC;IAE5BlC,MAAM,CACJsB,QAAQ,KAAKwC,SAAS,EACtB,gHACF,CAAC;IACD9D,MAAM,CACJkC,IAAI,KAAK,IAAI,GACTD,OAAO,CAACK,MAAM,CAACY,MAAM,KAAK,CAAC,IACzBjB,OAAO,CAACK,MAAM,CAACL,OAAO,CAACK,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,GACzDjB,OAAO,CAACK,MAAM,CAACL,OAAO,CAACK,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAC5D,gCACF,CAAC;IAED,IAAIjD,kBAAkB,CAACiC,IAAI,CAAC,EAAE;MAC5BtB,KAAK,CAACG,IAAI,EAAE;MACZH,KAAK,CAACI,MAAM,GAAG,CAAC;MAChBJ,KAAK,CAACK,MAAM,IAAIiB,IAAI,KAAK7B,KAAK,CAAC2D,sBAAsB,GAAG,CAAC,GAAG,CAAC;MAC7DP,uBAAuB,CAAC,CAAC;MACzBlD,KAAK,CAAC,2BAA2B,EAAEK,KAAK,CAAC;IAC3C,CAAC,MAAM,IAAIsB,IAAI,KAAK7B,KAAK,CAAC4D,YAAY,EAAE;MACtCrD,KAAK,CAACI,MAAM,EAAE;MACdJ,KAAK,CAACK,MAAM,EAAE;IAChB;;IAEA;IACA,IAAIL,KAAK,CAACC,YAAY,GAAG,CAAC,EAAE;MAC1BD,KAAK,CAACE,MAAM,EAAE;IAChB,CAAC,MAAM;MACLF,KAAK,CAACC,YAAY,EAAE;;MAEpB;MACA,IACED,KAAK,CAACC,YAAY;MAClB;MACA;MACA;MAAuBO,MAAM,CAACR,KAAK,CAACE,MAAM,CAAC,CAAEoC,MAAM,EACnD;QACAtC,KAAK,CAACC,YAAY,GAAG,CAAC,CAAC;QACvBD,KAAK,CAACE,MAAM,EAAE;MAChB;IACF;;IAEA;IACAmB,OAAO,CAACO,QAAQ,GAAGN,IAAI;;IAEvB;IACAZ,QAAQ,GAAG,IAAI;EACjB;;EAEA;EACA,SAASQ,KAAKA,CAACoC,IAAI,EAAEC,MAAM,EAAE;IAC3B;IACA;IACA,MAAMf,KAAK,GAAGe,MAAM,IAAI,CAAC,CAAC;IAC1Bf,KAAK,CAACc,IAAI,GAAGA,IAAI;IACjBd,KAAK,CAACgB,KAAK,GAAG7B,GAAG,CAAC,CAAC;IAEnBvC,MAAM,CAAC,OAAOkE,IAAI,KAAK,QAAQ,EAAE,sBAAsB,CAAC;IACxDlE,MAAM,CAACkE,IAAI,CAAChB,MAAM,GAAG,CAAC,EAAE,2BAA2B,CAAC;IACpD3C,KAAK,CAAC,aAAa,EAAE2D,IAAI,CAAC;IAE1BjC,OAAO,CAACK,MAAM,CAACpC,IAAI,CAAC,CAAC,OAAO,EAAEkD,KAAK,EAAEnB,OAAO,CAAC,CAAC;IAE9CZ,KAAK,CAACnB,IAAI,CAACkD,KAAK,CAAC;IAEjB,OAAOA,KAAK;EACd;;EAEA;EACA,SAASrB,IAAIA,CAACmC,IAAI,EAAE;IAClBlE,MAAM,CAAC,OAAOkE,IAAI,KAAK,QAAQ,EAAE,sBAAsB,CAAC;IACxDlE,MAAM,CAACkE,IAAI,CAAChB,MAAM,GAAG,CAAC,EAAE,2BAA2B,CAAC;IAEpD,MAAME,KAAK,GAAG/B,KAAK,CAACgD,GAAG,CAAC,CAAC;IACzBrE,MAAM,CAACoD,KAAK,EAAE,8BAA8B,CAAC;IAC7CA,KAAK,CAACkB,GAAG,GAAG/B,GAAG,CAAC,CAAC;IAEjBvC,MAAM,CAACkE,IAAI,KAAKd,KAAK,CAACc,IAAI,EAAE,4CAA4C,CAAC;IAEzElE,MAAM,CACJ,EACEoD,KAAK,CAACgB,KAAK,CAACtD,MAAM,KAAKsC,KAAK,CAACkB,GAAG,CAACxD,MAAM,IACvCsC,KAAK,CAACgB,KAAK,CAACvD,YAAY,KAAKuC,KAAK,CAACkB,GAAG,CAACzD,YAAY,CACpD,EACD,6BAA6B,GAAGqD,IAAI,GAAG,IACzC,CAAC;IAED3D,KAAK,CAAC,YAAY,EAAE6C,KAAK,CAACc,IAAI,CAAC;IAC/BjC,OAAO,CAACK,MAAM,CAACpC,IAAI,CAAC,CAAC,MAAM,EAAEkD,KAAK,EAAEnB,OAAO,CAAC,CAAC;IAE7C,OAAOmB,KAAK;EACd;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS1B,qBAAqBA,CAAC6C,SAAS,EAAEC,IAAI,EAAE;IAC9CrB,SAAS,CAACoB,SAAS,EAAEC,IAAI,CAAC7D,IAAI,CAAC;EACjC;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASiB,iBAAiBA,CAAC6C,CAAC,EAAED,IAAI,EAAE;IAClCA,IAAI,CAACE,OAAO,CAAC,CAAC;EAChB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASjD,gBAAgBA,CAACkD,QAAQ,EAAER,MAAM,EAAE;IAC1C,OAAOS,IAAI;;IAEX;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,SAASA,IAAIA,CAACC,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAE;MACjD;MACA,IAAIC,gBAAgB;MACpB;MACA,IAAIC,cAAc;MAClB;MACA,IAAIC,gBAAgB;MACpB;MACA,IAAIV,IAAI;MAER,OAAOW,KAAK,CAACC,OAAO,CAACP,UAAU,CAAC,GAC5B;MACAQ,sBAAsB,CAACR,UAAU,CAAC,GAClC,UAAU,IAAIA,UAAU;MACtB;MACAQ,sBAAsB,CAAC,EAAC,wBAA0BR,UAAU,EAAE,CAAC,GAC/DS,qBAAqB,CAACT,UAAU,CAAC;;MAEvC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACM,SAASS,qBAAqBA,CAACC,GAAG,EAAE;QAClC,OAAOnB,KAAK;;QAEZ;QACA,SAASA,KAAKA,CAAClC,IAAI,EAAE;UACnB,MAAMsD,IAAI,GAAGtD,IAAI,KAAK,IAAI,IAAIqD,GAAG,CAACrD,IAAI,CAAC;UACvC,MAAMuD,GAAG,GAAGvD,IAAI,KAAK,IAAI,IAAIqD,GAAG,CAACG,IAAI;UACrC,MAAMC,IAAI,GAAG;UACX;UACA;UACA,IAAIR,KAAK,CAACC,OAAO,CAACI,IAAI,CAAC,GAAGA,IAAI,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC,GAAG,EAAE,CAAC,EACpD,IAAIL,KAAK,CAACC,OAAO,CAACK,GAAG,CAAC,GAAGA,GAAG,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC,GAAG,EAAE,CAAC,CACjD;UAED,OAAOJ,sBAAsB,CAACM,IAAI,CAAC,CAACzD,IAAI,CAAC;QAC3C;MACF;;MAEA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACM,SAASmD,sBAAsBA,CAACM,IAAI,EAAE;QACpCX,gBAAgB,GAAGW,IAAI;QACvBV,cAAc,GAAG,CAAC;QAElB,IAAIU,IAAI,CAACzC,MAAM,KAAK,CAAC,EAAE;UACrBlD,MAAM,CAAC+E,UAAU,EAAE,mCAAmC,CAAC;UACvD,OAAOA,UAAU;QACnB;QAEA,OAAOa,eAAe,CAACD,IAAI,CAACV,cAAc,CAAC,CAAC;MAC9C;;MAEA;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACM,SAASW,eAAeA,CAACrB,SAAS,EAAE;QAClC,OAAOH,KAAK;;QAEZ;QACA,SAASA,KAAKA,CAAClC,IAAI,EAAE;UACnB;UACA;UACA;UACA;UACAsC,IAAI,GAAGqB,KAAK,CAAC,CAAC;UACdX,gBAAgB,GAAGX,SAAS;UAE5B,IAAI,CAACA,SAAS,CAACuB,OAAO,EAAE;YACtB7D,OAAO,CAACiD,gBAAgB,GAAGX,SAAS;UACtC;;UAEA;UACAvE,MAAM,CACJiC,OAAO,CAACxB,MAAM,CAACoE,UAAU,CAACkB,OAAO,CAACL,IAAI,EACtC,yCACF,CAAC;UAED,IACEnB,SAAS,CAACR,IAAI,IACd9B,OAAO,CAACxB,MAAM,CAACoE,UAAU,CAACkB,OAAO,CAACL,IAAI,CAACM,QAAQ,CAACzB,SAAS,CAACR,IAAI,CAAC,EAC/D;YACA,OAAOkC,GAAG,CAAC/D,IAAI,CAAC;UAClB;UAEA,OAAOqC,SAAS,CAAC1B,QAAQ,CAACC,IAAI;UAC5B;UACA;UACA;UACAqB,MAAM,GAAG+B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACE,MAAM,CAACnE,OAAO,CAAC,EAAEkC,MAAM,CAAC,GAAGlC,OAAO,EAChEV,OAAO,EACPxB,EAAE,EACFkG,GACF,CAAC,CAAC/D,IAAI,CAAC;QACT;MACF;;MAEA;MACA,SAASnC,EAAEA,CAACmC,IAAI,EAAE;QAChBlC,MAAM,CAACkC,IAAI,KAAKa,YAAY,EAAE,eAAe,CAAC;QAC9CzB,QAAQ,GAAG,IAAI;QACfqD,QAAQ,CAACO,gBAAgB,EAAEV,IAAI,CAAC;QAChC,OAAOM,WAAW;MACpB;;MAEA;MACA,SAASmB,GAAGA,CAAC/D,IAAI,EAAE;QACjBlC,MAAM,CAACkC,IAAI,KAAKa,YAAY,EAAE,eAAe,CAAC;QAC9CzB,QAAQ,GAAG,IAAI;QACfkD,IAAI,CAACE,OAAO,CAAC,CAAC;QAEd,IAAI,EAAEO,cAAc,GAAGD,gBAAgB,CAAC9B,MAAM,EAAE;UAC9C,OAAO0C,eAAe,CAACZ,gBAAgB,CAACC,cAAc,CAAC,CAAC;QAC1D;QAEA,OAAOF,UAAU;MACnB;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS5B,SAASA,CAACoB,SAAS,EAAE5D,IAAI,EAAE;IAClC,IAAI4D,SAAS,CAACnE,UAAU,IAAI,CAACe,oBAAoB,CAAC6E,QAAQ,CAACzB,SAAS,CAAC,EAAE;MACrEpD,oBAAoB,CAACjB,IAAI,CAACqE,SAAS,CAAC;IACtC;IAEA,IAAIA,SAAS,CAAC8B,OAAO,EAAE;MACrBlG,MAAM,CACJ8B,OAAO,CAACK,MAAM,EACd3B,IAAI,EACJsB,OAAO,CAACK,MAAM,CAACY,MAAM,GAAGvC,IAAI,EAC5B4D,SAAS,CAAC8B,OAAO,CAACpE,OAAO,CAACK,MAAM,CAACU,KAAK,CAACrC,IAAI,CAAC,EAAEsB,OAAO,CACvD,CAAC;IACH;IAEA,IAAIsC,SAAS,CAAC+B,SAAS,EAAE;MACvBrE,OAAO,CAACK,MAAM,GAAGiC,SAAS,CAAC+B,SAAS,CAACrE,OAAO,CAACK,MAAM,EAAEL,OAAO,CAAC;IAC/D;IAEAjC,MAAM,CACJuE,SAAS,CAACuB,OAAO,IACf7D,OAAO,CAACK,MAAM,CAACY,MAAM,KAAK,CAAC,IAC3BjB,OAAO,CAACK,MAAM,CAACL,OAAO,CAACK,MAAM,CAACY,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,EACzD,4BACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS2C,KAAKA,CAAA,EAAG;IACf,MAAMU,UAAU,GAAGhE,GAAG,CAAC,CAAC;IACxB,MAAMiE,aAAa,GAAGvE,OAAO,CAACO,QAAQ;IACtC,MAAMiE,qBAAqB,GAAGxE,OAAO,CAACiD,gBAAgB;IACtD,MAAMwB,gBAAgB,GAAGzE,OAAO,CAACK,MAAM,CAACY,MAAM;IAC9C,MAAMyD,UAAU,GAAGxB,KAAK,CAACxE,IAAI,CAACU,KAAK,CAAC;IAEpC,OAAO;MAACV,IAAI,EAAE+F,gBAAgB;MAAEhC;IAAO,CAAC;;IAExC;AACJ;AACA;AACA;AACA;AACA;IACI,SAASA,OAAOA,CAAA,EAAG;MACjB9D,KAAK,GAAG2F,UAAU;MAClBtE,OAAO,CAACO,QAAQ,GAAGgE,aAAa;MAChCvE,OAAO,CAACiD,gBAAgB,GAAGuB,qBAAqB;MAChDxE,OAAO,CAACK,MAAM,CAACY,MAAM,GAAGwD,gBAAgB;MACxCrF,KAAK,GAAGsF,UAAU;MAClBlD,uBAAuB,CAAC,CAAC;MACzBlD,KAAK,CAAC,yBAAyB,EAAEK,KAAK,CAAC;IACzC;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAAS6C,uBAAuBA,CAAA,EAAG;IACjC,IAAI7C,KAAK,CAACG,IAAI,IAAIG,WAAW,IAAIN,KAAK,CAACI,MAAM,GAAG,CAAC,EAAE;MACjDJ,KAAK,CAACI,MAAM,GAAGE,WAAW,CAACN,KAAK,CAACG,IAAI,CAAC;MACtCH,KAAK,CAACK,MAAM,IAAIC,WAAW,CAACN,KAAK,CAACG,IAAI,CAAC,GAAG,CAAC;IAC7C;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwC,WAAWA,CAACnC,MAAM,EAAEgC,KAAK,EAAE;EAClC,MAAMwD,UAAU,GAAGxD,KAAK,CAACgB,KAAK,CAACtD,MAAM;EACrC,MAAM+F,gBAAgB,GAAGzD,KAAK,CAACgB,KAAK,CAACvD,YAAY;EACjD,MAAMiG,QAAQ,GAAG1D,KAAK,CAACkB,GAAG,CAACxD,MAAM;EACjC,MAAMiG,cAAc,GAAG3D,KAAK,CAACkB,GAAG,CAACzD,YAAY;EAC7C;EACA,IAAImG,IAAI;EAER,IAAIJ,UAAU,KAAKE,QAAQ,EAAE;IAC3B9G,MAAM,CAAC+G,cAAc,GAAG,CAAC,CAAC,EAAE,wCAAwC,CAAC;IACrE/G,MAAM,CAAC6G,gBAAgB,GAAG,CAAC,CAAC,EAAE,0CAA0C,CAAC;IACzE;IACAG,IAAI,GAAG,CAAC5F,MAAM,CAACwF,UAAU,CAAC,CAAC5D,KAAK,CAAC6D,gBAAgB,EAAEE,cAAc,CAAC,CAAC;EACrE,CAAC,MAAM;IACLC,IAAI,GAAG5F,MAAM,CAAC4B,KAAK,CAAC4D,UAAU,EAAEE,QAAQ,CAAC;IAEzC,IAAID,gBAAgB,GAAG,CAAC,CAAC,EAAE;MACzB,MAAMI,IAAI,GAAGD,IAAI,CAAC,CAAC,CAAC;MACpB,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;QAC5BD,IAAI,CAAC,CAAC,CAAC,GAAGC,IAAI,CAACjE,KAAK,CAAC6D,gBAAgB,CAAC;QACtC;MACF,CAAC,MAAM;QACL7G,MAAM,CAAC6G,gBAAgB,KAAK,CAAC,EAAE,uCAAuC,CAAC;QACvEG,IAAI,CAACE,KAAK,CAAC,CAAC;MACd;IACF;IAEA,IAAIH,cAAc,GAAG,CAAC,EAAE;MACtB;MACAC,IAAI,CAAC9G,IAAI,CAACkB,MAAM,CAAC0F,QAAQ,CAAC,CAAC9D,KAAK,CAAC,CAAC,EAAE+D,cAAc,CAAC,CAAC;IACtD;EACF;EAEA,OAAOC,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1D,eAAeA,CAAClC,MAAM,EAAEiC,UAAU,EAAE;EAC3C,IAAI8D,KAAK,GAAG,CAAC,CAAC;EACd;EACA,MAAMC,MAAM,GAAG,EAAE;EACjB;EACA,IAAIC,KAAK;EAET,OAAO,EAAEF,KAAK,GAAG/F,MAAM,CAAC8B,MAAM,EAAE;IAC9B,MAAMS,KAAK,GAAGvC,MAAM,CAAC+F,KAAK,CAAC;IAC3B;IACA,IAAI3D,KAAK;IAET,IAAI,OAAOG,KAAK,KAAK,QAAQ,EAAE;MAC7BH,KAAK,GAAGG,KAAK;IACf,CAAC,MACC,QAAQA,KAAK;MACX,KAAKtD,KAAK,CAACiH,cAAc;QAAE;UACzB9D,KAAK,GAAGlD,MAAM,CAACiH,EAAE;UAEjB;QACF;MAEA,KAAKlH,KAAK,CAACmH,QAAQ;QAAE;UACnBhE,KAAK,GAAGlD,MAAM,CAACmH,EAAE;UAEjB;QACF;MAEA,KAAKpH,KAAK,CAAC2D,sBAAsB;QAAE;UACjCR,KAAK,GAAGlD,MAAM,CAACiH,EAAE,GAAGjH,MAAM,CAACmH,EAAE;UAE7B;QACF;MAEA,KAAKpH,KAAK,CAACqH,aAAa;QAAE;UACxBlE,KAAK,GAAGH,UAAU,GAAG/C,MAAM,CAACqH,KAAK,GAAGrH,MAAM,CAACsH,EAAE;UAE7C;QACF;MAEA,KAAKvH,KAAK,CAAC4D,YAAY;QAAE;UACvB,IAAI,CAACZ,UAAU,IAAIgE,KAAK,EAAE;UAC1B7D,KAAK,GAAGlD,MAAM,CAACqH,KAAK;UAEpB;QACF;MAEA;QAAS;UACP3H,MAAM,CAAC,OAAO2D,KAAK,KAAK,QAAQ,EAAE,iBAAiB,CAAC;UACpD;UACAH,KAAK,GAAGqE,MAAM,CAACC,YAAY,CAACnE,KAAK,CAAC;QACpC;IACF;IAEF0D,KAAK,GAAG1D,KAAK,KAAKtD,KAAK,CAACqH,aAAa;IACrCN,MAAM,CAAClH,IAAI,CAACsD,KAAK,CAAC;EACpB;EAEA,OAAO4D,MAAM,CAACW,IAAI,CAAC,EAAE,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}