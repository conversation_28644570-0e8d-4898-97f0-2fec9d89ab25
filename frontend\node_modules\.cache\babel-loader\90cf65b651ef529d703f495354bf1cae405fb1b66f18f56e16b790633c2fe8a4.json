{"ast": null, "code": "'use strict';\n\n// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};", "map": {"version": 3, "names": ["IndexedObject", "require", "requireObjectCoercible", "module", "exports", "it"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/core-js-pure/internals/to-indexed-object.js"], "sourcesContent": ["'use strict';\n// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n"], "mappings": "AAAA,YAAY;;AACZ;AACA,IAAIA,aAAa,GAAGC,OAAO,CAAC,6BAA6B,CAAC;AAC1D,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,uCAAuC,CAAC;AAE7EE,MAAM,CAACC,OAAO,GAAG,UAAUC,EAAE,EAAE;EAC7B,OAAOL,aAAa,CAACE,sBAAsB,CAACG,EAAE,CAAC,CAAC;AAClD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}