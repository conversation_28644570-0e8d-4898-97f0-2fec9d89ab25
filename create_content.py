#!/usr/bin/env python3
"""
Create test content using existing editor user
"""

import requests

BACKEND_URL = "http://localhost:8000"

def login_editor():
    """Login as editor user"""
    login_data = {
        "username": "editor_user",
        "password": "editor123"
    }
    
    response = requests.post(f"{BACKEND_URL}/auth/login", json=login_data)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"❌ Login failed: {response.text}")
        return None

def create_categories(token):
    """Create test categories"""
    print("🔍 Creating categories...")
    
    categories = [
        {"name": "Technology", "description": "Technology and programming articles"},
        {"name": "Documentation", "description": "Technical documentation"},
        {"name": "Tutorials", "description": "Step-by-step guides"},
        {"name": "Best Practices", "description": "Industry best practices"}
    ]
    
    headers = {"Authorization": f"Bearer {token}"}
    created = []
    
    for cat in categories:
        response = requests.post(f"{BACKEND_URL}/categories/", json=cat, headers=headers)
        if response.status_code == 200:
            created.append(response.json())
            print(f"✅ Created category: {cat['name']}")
        else:
            print(f"❌ Failed to create category: {response.text}")
    
    return created

def create_tags(token):
    """Create test tags"""
    print("🔍 Creating tags...")
    
    tags = [
        {"name": "python", "color": "#3776ab"},
        {"name": "javascript", "color": "#f7df1e"},
        {"name": "react", "color": "#61dafb"},
        {"name": "fastapi", "color": "#009688"},
        {"name": "tutorial", "color": "#ff9800"}
    ]
    
    headers = {"Authorization": f"Bearer {token}"}
    created = []
    
    for tag in tags:
        response = requests.post(f"{BACKEND_URL}/tags/", json=tag, headers=headers)
        if response.status_code == 200:
            created.append(response.json())
            print(f"✅ Created tag: {tag['name']}")
        else:
            print(f"❌ Failed to create tag: {response.text}")
    
    return created

def create_articles(token, categories, tags):
    """Create test articles"""
    print("🔍 Creating articles...")
    
    tech_cat = next((c for c in categories if c['name'] == 'Technology'), None)
    python_tag = next((t for t in tags if t['name'] == 'python'), None)
    fastapi_tag = next((t for t in tags if t['name'] == 'fastapi'), None)
    tutorial_tag = next((t for t in tags if t['name'] == 'tutorial'), None)
    
    articles = [
        {
            "title": "Getting Started with FastAPI",
            "content": """# Getting Started with FastAPI

FastAPI is a modern, fast web framework for building APIs with Python 3.7+.

## Key Features

- **Fast**: Very high performance
- **Easy**: Designed to be easy to use and learn
- **Robust**: Get production-ready code with automatic interactive documentation

## Installation

```bash
pip install fastapi uvicorn
```

## Basic Example

```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}
```

## Running

```bash
uvicorn main:app --reload
```

Visit http://127.0.0.1:8000 to see your API!""",
            "summary": "Learn how to get started with FastAPI, a modern Python web framework",
            "category_id": tech_cat['id'] if tech_cat else None,
            "tag_ids": [t['id'] for t in [python_tag, fastapi_tag, tutorial_tag] if t],
            "is_published": True,
            "is_featured": True
        },
        {
            "title": "React Hooks Fundamentals",
            "content": """# React Hooks Fundamentals

React Hooks allow you to use state and other React features in functional components.

## useState Hook

```javascript
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
```

## useEffect Hook

```javascript
import React, { useState, useEffect } from 'react';

function Example() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    document.title = `You clicked ${count} times`;
  });

  return (
    <div>
      <p>You clicked {count} times</p>
      <button onClick={() => setCount(count + 1)}>
        Click me
      </button>
    </div>
  );
}
```

Hooks make React components more powerful and easier to understand!""",
            "summary": "Understanding React Hooks and how to use them effectively",
            "category_id": tech_cat['id'] if tech_cat else None,
            "tag_ids": [t['id'] for t in [next((t for t in tags if t['name'] == 'react'), None), next((t for t in tags if t['name'] == 'javascript'), None), tutorial_tag] if t],
            "is_published": True,
            "is_featured": False
        }
    ]
    
    headers = {"Authorization": f"Bearer {token}"}
    created = []
    
    for article in articles:
        response = requests.post(f"{BACKEND_URL}/articles/", json=article, headers=headers)
        if response.status_code == 200:
            created.append(response.json())
            print(f"✅ Created article: {article['title']}")
        else:
            print(f"❌ Failed to create article: {response.text}")
    
    return created

def main():
    print("🚀 Creating test content...")
    
    # Login as editor
    token = login_editor()
    if not token:
        return
    
    # Create content
    categories = create_categories(token)
    tags = create_tags(token)
    articles = create_articles(token, categories, tags)
    
    print(f"\n🎉 Created {len(categories)} categories, {len(tags)} tags, and {len(articles)} articles!")
    print("\n🌐 You can now test the application:")
    print("   Frontend: http://localhost:3000")
    print("   Backend API: http://localhost:8000/docs")
    print("\n🔑 Test credentials:")
    print("   Editor: editor_user / editor123")
    print("   Viewer: viewer_user / viewer123")
    print("   Admin: admin_user / admin123")

if __name__ == "__main__":
    main()
