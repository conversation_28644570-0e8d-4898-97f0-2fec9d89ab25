{"ast": null, "code": "// Note: types exposed from `index.d.ts`.\nimport { merge } from './lib/util/merge.js';\nimport { aria } from './lib/aria.js';\nimport { html as htmlBase } from './lib/html.js';\nimport { svg as svgBase } from './lib/svg.js';\nimport { xlink } from './lib/xlink.js';\nimport { xmlns } from './lib/xmlns.js';\nimport { xml } from './lib/xml.js';\nexport { hastToReact } from './lib/hast-to-react.js';\nexport const html = merge([aria, htmlBase, xlink, xmlns, xml], 'html');\nexport { find } from './lib/find.js';\nexport { normalize } from './lib/normalize.js';\nexport const svg = merge([aria, svgBase, xlink, xmlns, xml], 'svg');", "map": {"version": 3, "names": ["merge", "aria", "html", "htmlBase", "svg", "svgBase", "xlink", "xmlns", "xml", "hastToReact", "find", "normalize"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/property-information/index.js"], "sourcesContent": ["// Note: types exposed from `index.d.ts`.\nimport {merge} from './lib/util/merge.js'\nimport {aria} from './lib/aria.js'\nimport {html as htmlBase} from './lib/html.js'\nimport {svg as svgBase} from './lib/svg.js'\nimport {xlink} from './lib/xlink.js'\nimport {xmlns} from './lib/xmlns.js'\nimport {xml} from './lib/xml.js'\n\nexport {hastToReact} from './lib/hast-to-react.js'\n\nexport const html = merge([aria, htmlBase, xlink, xmlns, xml], 'html')\n\nexport {find} from './lib/find.js'\nexport {normalize} from './lib/normalize.js'\n\nexport const svg = merge([aria, svgBase, xlink, xmlns, xml], 'svg')\n"], "mappings": "AAAA;AACA,SAAQA,KAAK,QAAO,qBAAqB;AACzC,SAAQC,IAAI,QAAO,eAAe;AAClC,SAAQC,IAAI,IAAIC,QAAQ,QAAO,eAAe;AAC9C,SAAQC,GAAG,IAAIC,OAAO,QAAO,cAAc;AAC3C,SAAQC,KAAK,QAAO,gBAAgB;AACpC,SAAQC,KAAK,QAAO,gBAAgB;AACpC,SAAQC,GAAG,QAAO,cAAc;AAEhC,SAAQC,WAAW,QAAO,wBAAwB;AAElD,OAAO,MAAMP,IAAI,GAAGF,KAAK,CAAC,CAACC,IAAI,EAAEE,QAAQ,EAAEG,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC,EAAE,MAAM,CAAC;AAEtE,SAAQE,IAAI,QAAO,eAAe;AAClC,SAAQC,SAAS,QAAO,oBAAoB;AAE5C,OAAO,MAAMP,GAAG,GAAGJ,KAAK,CAAC,CAACC,IAAI,EAAEI,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,CAAC,EAAE,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}