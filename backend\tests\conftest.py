import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.database import get_db, Base
from app import models, crud, auth

# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="function")
def db():
    Base.metadata.create_all(bind=engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def client():
    with TestClient(app) as c:
        yield c

@pytest.fixture
def test_user(db):
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User",
        "password": "testpassword"
    }
    user = crud.create_user(db, crud.schemas.UserCreate(**user_data))
    return user

@pytest.fixture
def test_admin(db):
    user_data = {
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "Admin User",
        "password": "adminpassword"
    }
    user = crud.create_user(db, crud.schemas.UserCreate(**user_data))
    user.role = "admin"
    db.commit()
    db.refresh(user)
    return user

@pytest.fixture
def auth_headers(client, test_user):
    login_data = {
        "username": test_user.username,
        "password": "testpassword"
    }
    response = client.post("/auth/login", json=login_data)
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}

@pytest.fixture
def admin_headers(client, test_admin):
    login_data = {
        "username": test_admin.username,
        "password": "adminpassword"
    }
    response = client.post("/auth/login", json=login_data)
    token = response.json()["access_token"]
    return {"Authorization": f"Bearer {token}"}
