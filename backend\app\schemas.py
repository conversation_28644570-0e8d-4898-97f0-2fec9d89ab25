from pydantic import BaseModel, EmailStr, ConfigDict
from typing import List, Optional
from datetime import datetime

# User schemas
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    role: Optional[str] = None

class User(UserBase):
    id: int
    role: str
    is_active: bool
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Category schemas
class CategoryBase(BaseModel):
    name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None

class CategoryCreate(CategoryBase):
    pass

class Category(CategoryBase):
    id: int
    created_at: datetime
    children: List['Category'] = []

    model_config = ConfigDict(from_attributes=True)

# Tag schemas
class TagBase(BaseModel):
    name: str
    color: Optional[str] = "#3B82F6"

class TagCreate(TagBase):
    pass

class Tag(TagBase):
    id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Article schemas
class ArticleBase(BaseModel):
    title: str
    content: str
    summary: Optional[str] = None
    category_id: Optional[int] = None
    is_published: bool = False
    is_featured: bool = False

class ArticleCreate(ArticleBase):
    tag_ids: List[int] = []

class ArticleUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    summary: Optional[str] = None
    category_id: Optional[int] = None
    is_published: Optional[bool] = None
    is_featured: Optional[bool] = None
    tag_ids: Optional[List[int]] = None

class Article(ArticleBase):
    id: int
    author_id: int
    view_count: int
    created_at: datetime
    updated_at: Optional[datetime]
    author: User
    category: Optional[Category]
    tags: List[Tag] = []

    model_config = ConfigDict(from_attributes=True)

# Comment schemas
class CommentBase(BaseModel):
    content: str
    parent_id: Optional[int] = None

class CommentCreate(CommentBase):
    article_id: int

class Comment(CommentBase):
    id: int
    article_id: int
    author_id: int
    created_at: datetime
    updated_at: Optional[datetime]
    author: User
    replies: List['Comment'] = []

    model_config = ConfigDict(from_attributes=True)

# Authentication schemas
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None

class UserLogin(BaseModel):
    username: str
    password: str

# Search schemas
class SearchResult(BaseModel):
    articles: List[Article]
    total: int
    page: int
    per_page: int

# Article Version schemas
class ArticleVersion(BaseModel):
    id: int
    article_id: int
    title: str
    content: str
    version_number: int
    created_at: datetime
    created_by: int
    creator: User

    model_config = ConfigDict(from_attributes=True)

# Update forward references
Category.model_rebuild()
Comment.model_rebuild()
