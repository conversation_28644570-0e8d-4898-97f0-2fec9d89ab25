{"ast": null, "code": "/// <reference lib=\"dom\" />\n\n/* global document */\n\nconst element = document.createElement('i');\n\n/**\n * @param {string} value\n * @returns {string | false}\n */\nexport function decodeNamedCharacterReference(value) {\n  const characterReference = '&' + value + ';';\n  element.innerHTML = characterReference;\n  const character = element.textContent;\n\n  // Some named character references do not require the closing semicolon\n  // (`&not`, for instance), which leads to situations where parsing the assumed\n  // named reference of `&notit;` will result in the string `¬it;`.\n  // When we encounter a trailing semicolon after parsing, and the character\n  // reference to decode was not a semicolon (`&semi;`), we can assume that the\n  // matching was not complete.\n  if (\n  // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n  // yield `null`.\n  character.charCodeAt(character.length - 1) === 59 /* `;` */ && value !== 'semi') {\n    return false;\n  }\n\n  // If the decoded string is equal to the input, the character reference was\n  // not valid.\n  // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n  // yield `null`.\n  return character === characterReference ? false : character;\n}", "map": {"version": 3, "names": ["element", "document", "createElement", "decodeNamedCharacterReference", "value", "characterReference", "innerHTML", "character", "textContent", "charCodeAt", "length"], "sources": ["C:/Users/<USER>/Desktop/x/frontend/node_modules/decode-named-character-reference/index.dom.js"], "sourcesContent": ["/// <reference lib=\"dom\" />\n\n/* global document */\n\nconst element = document.createElement('i')\n\n/**\n * @param {string} value\n * @returns {string | false}\n */\nexport function decodeNamedCharacterReference(value) {\n  const characterReference = '&' + value + ';'\n  element.innerHTML = characterReference\n  const character = element.textContent\n\n  // Some named character references do not require the closing semicolon\n  // (`&not`, for instance), which leads to situations where parsing the assumed\n  // named reference of `&notit;` will result in the string `¬it;`.\n  // When we encounter a trailing semicolon after parsing, and the character\n  // reference to decode was not a semicolon (`&semi;`), we can assume that the\n  // matching was not complete.\n  if (\n    // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n    // yield `null`.\n    character.charCodeAt(character.length - 1) === 59 /* `;` */ &&\n    value !== 'semi'\n  ) {\n    return false\n  }\n\n  // If the decoded string is equal to the input, the character reference was\n  // not valid.\n  // @ts-expect-error: TypeScript is wrong that `textContent` on elements can\n  // yield `null`.\n  return character === characterReference ? false : character\n}\n"], "mappings": "AAAA;;AAEA;;AAEA,MAAMA,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;;AAE3C;AACA;AACA;AACA;AACA,OAAO,SAASC,6BAA6BA,CAACC,KAAK,EAAE;EACnD,MAAMC,kBAAkB,GAAG,GAAG,GAAGD,KAAK,GAAG,GAAG;EAC5CJ,OAAO,CAACM,SAAS,GAAGD,kBAAkB;EACtC,MAAME,SAAS,GAAGP,OAAO,CAACQ,WAAW;;EAErC;EACA;EACA;EACA;EACA;EACA;EACA;EACE;EACA;EACAD,SAAS,CAACE,UAAU,CAACF,SAAS,CAACG,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC,aAClDN,KAAK,KAAK,MAAM,EAChB;IACA,OAAO,KAAK;EACd;;EAEA;EACA;EACA;EACA;EACA,OAAOG,SAAS,KAAKF,kBAAkB,GAAG,KAAK,GAAGE,SAAS;AAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}